{"version": 3, "file": "DeploymentDuplicate-C_VmqbOW.js", "sources": ["../../src/pages/DeploymentDuplicate.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"deployment\" class=\"deployment-edit\">\n    <template #header>\n      <PageHeadingDeploymentDuplicate :deployment=\"deployment\" />\n    </template>\n\n    <DeploymentForm :deployment=\"deployment\" mode=\"duplicate\" @cancel=\"cancel\" @submit=\"submit\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { PageHeadingDeploymentDuplicate, useWorkspaceApi, getApiErrorMessage, DeploymentForm, DeploymentCreate, DeploymentUpdateV2 } from '@prefecthq/prefect-ui-library'\n  import { useSubscription, useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import router, { routes } from '@/router'\n\n  const api = useWorkspaceApi()\n  const deploymentId = useRouteParam('deploymentId')\n\n  const deploymentSubscription = useSubscription(api.deployments.getDeployment, [deploymentId.value], {})\n  const deployment = computed(() => deploymentSubscription.response)\n\n  function isDeploymentCreate(request: DeploymentCreate | DeploymentUpdateV2): request is DeploymentCreate {\n    return 'name' in request\n  }\n\n  async function submit(request: DeploymentCreate | DeploymentUpdateV2): Promise<void> {\n    try {\n      if (!isDeploymentCreate(request)) {\n        throw new Error('Invalid request')\n      }\n      const newDeployment = await api.deployments.createDeployment(request)\n      showToast('Deployment created', 'success')\n      router.push(routes.deployment(newDeployment.id))\n    } catch (error) {\n      const message = getApiErrorMessage(error, 'Error creating deployment')\n      showToast(message, 'error')\n      console.warn(error)\n    }\n  }\n\n  function cancel(): void {\n    router.back()\n  }\n\n  const title = computed(() => {\n    if (!deployment.value) {\n      return 'Duplicate Deployment'\n    }\n    return `Duplicate Deployment: ${deployment.value.name}`\n  })\n  usePageTitle(title)\n</script>\n\n"], "names": ["api", "useWorkspaceApi", "deploymentId", "useRouteParam", "deploymentSubscription", "useSubscription", "deployment", "computed", "isDeploymentCreate", "request", "submit", "newDeployment", "showToast", "router", "routes", "error", "message", "getApiErrorMessage", "cancel", "title", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingDeploymentDuplicate", "DeploymentForm"], "mappings": "8PAkBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAE3CC,EAAyBC,EAAgBL,EAAI,YAAY,cAAe,CAACE,EAAa,KAAK,EAAG,EAAE,EAChGI,EAAaC,EAAS,IAAMH,EAAuB,QAAQ,EAEjE,SAASI,EAAmBC,EAA6E,CACvG,MAAO,SAAUA,CAAA,CAGnB,eAAeC,EAAOD,EAA+D,CAC/E,GAAA,CACE,GAAA,CAACD,EAAmBC,CAAO,EACvB,MAAA,IAAI,MAAM,iBAAiB,EAEnC,MAAME,EAAgB,MAAMX,EAAI,YAAY,iBAAiBS,CAAO,EACpEG,EAAU,qBAAsB,SAAS,EACzCC,EAAO,KAAKC,EAAO,WAAWH,EAAc,EAAE,CAAC,QACxCI,EAAO,CACR,MAAAC,EAAUC,EAAmBF,EAAO,2BAA2B,EACrEH,EAAUI,EAAS,OAAO,EAC1B,QAAQ,KAAKD,CAAK,CAAA,CACpB,CAGF,SAASG,GAAe,CACtBL,EAAO,KAAK,CAAA,CAGR,MAAAM,EAAQZ,EAAS,IAChBD,EAAW,MAGT,yBAAyBA,EAAW,MAAM,IAAI,GAF5C,sBAGV,EACD,OAAAc,EAAaD,CAAK,+CApDMb,EAAU,WAAlCe,EAMmBC,EAAA,OANiB,MAAM,iBAAA,GAC7B,SACT,IAA2D,CAA3DC,EAA2DC,EAAAC,CAAA,EAAA,CAA1B,WAAYnB,EAAU,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,aAGzD,IAA8F,CAA9FiB,EAA8FC,EAAAE,CAAA,EAAA,CAA7E,WAAYpB,EAAU,MAAE,KAAK,YAAa,SAAQY,EAAS,SAAQR,CAAA"}