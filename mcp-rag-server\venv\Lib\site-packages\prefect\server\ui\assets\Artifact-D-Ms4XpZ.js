import{d as S,e as D,W as N,f as P,g as p,ae as R,al as T,I as _,aG as V,i as n,c,o as l,j as e,q as $,a as i,k as s,n as t,aH as h,aI as H,aJ as d,B as W,t as Z,aK as v,a6 as j,aL as m,aM as q}from"./index-ei-kaitd.js";import{u as E}from"./usePageTitle-LeBMnqrg.js";const F={key:0},M=S({__name:"Artifact",setup(G){const b=D(),k=N("artifactId"),w=P(b.artifacts.getArtifact,[k]),a=p(()=>w.response),r=R(!1),y=[{label:"Artifact"},{label:"Details"},{label:"Raw"}],u=T("tab","Artifact"),C=p(()=>a.value?`${_.info.artifact}: ${a.value.key??V(a.value.type)}`:_.info.artifact);return E(C),(J,o)=>{const g=n("p-divider"),x=n("p-button"),I=n("p-content"),A=n("p-tabs"),B=n("p-layout-well");return l(),c(B,{class:"artifact"},{header:e(()=>[a.value?(l(),c(t(q),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),well:e(()=>[a.value?(l(),c(t(m),{key:0,artifact:a.value,alternate:""},null,8,["artifact"])):i("",!0)]),default:e(()=>[a.value?(l(),$("section",F,[s(t(h),{artifact:a.value},null,8,["artifact"]),s(g),t(H).xl?(l(),c(I,{key:0},{default:e(()=>[s(t(d),{artifact:a.value},null,8,["artifact"]),s(x,{class:"artifact__raw-data-button",small:"",onClick:o[0]||(o[0]=f=>r.value=!r.value)},{default:e(()=>[W(Z(r.value?"Hide":"Show")+" raw data ",1)]),_:1}),r.value?(l(),c(t(v),{key:0,artifact:a.value},null,8,["artifact"])):i("",!0)]),_:1})):(l(),c(A,{key:1,selected:t(u),"onUpdate:selected":o[1]||(o[1]=f=>j(u)?u.value=f:null),tabs:y},{artifact:e(()=>[s(t(d),{artifact:a.value},null,8,["artifact"])]),details:e(()=>[s(t(m),{artifact:a.value},null,8,["artifact"])]),raw:e(()=>[s(t(v),{artifact:a.value},null,8,["artifact"])]),_:1},8,["selected"]))])):i("",!0)]),_:1})}}});export{M as default};
//# sourceMappingURL=Artifact-D-Ms4XpZ.js.map
