"""Crawl session repository interface for the MCP RAG Server."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..entities import CrawlSession, CrawlStatus, CrawlStrategy


class CrawlSessionRepository(ABC):
    """Abstract crawl session repository interface."""
    
    @abstractmethod
    async def create(self, session: CrawlSession) -> CrawlSession:
        """Create a new crawl session."""
        pass
    
    @abstractmethod
    async def get_by_id(self, session_id: str) -> Optional[CrawlSession]:
        """Get crawl session by ID."""
        pass
    
    @abstractmethod
    async def update(self, session: CrawlSession) -> CrawlSession:
        """Update an existing crawl session."""
        pass
    
    @abstractmethod
    async def delete(self, session_id: str) -> bool:
        """Delete a crawl session."""
        pass
    
    @abstractmethod
    async def list_sessions(
        self,
        limit: int = 100,
        offset: int = 0,
        status: Optional[CrawlStatus] = None,
        strategy: Optional[CrawlStrategy] = None,
        created_by: Optional[str] = None,
    ) -> List[CrawlSession]:
        """List crawl sessions with optional filters."""
        pass
    
    @abstractmethod
    async def count_sessions(
        self,
        status: Optional[CrawlStatus] = None,
        strategy: Optional[CrawlStrategy] = None,
        created_by: Optional[str] = None,
    ) -> int:
        """Count crawl sessions with optional filters."""
        pass
    
    @abstractmethod
    async def get_sessions_by_status(self, status: CrawlStatus) -> List[CrawlSession]:
        """Get all sessions with specific status."""
        pass
    
    @abstractmethod
    async def get_active_sessions(self) -> List[CrawlSession]:
        """Get all active (running or paused) sessions."""
        pass
    
    @abstractmethod
    async def get_sessions_by_user(self, user_id: str) -> List[CrawlSession]:
        """Get all sessions created by a specific user."""
        pass
    
    @abstractmethod
    async def get_sessions_created_after(self, date: datetime) -> List[CrawlSession]:
        """Get sessions created after a specific date."""
        pass
    
    @abstractmethod
    async def get_sessions_updated_after(self, date: datetime) -> List[CrawlSession]:
        """Get sessions updated after a specific date."""
        pass
    
    @abstractmethod
    async def search_sessions(
        self,
        query: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[CrawlSession]:
        """Search sessions by name or description."""
        pass
    
    @abstractmethod
    async def update_session_status(
        self,
        session_id: str,
        status: CrawlStatus,
        error_message: Optional[str] = None,
    ) -> bool:
        """Update session status."""
        pass
    
    @abstractmethod
    async def update_session_progress(
        self,
        session_id: str,
        urls_crawled: int,
        urls_failed: int,
        current_depth: int,
    ) -> bool:
        """Update session progress."""
        pass
    
    @abstractmethod
    async def add_url_to_session(
        self,
        session_id: str,
        url: str,
        url_type: str = "to_crawl",  # "to_crawl", "crawled", "failed", "skipped"
    ) -> bool:
        """Add URL to session."""
        pass
    
    @abstractmethod
    async def remove_url_from_session(
        self,
        session_id: str,
        url: str,
        url_type: str = "to_crawl",
    ) -> bool:
        """Remove URL from session."""
        pass
    
    @abstractmethod
    async def get_session_urls(
        self,
        session_id: str,
        url_type: str = "to_crawl",
        limit: int = 100,
    ) -> List[str]:
        """Get URLs from session by type."""
        pass
    
    @abstractmethod
    async def get_next_url_to_crawl(self, session_id: str) -> Optional[str]:
        """Get next URL to crawl from session."""
        pass
    
    @abstractmethod
    async def mark_url_crawled(
        self,
        session_id: str,
        url: str,
        size_bytes: int = 0,
        response_time: float = 0.0,
    ) -> bool:
        """Mark URL as crawled in session."""
        pass
    
    @abstractmethod
    async def mark_url_failed(
        self,
        session_id: str,
        url: str,
        error_type: str = "unknown",
    ) -> bool:
        """Mark URL as failed in session."""
        pass
    
    @abstractmethod
    async def get_session_statistics(self, session_id: str) -> Dict[str, Any]:
        """Get detailed statistics for a session."""
        pass
    
    @abstractmethod
    async def get_global_statistics(self) -> Dict[str, Any]:
        """Get global crawl session statistics."""
        pass
    
    @abstractmethod
    async def cleanup_old_sessions(
        self,
        older_than: datetime,
        status: Optional[CrawlStatus] = None,
    ) -> int:
        """Clean up old sessions."""
        pass
    
    @abstractmethod
    async def pause_all_active_sessions(self) -> int:
        """Pause all active sessions."""
        pass
    
    @abstractmethod
    async def resume_paused_sessions(self, session_ids: List[str]) -> int:
        """Resume specific paused sessions."""
        pass
