#!/usr/bin/env python3
"""Comprehensive RAG Server Performance Testing Suite."""

import asyncio
import aiohttp
import time
import statistics
import json
from datetime import datetime
from typing import List, Dict, Any, Tuple
from concurrent.futures import ThreadPoolExecutor
import threading

from crawlers.session_manager import CrawlSessionManager
from core.application.services import (
    DocumentService, ChunkService, EmbeddingService
)
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.domain.entities import CrawlStrategy, DocumentType
from config.settings import get_settings


class RAGPerformanceTester:
    """Comprehensive RAG server performance tester."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.results = {
            "api_tests": [],
            "crawling_tests": [],
            "embedding_tests": [],
            "search_tests": [],
            "load_tests": [],
            "summary": {}
        }
    
    async def test_api_latency(self, num_requests: int = 50) -> Dict[str, Any]:
        """Test API endpoint latency."""
        print(f'🌐 Testing API Latency ({num_requests} requests)')
        
        endpoints = [
            "/health/",
            "/api/search/test",
            "/api/search/simple?q=test&max_results=5"
        ]
        
        results = {}
        
        async with aiohttp.ClientSession() as session:
            for endpoint in endpoints:
                print(f'   Testing {endpoint}...')
                latencies = []
                errors = 0
                
                for i in range(num_requests):
                    start_time = time.time()
                    try:
                        async with session.get(f"{self.base_url}{endpoint}") as response:
                            await response.text()
                            latency = (time.time() - start_time) * 1000  # ms
                            latencies.append(latency)
                            
                            if response.status != 200:
                                errors += 1
                                
                    except Exception as e:
                        errors += 1
                        print(f'     Error in request {i+1}: {e}')
                
                if latencies:
                    results[endpoint] = {
                        "avg_latency_ms": statistics.mean(latencies),
                        "min_latency_ms": min(latencies),
                        "max_latency_ms": max(latencies),
                        "p95_latency_ms": statistics.quantiles(latencies, n=20)[18] if len(latencies) > 20 else max(latencies),
                        "p99_latency_ms": statistics.quantiles(latencies, n=100)[98] if len(latencies) > 100 else max(latencies),
                        "success_rate": (num_requests - errors) / num_requests * 100,
                        "total_requests": num_requests,
                        "errors": errors
                    }
                    
                    print(f'     ✅ Avg: {results[endpoint]["avg_latency_ms"]:.2f}ms, '
                          f'P95: {results[endpoint]["p95_latency_ms"]:.2f}ms, '
                          f'Success: {results[endpoint]["success_rate"]:.1f}%')
                else:
                    results[endpoint] = {"error": "All requests failed"}
                    print(f'     ❌ All requests failed')
        
        self.results["api_tests"] = results
        return results
    
    async def test_concurrent_load(self, concurrent_users: int = 10, requests_per_user: int = 20) -> Dict[str, Any]:
        """Test concurrent load on the server."""
        print(f'⚡ Testing Concurrent Load ({concurrent_users} users, {requests_per_user} req/user)')
        
        async def user_session(user_id: int) -> List[float]:
            """Simulate a user session."""
            latencies = []
            async with aiohttp.ClientSession() as session:
                for i in range(requests_per_user):
                    start_time = time.time()
                    try:
                        # Mix of different endpoints
                        endpoints = [
                            "/health/",
                            "/api/search/test",
                            f"/api/search/simple?q=user{user_id}_query{i}&max_results=5"
                        ]
                        endpoint = endpoints[i % len(endpoints)]
                        
                        async with session.get(f"{self.base_url}{endpoint}") as response:
                            await response.text()
                            latency = (time.time() - start_time) * 1000
                            latencies.append(latency)
                            
                    except Exception as e:
                        print(f'     User {user_id} error: {e}')
            
            return latencies
        
        # Run concurrent users
        start_time = time.time()
        tasks = [user_session(i) for i in range(concurrent_users)]
        all_latencies = await asyncio.gather(*tasks)
        total_time = time.time() - start_time
        
        # Flatten all latencies
        flat_latencies = [lat for user_lats in all_latencies for lat in user_lats]
        total_requests = len(flat_latencies)
        
        if flat_latencies:
            results = {
                "concurrent_users": concurrent_users,
                "requests_per_user": requests_per_user,
                "total_requests": total_requests,
                "total_time_seconds": total_time,
                "throughput_rps": total_requests / total_time,
                "avg_latency_ms": statistics.mean(flat_latencies),
                "min_latency_ms": min(flat_latencies),
                "max_latency_ms": max(flat_latencies),
                "p95_latency_ms": statistics.quantiles(flat_latencies, n=20)[18] if len(flat_latencies) > 20 else max(flat_latencies),
                "p99_latency_ms": statistics.quantiles(flat_latencies, n=100)[98] if len(flat_latencies) > 100 else max(flat_latencies),
            }
            
            print(f'   ✅ Throughput: {results["throughput_rps"]:.2f} RPS')
            print(f'   ✅ Avg Latency: {results["avg_latency_ms"]:.2f}ms')
            print(f'   ✅ P95 Latency: {results["p95_latency_ms"]:.2f}ms')
        else:
            results = {"error": "No successful requests"}
            print(f'   ❌ Load test failed')
        
        self.results["load_tests"] = results
        return results
    
    async def test_crawling_performance(self) -> Dict[str, Any]:
        """Test crawling system performance."""
        print('🕷️  Testing Crawling Performance')
        
        # Setup
        settings = get_settings()
        doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
        doc_service = DocumentService(doc_repository)
        
        session_manager = CrawlSessionManager(
            document_service=doc_service,
            max_concurrent_sessions=1,
            max_pages_per_session=5,
            max_depth=2,
        )
        
        # Test crawling performance
        start_time = time.time()
        
        session = await session_manager.create_session(
            name="Performance Test Session",
            start_urls=["https://example.com"],
            strategy=CrawlStrategy.BREADTH_FIRST,
            description="Performance testing crawl session",
        )
        
        session_creation_time = time.time() - start_time
        
        # Start crawling
        crawl_start_time = time.time()
        started = await session_manager.start_session(session.id)
        
        if started:
            # Wait for completion
            for i in range(30):  # Wait up to 30 seconds
                await asyncio.sleep(1)
                current_session = session_manager.get_session(session.id)
                if current_session and current_session.status.value in ['completed', 'failed']:
                    break
            
            crawl_total_time = time.time() - crawl_start_time
            
            # Get statistics
            stats = session_manager.get_session_statistics(session.id)
            
            results = {
                "session_creation_time_ms": session_creation_time * 1000,
                "crawl_total_time_seconds": crawl_total_time,
                "pages_crawled": stats["urls_crawled"] if stats else 0,
                "pages_failed": stats["urls_failed"] if stats else 0,
                "pages_per_second": (stats["urls_crawled"] / crawl_total_time) if stats and crawl_total_time > 0 else 0,
                "success_rate": ((stats["urls_crawled"] / max(1, stats["urls_crawled"] + stats["urls_failed"])) * 100) if stats else 0,
                "status": stats["status"] if stats else "unknown"
            }
            
            print(f'   ✅ Pages crawled: {results["pages_crawled"]}')
            print(f'   ✅ Crawl time: {results["crawl_total_time_seconds"]:.2f}s')
            print(f'   ✅ Pages/sec: {results["pages_per_second"]:.2f}')
            print(f'   ✅ Success rate: {results["success_rate"]:.1f}%')
        else:
            results = {"error": "Failed to start crawl session"}
            print('   ❌ Failed to start crawling')
        
        self.results["crawling_tests"] = results
        return results
    
    async def test_embedding_performance(self) -> Dict[str, Any]:
        """Test embedding generation performance."""
        print('🤖 Testing Embedding Performance')
        
        # Mock chunk repository for testing
        class MockChunkRepository:
            def __init__(self):
                self.chunks = {}
                self.next_id = 1
            
            async def create(self, chunk):
                chunk.id = str(self.next_id)
                self.next_id += 1
                self.chunks[chunk.id] = chunk
                return chunk
            
            async def delete_chunks_by_document(self, document_id):
                to_delete = [cid for cid, c in self.chunks.items() if c.document_id == document_id]
                for cid in to_delete:
                    del self.chunks[cid]
                return len(to_delete)
            
            async def bulk_create(self, chunks):
                for chunk in chunks:
                    await self.create(chunk)
                return chunks
            
            async def bulk_update_embeddings(self, chunk_embeddings):
                for chunk_id, embedding, model in chunk_embeddings:
                    if chunk_id in self.chunks:
                        self.chunks[chunk_id].embedding = embedding
                        self.chunks[chunk_id].embedding_model = model
                return len(chunk_embeddings)
            
            async def get_chunks_by_document(self, document_id, limit=100, offset=0):
                return [c for c in self.chunks.values() if c.document_id == document_id]
            
            async def get_statistics(self):
                total = len(self.chunks)
                with_embeddings = len([c for c in self.chunks.values() if c.embedding])
                return {"total_chunks": total, "chunks_with_embeddings": with_embeddings}
            
            async def get_chunks_without_embeddings(self, limit=100, embedding_model=None):
                return [c for c in self.chunks.values() if not c.embedding][:limit]
            
            async def get_random_chunks(self, limit=10):
                return list(self.chunks.values())[:limit]
        
        # Setup
        settings = get_settings()
        doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
        doc_service = DocumentService(doc_repository)
        
        chunk_repo = MockChunkRepository()
        chunk_service = ChunkService(chunk_repo)
        
        embedding_service = EmbeddingService(
            chunk_repository=chunk_repo,
            openai_api_key=settings.openai_api_key,
            model='text-embedding-3-small'
        )
        
        chunk_processor = ChunkProcessor(
            document_service=doc_service,
            chunk_service=chunk_service,
            embedding_service=embedding_service,
            chunk_size=500,
            chunk_overlap=100,
        )
        
        # Create test document
        test_content = """
        This is a comprehensive performance test for the MCP RAG Server embedding system.
        The system processes documents by splitting them into chunks and generating vector embeddings.
        Each chunk is processed through OpenAI's embedding API to create semantic representations.
        The performance metrics include processing time, throughput, and embedding quality.
        Vector embeddings enable semantic search and retrieval augmented generation capabilities.
        The system supports multiple document types and maintains high processing efficiency.
        Quality validation ensures that embeddings are generated correctly and consistently.
        Performance monitoring tracks processing speed and resource utilization over time.
        """ * 3  # Make it longer for better testing
        
        # Test document processing performance
        start_time = time.time()
        
        document = await doc_service.create_document(
            url="https://test.example.com/performance-test",
            content=test_content,
            document_type=DocumentType.HTML,
            metadata={"title": "Performance Test Document"},
        )
        
        doc_creation_time = time.time() - start_time
        
        # Test chunk processing
        processing_start_time = time.time()
        success = await chunk_processor.process_document(document.id)
        processing_total_time = time.time() - processing_start_time
        
        if success:
            chunks = await chunk_service.get_chunks_by_document(document.id)
            stats = chunk_processor.get_statistics()
            
            results = {
                "document_creation_time_ms": doc_creation_time * 1000,
                "processing_total_time_seconds": processing_total_time,
                "chunks_created": len(chunks),
                "embeddings_generated": stats["embeddings_generated"],
                "chunks_per_second": len(chunks) / processing_total_time if processing_total_time > 0 else 0,
                "embeddings_per_second": stats["embeddings_generated"] / processing_total_time if processing_total_time > 0 else 0,
                "avg_chunk_size": statistics.mean([len(c.content) for c in chunks]) if chunks else 0,
                "embedding_dimensions": len(chunks[0].embedding) if chunks and chunks[0].embedding else 0,
                "success": True
            }
            
            print(f'   ✅ Chunks created: {results["chunks_created"]}')
            print(f'   ✅ Processing time: {results["processing_total_time_seconds"]:.2f}s')
            print(f'   ✅ Chunks/sec: {results["chunks_per_second"]:.2f}')
            print(f'   ✅ Embeddings/sec: {results["embeddings_per_second"]:.2f}')
            print(f'   ✅ Embedding dims: {results["embedding_dimensions"]}')
        else:
            results = {"error": "Document processing failed", "success": False}
            print('   ❌ Document processing failed')
        
        self.results["embedding_tests"] = results
        return results
    
    async def test_search_performance(self, num_queries: int = 20) -> Dict[str, Any]:
        """Test search performance with various queries."""
        print(f'🔍 Testing Search Performance ({num_queries} queries)')
        
        test_queries = [
            "artificial intelligence",
            "machine learning algorithms",
            "natural language processing",
            "vector embeddings",
            "semantic search",
            "retrieval augmented generation",
            "document processing",
            "performance optimization",
            "data analysis",
            "information retrieval"
        ]
        
        search_latencies = []
        errors = 0
        
        async with aiohttp.ClientSession() as session:
            for i in range(num_queries):
                query = test_queries[i % len(test_queries)]
                
                start_time = time.time()
                try:
                    async with session.get(f"{self.base_url}/api/search/simple?q={query}&max_results=10") as response:
                        data = await response.json()
                        latency = (time.time() - start_time) * 1000
                        search_latencies.append(latency)
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception as e:
                    errors += 1
                    print(f'   Error in query {i+1}: {e}')
        
        if search_latencies:
            results = {
                "total_queries": num_queries,
                "successful_queries": len(search_latencies),
                "failed_queries": errors,
                "avg_search_latency_ms": statistics.mean(search_latencies),
                "min_search_latency_ms": min(search_latencies),
                "max_search_latency_ms": max(search_latencies),
                "p95_search_latency_ms": statistics.quantiles(search_latencies, n=20)[18] if len(search_latencies) > 20 else max(search_latencies),
                "queries_per_second": len(search_latencies) / (sum(search_latencies) / 1000) if search_latencies else 0,
                "success_rate": len(search_latencies) / num_queries * 100
            }
            
            print(f'   ✅ Avg search latency: {results["avg_search_latency_ms"]:.2f}ms')
            print(f'   ✅ P95 latency: {results["p95_search_latency_ms"]:.2f}ms')
            print(f'   ✅ Success rate: {results["success_rate"]:.1f}%')
        else:
            results = {"error": "All search queries failed"}
            print('   ❌ All search queries failed')
        
        self.results["search_tests"] = results
        return results
    
    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report."""
        report = []
        report.append("🚀 MCP RAG SERVER PERFORMANCE REPORT")
        report.append("=" * 60)
        report.append(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # API Performance
        if self.results["api_tests"]:
            report.append("🌐 API PERFORMANCE:")
            for endpoint, metrics in self.results["api_tests"].items():
                if "error" not in metrics:
                    report.append(f"   {endpoint}:")
                    report.append(f"     Avg Latency: {metrics['avg_latency_ms']:.2f}ms")
                    report.append(f"     P95 Latency: {metrics['p95_latency_ms']:.2f}ms")
                    report.append(f"     Success Rate: {metrics['success_rate']:.1f}%")
        
        # Load Testing
        if self.results["load_tests"] and "error" not in self.results["load_tests"]:
            load = self.results["load_tests"]
            report.append("")
            report.append("⚡ LOAD TESTING:")
            report.append(f"   Concurrent Users: {load['concurrent_users']}")
            report.append(f"   Total Requests: {load['total_requests']}")
            report.append(f"   Throughput: {load['throughput_rps']:.2f} RPS")
            report.append(f"   Avg Latency: {load['avg_latency_ms']:.2f}ms")
            report.append(f"   P95 Latency: {load['p95_latency_ms']:.2f}ms")
        
        # Crawling Performance
        if self.results["crawling_tests"] and "error" not in self.results["crawling_tests"]:
            crawl = self.results["crawling_tests"]
            report.append("")
            report.append("🕷️  CRAWLING PERFORMANCE:")
            report.append(f"   Pages Crawled: {crawl['pages_crawled']}")
            report.append(f"   Crawl Time: {crawl['crawl_total_time_seconds']:.2f}s")
            report.append(f"   Pages/Second: {crawl['pages_per_second']:.2f}")
            report.append(f"   Success Rate: {crawl['success_rate']:.1f}%")
        
        # Embedding Performance
        if self.results["embedding_tests"] and self.results["embedding_tests"].get("success"):
            embed = self.results["embedding_tests"]
            report.append("")
            report.append("🤖 EMBEDDING PERFORMANCE:")
            report.append(f"   Chunks Created: {embed['chunks_created']}")
            report.append(f"   Processing Time: {embed['processing_total_time_seconds']:.2f}s")
            report.append(f"   Chunks/Second: {embed['chunks_per_second']:.2f}")
            report.append(f"   Embeddings/Second: {embed['embeddings_per_second']:.2f}")
            report.append(f"   Embedding Dimensions: {embed['embedding_dimensions']}")
        
        # Search Performance
        if self.results["search_tests"] and "error" not in self.results["search_tests"]:
            search = self.results["search_tests"]
            report.append("")
            report.append("🔍 SEARCH PERFORMANCE:")
            report.append(f"   Total Queries: {search['total_queries']}")
            report.append(f"   Avg Search Latency: {search['avg_search_latency_ms']:.2f}ms")
            report.append(f"   P95 Latency: {search['p95_search_latency_ms']:.2f}ms")
            report.append(f"   Success Rate: {search['success_rate']:.1f}%")
        
        # Overall Assessment
        report.append("")
        report.append("🎯 OVERALL ASSESSMENT:")
        
        # Calculate overall score
        scores = []
        if self.results["api_tests"]:
            api_score = sum(1 for metrics in self.results["api_tests"].values() 
                          if "error" not in metrics and metrics.get("success_rate", 0) > 95)
            scores.append(api_score / len(self.results["api_tests"]) * 100)
        
        if self.results["load_tests"] and "error" not in self.results["load_tests"]:
            load_score = 100 if self.results["load_tests"]["throughput_rps"] > 10 else 50
            scores.append(load_score)
        
        if self.results["search_tests"] and "error" not in self.results["search_tests"]:
            search_score = self.results["search_tests"]["success_rate"]
            scores.append(search_score)
        
        overall_score = statistics.mean(scores) if scores else 0
        
        if overall_score >= 90:
            report.append("   🟢 EXCELLENT - System performing at optimal levels")
        elif overall_score >= 75:
            report.append("   🟡 GOOD - System performing well with minor issues")
        elif overall_score >= 50:
            report.append("   🟠 FAIR - System functional but needs optimization")
        else:
            report.append("   🔴 POOR - System requires immediate attention")
        
        report.append(f"   Overall Score: {overall_score:.1f}/100")
        
        return "\n".join(report)
    
    async def run_full_performance_test(self):
        """Run the complete performance test suite."""
        print("🚀 Starting Comprehensive RAG Server Performance Test")
        print("=" * 70)
        
        start_time = time.time()
        
        try:
            # Run all tests
            await self.test_api_latency(num_requests=30)
            await self.test_concurrent_load(concurrent_users=5, requests_per_user=10)
            await self.test_search_performance(num_queries=15)
            await self.test_crawling_performance()
            await self.test_embedding_performance()
            
            total_time = time.time() - start_time
            
            print(f"\n⏱️  Total test time: {total_time:.2f} seconds")
            print("\n" + self.generate_performance_report())
            
            # Save results to file
            with open(f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json", "w") as f:
                json.dump(self.results, f, indent=2)
            
            print(f"\n💾 Detailed results saved to performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json")
            
        except Exception as e:
            print(f"\n❌ Performance test failed: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """Main function to run performance tests."""
    tester = RAGPerformanceTester()
    await tester.run_full_performance_test()


if __name__ == "__main__":
    asyncio.run(main())
