ФАЗА 1: MVP CORE - Дет<PERSON>йлен План за Augment Code
🚨 КРИТИЧНИ ПРАВИЛА ЗА ИЗПЪЛНЕНИЕ
‼️ ЗАДЪЛЖИТЕЛНИ ИЗИСКВАНИЯ:
✅ ВСЯКА ЗАДАЧА трябва да има валидационни квадратчета
‼️ ЗАБРАНЕНО е преминаване без 100% потвърждение
🔍 ИЗИСКВАНЕ: Реално тестване на всяка функционалност
🚫 СТРОГО ЗАБРАНЕНО: Лъжене или измисляне на резултати
📝 ЗАДЪЛЖИТЕЛНО: Отметка [✅] след всяка завършена задача
📅 СЕДМИЦА 1: ОСНОВИ И ИНФРАСТРУКТУРА
ЗАДАЧА 1.1: Създаване на проектна структура
Цел: Създаване на пълна проектна структура с всички необходими файлове
Стъпка 1.1.1: Създаване на основни директории
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Създаване на основната структура
mkdir -p mcp-rag-server
cd mcp-rag-server

# Core архитектура
mkdir -p core/domain/entities
mkdir -p core/domain/repositories
mkdir -p core/application/services
mkdir -p core/application/use_cases
mkdir -p core/infrastructure/database
mkdir -p core/infrastructure/external

# Crawlers
mkdir -p crawlers/html
mkdir -p crawlers/pdf
mkdir -p crawlers/hybrid

# API
mkdir -p api/mcp
mkdir -p api/health
mkdir -p api/middleware

# Workflows
mkdir -p workflows/prefect

# Config
mkdir -p config/settings
mkdir -p config/database

# Tests
mkdir -p tests/unit/core
mkdir -p tests/integration
mkdir -p tests/e2e

# Monitoring
mkdir -p monitoring/prometheus
mkdir -p monitoring/logging

# Deployment
mkdir -p deployment/docker
mkdir -p deployment/scripts

# Documentation
mkdir -p docs/api
mkdir -p docs/architecture

# Scripts
mkdir -p scripts/setup
mkdir -p scripts/maintenance
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.1.1:
Generated bash
# Проверка че всички директории са създадени
find . -type d | sort

# Трябва да видиш точно тези директории:
# ./core/domain/entities
# ./core/domain/repositories
# ./core/application/services
# ./core/application/use_cases
# ./core/infrastructure/database
# ./core/infrastructure/external
# ./crawlers/html
# ./crawlers/pdf
# ./crawlers/hybrid
# ./api/mcp
# ./api/health
# ./api/middleware
# ./workflows/prefect
# ./config/settings
# ./config/database
# ./tests/unit/core
# ./tests/integration
# ./tests/e2e
# ./monitoring/prometheus
# ./monitoring/logging
# ./deployment/docker
# ./deployment/scripts
# ./docs/api
# ./docs/architecture
# ./scripts/setup
# ./scripts/maintenance
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.1.1:
100% съм убеден че всички директории са създадени правилно
Командата find . -type d | sort показва всички очаквани директории
НЕ лъжа и НЕ си измислям - структурата е реална и функционална
Стъпка 1.1.2: Създаване на __init__.py файлове
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Създаване на __init__.py във всички Python пакети
find . -type d -name "*.py" -prune -o -type d -print | while read dir; do
    if [[ "$dir" != "." && "$dir" != "./deployment" && "$dir" != "./docs" && "$dir" != "./scripts" ]]; then
        touch "$dir/__init__.py"
    fi
done

# Специфично създаване на важните __init__.py
touch core/__init__.py
touch core/domain/__init__.py
touch core/application/__init__.py
touch core/infrastructure/__init__.py
touch crawlers/__init__.py
touch api/__init__.py
touch workflows/__init__.py
touch config/__init__.py
touch tests/__init__.py
touch monitoring/__init__.py
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.1.2:
Generated bash
# Проверка че __init__.py файловете са създадени
find . -name "__init__.py" | sort

# Трябва да видиш поне тези файлове:
# ./api/__init__.py
# ./config/__init__.py
# ./core/__init__.py
# ./core/application/__init__.py
# ./core/domain/__init__.py
# ./core/infrastructure/__init__.py
# ./crawlers/__init__.py
# ./monitoring/__init__.py
# ./tests/__init__.py
# ./workflows/__init__.py
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.1.2:
100% съм убеден че всички __init__.py файлове са създадени
Командата find . -name "__init__.py" показва поне 10 файла
НЕ лъжа и НЕ си измислям - файловете съществуват реално
Стъпка 1.1.3: Създаване на pyproject.toml
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > pyproject.toml << 'PYPROJECT_EOF'
[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-rag-server"
version = "0.1.0"
description = "MCP RAG Server with advanced crawling and vector search"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"

dependencies = [
    # Core Framework
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Database & Storage
    "supabase>=2.3.0",
    "pgvector>=0.2.4",
    "redis>=5.0.1",
    
    # Async & HTTP
    "aiohttp>=3.9.1",
    "httpx>=0.25.2",
    "asyncio-throttle>=1.0.2",
    
    # Web Crawling
    "scrapy>=2.11.0",
    "playwright>=1.40.0",
    "beautifulsoup4>=4.12.2",
    
    # Document Processing
    "pymupdf>=1.23.8",
    "langdetect>=1.0.9",
    
    # AI & Embeddings
    "openai>=1.6.0",
    
    # Workflow Orchestration
    "prefect>=2.14.0",
    
    # Monitoring & Observability
    "prometheus-client>=0.16.0",
    "structlog>=23.2.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    
    # Security
    "cryptography>=40.0.2",
    
    # Utilities
    "tenacity>=8.2.2",
    "python-dotenv>=1.0.0",
    "orjson>=3.9.10",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.1",
    "flake8>=6.1.0",
    "pre-commit>=3.6.0",
]

test = [
    "factory-boy>=3.3.0",
    "testcontainers>=3.7.1",
    "httpx>=0.25.2",
]

docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocs-mermaid2-plugin>=1.1.1",
]

[project.scripts]
mcp-rag-server = "main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "crawlers*", "api*", "workflows*", "config*", "monitoring*"]

[tool.black]
line-length = 100
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow tests",
]
PYPROJECT_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.1.3:
Generated bash
# Проверка че pyproject.toml е създаден правилно
ls -la pyproject.toml
cat pyproject.toml | head -10

# Проверка на синтаксиса
python -c "import tomllib; tomllib.load(open('pyproject.toml', 'rb'))" 2>/dev/null && echo "✅ pyproject.toml syntax OK" || echo "❌ pyproject.toml syntax ERROR"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.1.3:
100% съм убеден че pyproject.toml е създаден правилно
Файлът съществува и има валиден TOML синтаксис
НЕ лъжа и НЕ си измислям - файлът е реален и функционален
Стъпка 1.1.4: Създаване на .env.example
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > .env.example << 'ENV_EOF'
# =============================================================================
# MCP RAG Server Configuration
# =============================================================================

# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# Database Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=8192

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=false

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ENCRYPTION_KEY=your-encryption-key-here-32-chars

# Crawling Configuration
CRAWL_DELAY_MIN=1
CRAWL_DELAY_MAX=3
CRAWL_CONCURRENT_REQUESTS=8
CRAWL_USER_AGENT=MCP-RAG-Server/1.0

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Environment
ENVIRONMENT=development
DEBUG=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Prefect Configuration
PREFECT_API_URL=http://localhost:4200/api
PREFECT_LOGGING_LEVEL=INFO
ENV_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.1.4:
Generated bash
# Проверка че .env.example е създаден
ls -la .env.example
wc -l .env.example

# Трябва да има поне 40 реда
line_count=$(wc -l < .env.example)
if [ "$line_count" -ge 40 ]; then
    echo "✅ .env.example has $line_count lines (OK)"
else
    echo "❌ .env.example has only $line_count lines (ERROR)"
fi
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.1.4:
100% съм убеден че .env.example е създаден правилно
Файлът съдържа поне 40 реда с конфигурация
НЕ лъжа и НЕ си измислям - файлът е реален и функционален
Стъпка 1.1.5: Създаване на .gitignore
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > .gitignore << 'GITIGNORE_EOF'
# Byte-compiled / optimized / DLL files
__pycache__/
*.py[cod]
*$py.class

# C extensions
*.so

# Distribution / packaging
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# Project specific
*.db
*.sqlite
logs/
temp/
tmp/
.DS_Store
Thumbs.db

# Scrapy
.scrapy/
scrapy.cfg

# Prefect
.prefect/

# Docker
.dockerignore
Dockerfile.dev

# Local development
local_config.py
test_data/
GITIGNORE_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.1.5:
Generated bash
# Проверка че .gitignore е създаден
ls -la .gitignore
wc -l .gitignore

# Трябва да има поне 100 реда
line_count=$(wc -l < .gitignore)
if [ "$line_count" -ge 100 ]; then
    echo "✅ .gitignore has $line_count lines (OK)"
else
    echo "❌ .gitignore has only $line_count lines (ERROR)"
fi
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.1.5:
100% съм убеден че .gitignore е създаден правилно
Файлът съдържа поне 100 реда с ignore правила
НЕ лъжа и НЕ си измислям - файлът е реален и функционален
Стъпка 1.1.6: Създаване на README.md
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > README.md << 'README_EOF'
# MCP RAG Server

A production-ready Model Context Protocol (MCP) server with Retrieval-Augmented Generation (RAG) capabilities.

## 🚀 Features

- **Advanced Web Crawling**: Hybrid approach with aiohttp, Scrapy, and Playwright
- **Vector Search**: Supabase with pgvector and HNSW indexing
- **Document Processing**: PDF, HTML, and text processing with embeddings
- **MCP Protocol**: Full Model Context Protocol implementation
- **Production Ready**: Monitoring, logging, error handling, and scalability
- **Clean Architecture**: Domain-driven design with proper separation of concerns

## requirements

- Python 3.11+
- PostgreSQL with pgvector extension
- Redis
- Supabase account (or local PostgreSQL)
- OpenAI API key

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mcp-rag-server
Use code with caution.
Bash
Create virtual environment:
Generated bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows
Use code with caution.
Bash
Install dependencies:
Generated bash
pip install -e .
Use code with caution.
Bash
Configure environment:
Generated bash
cp .env.example .env
# Edit .env with your configuration
Use code with caution.
Bash
Initialize database:
Generated bash
python scripts/setup/init_database.py
Use code with caution.
Bash
🚀 Quick Start
Start the MCP server:```bash
uvicorn main:app --host 0.0.0.0 --port 8000
Generated code
2. Test the health endpoint:
```bash
curl http://localhost:8000/health
Use code with caution.
Start crawling:
Generated bash
python scripts/crawl.py --url https://example.com
Use code with caution.
Bash
📖 Documentation
Architecture Overview
API Documentation
Deployment Guide
🧪 Testing
Run all tests:
Generated bash
pytest
Use code with caution.
Bash
Run specific test types:
Generated bash
pytest tests/unit/          # Unit tests
pytest tests/integration/   # Integration tests
pytest tests/e2e/          # End-to-end tests
Use code with caution.
Bash
📈 Monitoring
Prometheus metrics: http://localhost:9090/metrics
Health checks: http://localhost:8000/health
API documentation: http://localhost:8000/docs
🤝 Contributing
Fork the repository
Create a feature branch
Make your changes
Add tests
Submit a pull request
📜 License
MIT License - see LICENSE file for details.
README_EOF
Generated code
**ВАЛИДАЦИЯ 1.1.6:**
```bash
# Проверка че README.md е създаден
ls -la README.md
wc -l README.md

# Проверка на съдържанието
head -5 README.md
tail -5 README.md
Use code with caution.
✅ ПОТВЪРЖДЕНИЕ 1.1.6:
100% съм убеден че README.md е създаден правилно
Файлът съдържа пълно описание на проекта
НЕ лъжа и НЕ си измислям - файлът е реален и функционален
✅ ФИНАЛНА ВАЛИДАЦИЯ НА ЗАДАЧА 1.1:
Generated bash
# Проверка на цялата структура
echo "=== ПРОВЕРКА НА ПРОЕКТНАТА СТРУКТУРА ==="
ls -la
echo ""
echo "=== ПРОВЕРКА НА ДИРЕКТОРИИ ==="
find . -type d | sort
echo ""
echo "=== ПРОВЕРКА НА ОСНОВНИ ФАЙЛОВЕ ==="
ls -la pyproject.toml .env.example .gitignore README.md
echo ""
echo "=== ПРОВЕРКА НА __init__.py ФАЙЛОВЕ ==="
find . -name "__init__.py" | wc -l
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ НА ЦЯЛАТА ЗАДАЧА 1.1:
100% съм убеден че цялата проектна структура е създадена правилно
Всички директории, файлове и конфигурации са на място
НЕ лъжа и НЕ си измислям - структурата е реална и готова за работа
ЗАДАЧА 1.2: Git инициализация и първи commit
Стъпка 1.2.1: Git инициализация
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Инициализация на Git репозиторий
git init

# Конфигуриране на Git (ако не е направено)
git config user.name "Your Name"
git config user.email "<EMAIL>"

# Добавяне на всички файлове
git add .

# Първи commit
git commit -m "Initial project structure

- Created Clean Architecture folder structure
- Added pyproject.toml with all dependencies
- Added .env.example with configuration
- Added comprehensive .gitignore
- Added README.md with documentation
- Added __init__.py files for Python packages"
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.2.1:
Generated bash
# Проверка че Git е инициализиран
git status
git log --oneline

# Проверка че всички файлове са добавени
git ls-files | wc -l
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.2.1:
100% съм убеден че Git репозитория е инициализиран правилно
Първият commit е направен с всички файлове
НЕ лъжа и НЕ си измислям - Git репозитория работи
ЗАДАЧА 1.3: Виртуална среда и зависимости
Стъпка 1.3.1: Създаване на виртуална среда
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Създаване на виртуална среда
python -m venv venv

# Активиране на виртуалната среда
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows

# Проверка че виртуалната среда е активна
which python
python --version
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.3.1:
Generated bash
# Проверка че виртуалната среда е създадена
ls -la venv/
ls -la venv/bin/  # Linux/Mac
# ls -la venv\Scripts\  # Windows

# Проверка че Python е от виртуалната среда
which python | grep venv
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.3.1:
100% съм убеден че виртуалната среда е създадена правилно
Python командата сочи към виртуалната среда
НЕ лъжа и НЕ си измислям - виртуалната среда работи
Стъпка 1.3.2: Инсталиране на зависимости
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Upgrade pip
pip install --upgrade pip

# Инсталиране на основните зависимости
pip install -e .

# Инсталиране на development зависимости
pip install -e ".[dev,test,docs]"

# Проверка на инсталираните пакети
pip list
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.3.2:
Generated bash
# Проверка че основните пакети са инсталирани
pip show fastapi
pip show supabase
pip show openai
pip show scrapy
pip show pytest

# Проверка на общия брой пакети
pip list | wc -l
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.3.2:
100% съм убеден че всички зависимости са инсталирани правилно
Основните пакети (fastapi, supabase, openai, scrapy, pytest) са налични
НЕ лъжа и НЕ си измислям - зависимостите са реални и работят
ЗАДАЧА 1.4: Основни конфигурационни файлове
Стъпка 1.4.1: Създаване на config/settings/base.py
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > config/settings/base.py << 'CONFIG_EOF'
"""
Base configuration settings for MCP RAG Server.
"""

from pydantic import Field
from pydantic_settings import BaseSettings
from typing import Optional


class Settings(BaseSettings):
    """Base settings class with common configuration."""
    
    # Application
    app_name: str = "MCP RAG Server"
    app_version: str = "0.1.0"
    debug: bool = False
    environment: str = "development"
    
    # API Configuration
    api_host: str = "0.0.0.0"
    api_port: int = 8000
    api_workers: int = 4
    api_reload: bool = False
    
    # Database Configuration
    supabase_url: str = Field(..., description="Supabase project URL")
    supabase_service_key: str = Field(..., description="Supabase service key")
    supabase_anon_key: Optional[str] = Field(None, description="Supabase anon key")
    
    # Database Pool Configuration
    database_pool_size: int = 10
    database_max_overflow: int = 20
    database_pool_timeout: int = 30
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    openai_model: str = "text-embedding-3-small"
    openai_max_tokens: int = 8192
    
    # Redis Configuration
    redis_url: str = "redis://localhost:6379/0"
    redis_password: Optional[str] = None
    redis_db: int = 0
    
    # Security
    secret_key: str = Field(..., description="Secret key for encryption")
    encryption_key: str = Field(..., description="Encryption key (32 chars)")
    
    # Crawling Configuration
    crawl_delay_min: float = 1.0
    crawl_delay_max: float = 3.0
    crawl_concurrent_requests: int = 8
    crawl_user_agent: str = "MCP-RAG-Server/1.0"
    
    # Monitoring
    prometheus_port: int = 9090
    log_level: str = "INFO"
    structured_logging: bool = True
    
    # Rate Limiting
    rate_limit_requests: int = 100
    rate_limit_window: int = 60
    
    # Prefect Configuration
    prefect_api_url: str = "http://localhost:4200/api"
    prefect_logging_level: str = "INFO"
    
    class Config:
        env_file = ".env"
        case_sensitive = False
        extra = "ignore"


# Global settings instance
settings = Settings()
CONFIG_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.4.1:
Generated bash
# Проверка че файлът е създаден
ls -la config/settings/base.py

# Проверка на синтаксиса
python -c "from config.settings.base import settings; print('✅ Config loaded successfully')"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.4.1:
100% съм убеден че config/settings/base.py е създаден правилно
Python може да импортира настройките без грешки
НЕ лъжа и НЕ си измислям - конфигурацията работи
Стъпка 1.4.2: Създаване на main.py
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > main.py << 'MAIN_EOF'
"""
Main entry point for MCP RAG Server.
"""

import asyncio
import logging
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from config.settings.base import settings
from api.health.endpoints import router as health_router


# Configure logging
logging.basicConfig(
    level=getattr(logging, settings.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)

logger = logging.getLogger(__name__)


def create_app() -> FastAPI:
    """Create and configure FastAPI application."""
    
    app = FastAPI(
        title=settings.app_name,
        version=settings.app_version,
        debug=settings.debug,
        docs_url="/docs" if settings.debug else None,
        redoc_url="/redoc" if settings.debug else None,
    )
    
    # CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"] if settings.debug else [],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(health_router, prefix="/health", tags=["health"])
    
    @app.on_event("startup")
    async def startup_event():
        """Application startup event."""
        logger.info(f"Starting {settings.app_name} v{settings.app_version}")
        logger.info(f"Environment: {settings.environment}")
        logger.info(f"Debug mode: {settings.debug}")
    
    @app.on_event("shutdown")
    async def shutdown_event():
        """Application shutdown event."""
        logger.info(f"Shutting down {settings.app_name}")
    
    return app


# Create app instance
app = create_app()


def main():
    """Main entry point."""
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        workers=1 if settings.debug else settings.api_workers,
        reload=settings.api_reload,
        log_level=settings.log_level.lower(),
    )


if __name__ == "__main__":
    main()
MAIN_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.4.2:
Generated bash
# Проверка че файлът е създаден
ls -la main.py

# Проверка на синтаксиса
python -c "import main; print('✅ Main module loaded successfully')"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.4.2:
100% съм убеден че main.py е създаден правилно
Python може да импортира main модула без грешки
НЕ лъжа и НЕ си измислям - main.py работи
Стъпка 1.4.3: Създаване на health endpoint
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > api/health/endpoints.py << 'HEALTH_EOF'
"""
Health check endpoints for MCP RAG Server.
"""

from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import time
import psutil
import sys
from datetime import datetime

from config.settings.base import settings


router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    system_info: Dict[str, Any]


class DetailedHealthResponse(BaseModel):
    """Detailed health check response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    system_info: Dict[str, Any]
    checks: Dict[str, Dict[str, Any]]


# Application start time
_start_time = time.time()


@router.get("/", response_model=HealthResponse)
async def basic_health_check():
    """Basic health check endpoint."""
    
    current_time = time.time()
    uptime = current_time - _start_time
    
    # System information
    system_info = {
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage_percent": psutil.disk_usage('/').percent,
    }
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version,
        environment=settings.environment,
        uptime_seconds=round(uptime, 2),
        system_info=system_info
    )


@router.get("/detailed", response_model=DetailedHealthResponse)
async def detailed_health_check():
    """Detailed health check with component status."""
    
    current_time = time.time()
    uptime = current_time - _start_time
    
    # System information
    system_info = {
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage_percent": psutil.disk_usage('/').percent,
        "cpu_percent": psutil.cpu_percent(interval=1),
    }
    
    # Component checks
    checks = {
        "api": {
            "status": "healthy",
            "message": "API server is running"
        },
        "configuration": {
            "status": "healthy",
            "message": "Configuration loaded successfully"
        }
    }
    
    # Overall status
    overall_status = "healthy" if all(
        check["status"] == "healthy" for check in checks.values()
    ) else "unhealthy"
    
    return DetailedHealthResponse(
        status=overall_status,
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version,
        environment=settings.environment,
        uptime_seconds=round(uptime, 2),
        system_info=system_info,
        checks=checks
    )


@router.get("/ready")
async def readiness_check():
    """Readiness check for Kubernetes."""
    return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}


@router.get("/live")
async def liveness_check():
    """Liveness check for Kubernetes."""
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
HEALTH_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.4.3:
Generated bash
# Проверка че файлът е създаден
ls -la api/health/endpoints.py

# Проверка на синтаксиса
python -c "from api.health.endpoints import router; print('✅ Health endpoints loaded successfully')"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.4.3:
100% съм убеден че api/health/endpoints.py е създаден правилно
Python може да импортира health endpoints без грешки
НЕ лъжа и НЕ си измислям - health endpoints работят
ЗАДАЧА 1.5: Първи тест на сървъра
Стъпка 1.5.1: Създаване на .env файл
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Копиране на .env.example към .env
cp .env.example .env

# Редактиране на .env със тестови стойности
cat > .env << 'ENV_TEST_EOF'
# Test configuration for MCP RAG Server
SUPABASE_URL=https://test-project.supabase.co
SUPABASE_SERVICE_KEY=test-service-key
SUPABASE_ANON_KEY=test-anon-key

DATABASE_POOL_SIZE=5
DATABASE_MAX_OVERFLOW=10
DATABASE_POOL_TIMEOUT=30

OPENAI_API_KEY=test-openai-key
OPENAI_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=8192

REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_RELOAD=true

SECRET_KEY=test-secret-key-change-in-production-32-chars
ENCRYPTION_KEY=test-encryption-key-32-characters

CRAWL_DELAY_MIN=1
CRAWL_DELAY_MAX=3
CRAWL_CONCURRENT_REQUESTS=8
CRAWL_USER_AGENT=MCP-RAG-Server/1.0

PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

ENVIRONMENT=development
DEBUG=true

RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

PREFECT_API_URL=http://localhost:4200/api
PREFECT_LOGGING_LEVEL=INFO
ENV_TEST_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.5.1:
Generated bash
# Проверка че .env файлът е създаден
ls -la .env

# Проверка че настройките се зареждат
python -c "from config.settings.base import settings; print(f'✅ Environment: {settings.environment}')"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.5.1:
100% съм убеден че .env файлът е създаден правилно
Настройките се зареждат без грешки
НЕ лъжа и НЕ си измислям - конфигурацията работи
Стъпка 1.5.2: Стартиране на сървъра
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Стартиране на сървъра в background
python main.py &
SERVER_PID=$!

# Изчакване сървърът да стартира
sleep 5

# Проверка че сървърът работи
curl -f http://localhost:8000/health/ || echo "❌ Server not responding"

# Спиране на сървъра
kill $SERVER_PID 2>/dev/null || true
Use code with caution.
Bash
ВАЛИДАЦИЯ 1.5.2:
Generated bash
# Тест на health endpoint
echo "Testing health endpoint..."
python main.py &
SERVER_PID=$!
sleep 5

# Тест на основния health endpoint
response=$(curl -s http://localhost:8000/health/)
echo "Health response: $response"

# Тест на detailed health endpoint
detailed_response=$(curl -s http://localhost:8000/health/detailed)
echo "Detailed health response: $detailed_response"

# Тест на ready endpoint
ready_response=$(curl -s http://localhost:8000/health/ready)
echo "Ready response: $ready_response"

# Спиране на сървъра
kill $SERVER_PID 2>/dev/null || true
sleep 2
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 1.5.2:
100% съм убеден че сървърът стартира успешно
Health endpoints отговарят с валидни JSON отговори
НЕ лъжа и НЕ си измислям - сървърът работи реално
✅ ФИНАЛНА ВАЛИДАЦИЯ НА СЕДМИЦА 1
Generated bash
echo "=== ФИНАЛНА ПРОВЕРКА НА СЕДМИЦА 1 ==="

echo "1. Проверка на проектната структура:"
find . -type d | sort

echo ""
echo "2. Проверка на основни файлове:"
ls -la pyproject.toml .env .gitignore README.md main.py

echo ""
echo "3. Проверка на Python модули:"
python -c "
try:
    from config.settings.base import settings
    from api.health.endpoints import router
    import main
    print('✅ All modules import successfully')
except Exception as e:
    print(f'❌ Import error: {e}')
"

echo ""
echo "4. Проверка на зависимости:"
pip show fastapi supabase openai scrapy pytest | grep "Name:"

echo ""
echo "5. Тест на сървъра:"
python main.py &
SERVER_PID=$!
sleep 5
curl -f http://localhost:8000/health/ && echo "✅ Server works" || echo "❌ Server failed"
kill $SERVER_PID 2>/dev/null || true

echo ""
echo "6. Git статус:"
git status --porcelain | wc -l
git log --oneline | head -1
Use code with caution.
Bash
✅ ФИНАЛНО ПОТВЪРЖДЕНИЕ НА СЕДМИЦА 1:
100% съм убеден че цялата седмица 1 е завършена успешно
Проектната структура е създадена и работи
Сървърът стартира и отговаря на заявки
Всички конфигурации и зависимости са на място
НЕ лъжа и НЕ си измислям - всичко работи реално
📝 CHECKPOINT СЕДМИЦА 1 ЗАВЪРШЕНА ✅
Какво е направено:
✅ Създадена Clean Architecture проектна структура
✅ Конфигурирани всички зависимости в pyproject.toml
✅ Създадени конфигурационни файлове (.env, .gitignore, README.md)
✅ Имплементиран основен FastAPI сървър с health endpoints
✅ Тестван работещ сървър на localhost:8000
✅ Git репозиторий инициализиран с първи commit
Следваща стъпка: Седмица 2 - Database setup и основни модели