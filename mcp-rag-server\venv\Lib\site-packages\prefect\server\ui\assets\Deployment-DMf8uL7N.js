import{d as N,W as E,g as r,u as P,e as W,f as $,aI as j,al as A,bt as M,bu as f,bX as Z,i as s,c,a as _,o as p,j as l,k as n,n as e,a6 as q,bz as b,q as z,F as O,B as X,bY as Y,bZ as v,b_ as G,b$ as H,c0 as J,c1 as K,c2 as L,c3 as Q,be as ee,c4 as te}from"./index-ei-kaitd.js";import{u as ne}from"./usePageTitle-LeBMnqrg.js";const se=N({__name:"Deployment",setup(le){const m=E("deploymentId"),i=r(()=>[m.value]),g=P(),w=W(),h={interval:3e5},d=$(w.deployments.getDeployment,[m.value],h),t=r(()=>d.response),x=r(()=>{var a,o;return[{label:"Details",hidden:j.xl},{label:"Runs"},{label:"Upcoming"},{label:"Parameters",hidden:(a=t.value)==null?void 0:a.deprecated},{label:"Configuration",hidden:(o=t.value)==null?void 0:o.deprecated},{label:"Description"}]}),u=A("tab","Details"),{tabs:R}=M(x,u);function k(){g.push(ee.deployments())}const{filter:D}=f({deployments:{id:i},flowRuns:{state:{name:te.filter(a=>a!=="Scheduled")}}}),{filter:T}=f({sort:"START_TIME_ASC",deployments:{id:i},flowRuns:{state:{name:["Scheduled"]}}}),{flowRun:y}=Z(()=>({deployments:{id:i.value}})),U=r(()=>t.value?`Deployment: ${t.value.name}`:"Deployment");return ne(U),(a,o)=>{const C=s("p-content"),F=s("p-heading"),I=s("p-divider"),S=s("p-tabs"),V=s("p-layout-well");return t.value?(p(),c(V,{key:0,class:"deployment"},{header:l(()=>[n(e(Q),{deployment:t.value,onUpdate:e(d).refresh,onDelete:k},null,8,["deployment","onUpdate"])]),well:l(()=>[n(e(v),{deployment:t.value,alternate:"",onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),default:l(()=>[n(S,{selected:e(u),"onUpdate:selected":o[0]||(o[0]=B=>q(u)?u.value=B:null),tabs:e(R)},{description:l(()=>[n(C,{secondary:""},{default:l(()=>[t.value.deprecated?(p(),c(e(J),{key:0})):t.value.description?(p(),c(e(K),{key:1,description:t.value.description},null,8,["description"])):(p(),c(e(L),{key:2,deployment:t.value},null,8,["deployment"]))]),_:1})]),parameters:l(()=>[n(e(H),{deployment:t.value},null,8,["deployment"])]),configuration:l(()=>[n(e(G),{deployment:t.value},null,8,["deployment"])]),details:l(()=>[n(e(v),{deployment:t.value,onUpdate:e(d).refresh},null,8,["deployment","onUpdate"])]),runs:l(()=>[e(y)?(p(),z(O,{key:0},[n(F,{heading:"6",class:"deployment__next-run"},{default:l(()=>o[1]||(o[1]=[X(" Next Run ")])),_:1,__:[1]}),n(e(Y),{"flow-run":e(y)},null,8,["flow-run"]),n(I)],64)):_("",!0),n(e(b),{filter:e(D),selectable:"",prefix:"runs"},null,8,["filter"])]),upcoming:l(()=>[n(e(b),{filter:e(T),selectable:"",prefix:"upcoming"},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):_("",!0)}}});export{se as default};
//# sourceMappingURL=Deployment-DMf8uL7N.js.map
