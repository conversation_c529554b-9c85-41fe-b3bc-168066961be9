#!/usr/bin/env python3
"""Check what data is actually stored in Supabase."""

import asyncio
from core.application.services import DocumentService
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from config.settings import get_settings


async def check_supabase_data():
    """Check what data is actually in Supabase."""
    print('🔍 Checking Supabase Data')
    print('=' * 40)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    try:
        # Get all documents
        print('📄 Checking documents...')
        
        # Try to get recent documents
        documents = await doc_service.get_recent_documents(limit=20)
        
        print(f'📊 Found {len(documents)} documents in database')
        
        if documents:
            print('\n📝 Recent documents:')
            for i, doc in enumerate(documents[:10], 1):
                content_length = len(doc.content) if doc.content else 0
                print(f'   {i}. {doc.url}')
                print(f'      Status: {doc.status.value}')
                print(f'      Type: {doc.document_type.value}')
                print(f'      Content: {content_length} chars')
                print(f'      Created: {doc.created_at}')
                if doc.crawl_session_id:
                    print(f'      Session: {doc.crawl_session_id}')
                
                # Show content preview
                if doc.content:
                    preview = doc.content[:200].strip()
                    if preview:
                        print(f'      Preview: "{preview}..."')
                print()
        else:
            print('❌ No documents found in database!')
        
        # Check for eufunds documents specifically
        print('\n🇪🇺 Checking for eufunds.bg documents...')
        
        # This is a simple way to check - we'll look for URLs containing eufunds
        eufunds_docs = [doc for doc in documents if 'eufunds' in doc.url.lower()]
        
        print(f'📊 Found {len(eufunds_docs)} eufunds.bg documents')
        
        if eufunds_docs:
            print('\n📝 EUFunds documents:')
            for i, doc in enumerate(eufunds_docs, 1):
                content_length = len(doc.content) if doc.content else 0
                print(f'   {i}. {doc.url}')
                print(f'      Content: {content_length} chars')
                print(f'      Status: {doc.status.value}')
                if doc.content:
                    # Clean preview
                    clean_content = ' '.join(doc.content.split())
                    preview = clean_content[:300] if clean_content else "No content"
                    print(f'      Content preview: "{preview}..."')
                print()
        
        # Check crawl sessions
        print('\n🕷️  Checking crawl sessions...')
        
        # Get unique crawl session IDs
        session_ids = set()
        for doc in documents:
            if doc.crawl_session_id:
                session_ids.add(doc.crawl_session_id)
        
        print(f'📊 Found {len(session_ids)} unique crawl sessions')
        
        if session_ids:
            for session_id in list(session_ids)[:5]:  # Show first 5
                session_docs = [doc for doc in documents if doc.crawl_session_id == session_id]
                print(f'   Session {session_id[:8]}...: {len(session_docs)} documents')
        
        # Summary statistics
        print('\n📊 Summary Statistics:')
        print(f'   Total documents: {len(documents)}')
        print(f'   EUFunds documents: {len(eufunds_docs)}')
        print(f'   Crawl sessions: {len(session_ids)}')
        
        # Content statistics
        total_content = sum(len(doc.content) if doc.content else 0 for doc in documents)
        docs_with_content = sum(1 for doc in documents if doc.content and doc.content.strip())
        
        print(f'   Documents with content: {docs_with_content}/{len(documents)}')
        print(f'   Total content length: {total_content:,} chars')
        if docs_with_content > 0:
            print(f'   Average content length: {total_content // docs_with_content:,} chars')
        
        # Status distribution
        status_counts = {}
        for doc in documents:
            status = doc.status.value
            status_counts[status] = status_counts.get(status, 0) + 1
        
        print(f'   Status distribution: {status_counts}')
        
        return {
            "total_documents": len(documents),
            "eufunds_documents": len(eufunds_docs),
            "crawl_sessions": len(session_ids),
            "documents_with_content": docs_with_content,
            "total_content_length": total_content,
            "status_distribution": status_counts
        }
        
    except Exception as e:
        print(f'❌ Error checking Supabase data: {e}')
        import traceback
        traceback.print_exc()
        return {"error": str(e)}


async def check_tables_exist():
    """Check if required tables exist in Supabase."""
    print('\n🗄️  Checking Database Tables')
    print('=' * 40)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Check documents table
        print('📋 Checking documents table...')
        try:
            result = client.table('documents').select('id').limit(1).execute()
            print('   ✅ Documents table exists')
        except Exception as e:
            print(f'   ❌ Documents table error: {e}')
        
        # Check chunks table
        print('📋 Checking chunks table...')
        try:
            result = client.table('chunks').select('id').limit(1).execute()
            print('   ✅ Chunks table exists')
        except Exception as e:
            print(f'   ❌ Chunks table error: {e}')
            print('   💡 This explains why chunk processing failed!')
        
    except Exception as e:
        print(f'❌ Error checking tables: {e}')


async def main():
    """Main function."""
    print('🔍 SUPABASE DATA VERIFICATION')
    print('=' * 50)
    
    # Check what data exists
    data_stats = await check_supabase_data()
    
    # Check table structure
    await check_tables_exist()
    
    # Final assessment
    print('\n🎯 ASSESSMENT')
    print('=' * 20)
    
    if "error" in data_stats:
        print('❌ Cannot access Supabase data')
        print('🔧 Check connection and credentials')
    else:
        total_docs = data_stats["total_documents"]
        eufunds_docs = data_stats["eufunds_documents"]
        docs_with_content = data_stats["documents_with_content"]
        
        print(f'📊 Database Status:')
        print(f'   Total documents: {total_docs}')
        print(f'   EUFunds documents: {eufunds_docs}')
        print(f'   Documents with content: {docs_with_content}')
        
        if total_docs == 0:
            print('❌ NO DATA IN DATABASE!')
            print('💡 Crawling may not be working properly')
        elif eufunds_docs == 0:
            print('⚠️  No EUFunds data found')
            print('💡 Recent crawl may have failed')
        elif docs_with_content < total_docs:
            print('⚠️  Some documents have no content')
            print('💡 Content extraction needs improvement')
        else:
            print('✅ Database has good data!')
        
        # RAG System Assessment
        print(f'\n🤖 RAG System Assessment:')
        if total_docs == 0:
            print('   🔴 RAG CANNOT WORK - No data to search!')
        elif docs_with_content == 0:
            print('   🔴 RAG CANNOT WORK - No content to process!')
        elif eufunds_docs == 0:
            print('   🟠 RAG LIMITED - No recent EUFunds data!')
        else:
            print('   🟢 RAG CAN WORK - Has data to process!')


if __name__ == "__main__":
    asyncio.run(main())
