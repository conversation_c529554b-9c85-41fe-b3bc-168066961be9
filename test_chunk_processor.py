#!/usr/bin/env python3
"""Test script for chunk processor."""

import asyncio
from core.application.services import (
    DocumentService, ChunkService, EmbeddingService
)
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.domain.entities import DocumentType
from config.settings import get_settings


# Mock chunk repository for testing
class MockChunkRepository:
    def __init__(self):
        self.chunks = {}
        self.next_id = 1
    
    async def create(self, chunk):
        chunk.id = str(self.next_id)
        self.next_id += 1
        self.chunks[chunk.id] = chunk
        return chunk
    
    async def get_by_id(self, chunk_id):
        return self.chunks.get(chunk_id)
    
    async def update(self, chunk):
        if chunk.id in self.chunks:
            self.chunks[chunk.id] = chunk
        return chunk
    
    async def delete(self, chunk_id):
        return self.chunks.pop(chunk_id, None) is not None
    
    async def get_chunks_by_document(self, document_id, limit=100, offset=0):
        return [c for c in self.chunks.values() if c.document_id == document_id]
    
    async def count_chunks_by_document(self, document_id):
        return len([c for c in self.chunks.values() if c.document_id == document_id])
    
    async def delete_chunks_by_document(self, document_id):
        to_delete = [cid for cid, c in self.chunks.items() if c.document_id == document_id]
        for cid in to_delete:
            del self.chunks[cid]
        return len(to_delete)
    
    async def bulk_create(self, chunks):
        for chunk in chunks:
            await self.create(chunk)
        return chunks
    
    async def bulk_update_embeddings(self, chunk_embeddings):
        for chunk_id, embedding, model in chunk_embeddings:
            if chunk_id in self.chunks:
                self.chunks[chunk_id].embedding = embedding
                self.chunks[chunk_id].embedding_model = model
        return len(chunk_embeddings)
    
    async def get_chunks_without_embeddings(self, limit=100, embedding_model=None):
        return [c for c in self.chunks.values() if not c.embedding][:limit]
    
    async def get_chunks_by_embedding_model(self, model):
        return [c for c in self.chunks.values() if c.embedding_model == model]
    
    async def get_random_chunks(self, limit=10):
        return list(self.chunks.values())[:limit]
    
    async def get_statistics(self):
        total = len(self.chunks)
        with_embeddings = len([c for c in self.chunks.values() if c.embedding])
        return {
            "total_chunks": total,
            "chunks_with_embeddings": with_embeddings,
        }


async def test_chunk_processor():
    print('🧩 Testing Chunk Processor')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    # Use mock chunk repository for testing
    chunk_repo = MockChunkRepository()
    chunk_service = ChunkService(chunk_repo)
    
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repo,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    chunk_processor = ChunkProcessor(
        document_service=doc_service,
        chunk_service=chunk_service,
        embedding_service=embedding_service,
        chunk_size=500,  # Smaller for testing
        chunk_overlap=100,
    )
    
    # Test 1: Create a test document
    print('\n📄 Test 1: Creating test document')
    test_content = """
    This is a test document for the MCP RAG Server chunk processor.
    
    The document contains multiple paragraphs to test the chunking functionality.
    Each paragraph should be processed correctly and split into appropriate chunks.
    
    The embedding system should generate vector representations for each chunk.
    This allows for semantic search and retrieval augmented generation (RAG).
    
    The system supports various document types including HTML, PDF, and plain text.
    All content is processed through the same pipeline for consistency.
    
    Quality validation ensures that chunks are properly sized and embedded.
    Statistics tracking helps monitor the processing performance over time.
    """
    
    document = await doc_service.create_document(
        url="https://test.example.com/test-doc",
        content=test_content,
        document_type=DocumentType.HTML,
        metadata={"title": "Test Document", "description": "Test document for chunk processor"},
    )
    
    print(f'✅ Created test document: {document.id}')
    print(f'   Content length: {len(document.content)} chars')
    
    # Test 2: Process the document
    print('\n🔄 Test 2: Processing document into chunks')
    success = await chunk_processor.process_document(document.id)
    
    if success:
        print('✅ Document processed successfully')
        
        # Check chunks
        chunks = await chunk_service.get_chunks_by_document(document.id)
        print(f'   Created {len(chunks)} chunks')
        
        for i, chunk in enumerate(chunks):
            print(f'   Chunk {i+1}: {len(chunk.content)} chars, embedding: {len(chunk.embedding) if chunk.embedding else 0} dims')
    else:
        print('❌ Document processing failed')
    
    # Test 3: Processing statistics
    print('\n📊 Test 3: Processing statistics')
    stats = await chunk_processor.get_processing_statistics()
    print(f'📈 Processing statistics:')
    for key, value in stats.items():
        if isinstance(value, dict):
            print(f'   {key}:')
            for k, v in value.items():
                print(f'     {k}: {v}')
        else:
            print(f'   {key}: {value}')
    
    # Test 4: Quality validation
    print('\n🔍 Test 4: Quality validation')
    quality = await chunk_processor.validate_processing_quality(sample_size=10)
    print(f'🎯 Quality validation:')
    print(f'   Quality score: {quality.get("quality_score", 0):.1f}%')
    print(f'   Sample size: {quality.get("sample_size", 0)}')
    
    if quality.get("issues"):
        print('   Issues found:')
        for issue, count in quality["issues"].items():
            if count > 0:
                print(f'     {issue}: {count}')
    
    if quality.get("recommendations"):
        print('   Recommendations:')
        for rec in quality["recommendations"]:
            print(f'     - {rec}')
    
    # Test 5: Process chunks without embeddings
    print('\n🤖 Test 5: Processing chunks without embeddings')
    processed = await chunk_processor.process_chunks_without_embeddings(limit=50)
    print(f'✅ Processed {processed} chunks without embeddings')
    
    print('\n🎉 Chunk processor testing completed!')


async def test_batch_processing():
    """Test batch processing of multiple documents."""
    print('\n📦 Testing Batch Processing')
    print('=' * 50)
    
    # Setup (reuse from previous test)
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    chunk_repo = MockChunkRepository()
    chunk_service = ChunkService(chunk_repo)
    
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repo,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    chunk_processor = ChunkProcessor(
        document_service=doc_service,
        chunk_service=chunk_service,
        embedding_service=embedding_service,
    )
    
    # Create multiple test documents
    test_docs = [
        "Document 1: This is the first test document with some content about AI and machine learning.",
        "Document 2: This is the second document discussing natural language processing and embeddings.",
        "Document 3: The third document covers retrieval augmented generation and vector databases.",
    ]
    
    created_docs = []
    for i, content in enumerate(test_docs, 1):
        doc = await doc_service.create_document(
            url=f"https://test.example.com/doc-{i}",
            content=content,
            document_type=DocumentType.HTML,
        )
        created_docs.append(doc)
    
    print(f'✅ Created {len(created_docs)} test documents')
    
    # Process pending documents
    result = await chunk_processor.process_pending_documents(limit=10)
    
    print(f'📊 Batch processing results:')
    print(f'   Processed: {result["processed"]}')
    print(f'   Failed: {result["failed"]}')
    print(f'   Total: {result["total"]}')
    print(f'   Success rate: {result["success_rate"]:.1f}%')
    
    # Final statistics
    final_stats = chunk_processor.get_statistics()
    print(f'\n📈 Final processor statistics:')
    for key, value in final_stats.items():
        print(f'   {key}: {value}')


async def main():
    """Main function."""
    print('🚀 MCP RAG Server - Chunk Processor Testing')
    print('=' * 60)
    
    # Basic chunk processor test
    await test_chunk_processor()
    
    # Batch processing test
    await test_batch_processing()
    
    print('\n🎉 All chunk processor tests completed!')


if __name__ == "__main__":
    asyncio.run(main())
