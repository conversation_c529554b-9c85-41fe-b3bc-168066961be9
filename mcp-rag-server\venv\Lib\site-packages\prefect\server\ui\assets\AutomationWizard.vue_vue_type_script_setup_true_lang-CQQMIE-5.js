import{d as w,M as q,g as S,Z as L,N as X,i as v,c as V,o as _,O as M,P as I,Q as G,R as J,S as h,T as tt,q as k,l as j,k as d,t as E,j as y,a as x,n as l,r as P,U as Z,X as et,a0 as R,a1 as D,a2 as at,w as Q,a3 as ot,F as $,v as nt,B as C,a4 as st,a5 as B,a6 as W,H as it,I as rt,a7 as ut,a8 as lt,a9 as pt,aa as mt,ab as ct,ac as dt,ad as gt,ae as O,af as H,ag as _t,ah as vt,ai as ft,u as At,aj as yt}from"./index-ei-kaitd.js";import{m as F,i as bt,A as Tt}from"./mapper-BuxGYc8V.js";const Vt=w({__name:"AutomationActionTypeSelect",props:{type:{required:!0},typeModifiers:{}},emits:["update:type"],setup(f){const n=q(f,"type"),o=S(()=>{const e=L.map(t=>({label:X[t],value:t}));return n.value==="do-nothing"?e:e.filter(t=>t.value!=="do-nothing")});return(e,t)=>{const p=v("p-select");return _(),V(p,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=c=>n.value=c),options:o.value,class:"automation-action-type-select"},null,8,["modelValue","options"])}}}),wt={class:"automation-wizard-action"},St={class:"automation-wizard-action__header"},Ut={class:"automation-wizard-action__heading"},kt=w({__name:"AutomationWizardAction",props:{index:{},action:{},automation:{}},emits:["delete","update:action"],setup(f,{emit:n}){const o=f,e=n,t=S({get(){return o.action.type??null},set(m){if(M(m)){e("update:action",{});return}const r=I(o.automation.trigger),u=G(m,r);e("update:action",u)}}),{state:p,error:c}=J(t,"Action Type",m=>!!m),s=S(()=>o.action.type?h(tt,{action:o.action,"onUpdate:action":m=>e("update:action",m)}):null);function i(m){e("update:action",m)}return(m,r)=>{const u=v("p-button"),g=v("p-label"),A=v("p-content");return _(),k("div",wt,[j("div",St,[j("span",Ut,"Action "+E(m.index+1),1),d(u,{size:"sm",icon:"TrashIcon",onClick:r[0]||(r[0]=b=>e("delete"))})]),d(A,null,{default:y(()=>[d(g,{label:"Action Type",state:l(p),message:l(c)},{default:y(({id:b})=>[d(Vt,{id:b,type:t.value,"onUpdate:type":r[1]||(r[1]=a=>t.value=a),state:l(p)},null,8,["id","type","state"])]),_:1},8,["state","message"]),s.value?(_(),V(P(s.value.component),Z({key:0},s.value.props,{"onUpdate:action":i}),null,16)):x("",!0)]),_:1})])}}}),zt={class:"automation-wizard-step-actions"},xt=w({__name:"AutomationWizardStepActions",props:{automation:{}},emits:["update:automation"],setup(f,{emit:n}){const o=f,e=n,t=et(o.automation.actions??[]);function p(){t.push({})}function c(u){t.splice(u,1)}function s(u,g){t[u]=g}const{error:i}=J(t,"Actions",u=>u.length?!0:"At least 1 action is required"),{defineValidate:m}=R(),{validate:r}=D();return m(r),at(()=>{t.length===0&&p()}),Q(t,()=>{e("update:automation",{...o.automation,actions:t.filter(st)})}),(u,g)=>{const A=v("p-divider"),b=v("p-message"),a=v("p-button");return _(),k("div",zt,[(_(),V(ot,null,[(_(!0),k($,null,nt(t,(T,U)=>(_(),k($,{key:U},[d(kt,{action:T,automation:u.automation,index:U,"onUpdate:action":N=>s(U,N),onDelete:N=>c(U)},null,8,["action","automation","index","onUpdate:action","onDelete"]),d(A)],64))),128))],1024)),l(i)?(_(),V(b,{key:0,error:""},{default:y(()=>[C(E(l(i)),1)]),_:1})):x("",!0),d(a,{class:"automations-wizard-step-actions__add",icon:"PlusIcon",onClick:p},{default:y(()=>g[0]||(g[0]=[C(" Add Action ")])),_:1,__:[0]})])}}}),qt=w({__name:"AutomationWizardStepDetails",props:{automation:{required:!0},automationModifiers:{}},emits:["update:automation"],setup(f){const n=q(f,"automation"),o=B(n,"name"),e=B(n,"description"),{validate:t}=D(),p=(m="",r)=>m.length>0?!0:`${r} is required`,{state:c,error:s}=J(o,"Automation name",p),{defineValidate:i}=R();return i(t),(m,r)=>{const u=v("p-text-input"),g=v("p-label"),A=v("p-content");return _(),V(A,{class:"automation-wizard-step-details"},{default:y(()=>[d(g,{label:"Automation Name",state:l(c),message:l(s)},{default:y(({id:b})=>[d(u,{id:b,modelValue:l(o),"onUpdate:modelValue":r[0]||(r[0]=a=>W(o)?o.value=a:null),state:l(c)},null,8,["id","modelValue","state"])]),_:1},8,["state","message"]),d(g,{label:"Description (Optional)"},{default:y(({id:b})=>[d(u,{id:b,modelValue:l(e),"onUpdate:modelValue":r[1]||(r[1]=a=>W(e)?e.value=a:null)},null,8,["id","modelValue"])]),_:1})]),_:1})}}}),Jt=w({__name:"AutomationTriggerJsonInput",props:{modelValue:{required:!0},modelModifiers:{}},emits:["update:modelValue"],setup(f){const n=q(f,"modelValue"),o=s=>{try{const i=JSON.parse(s);F.map("AutomationTriggerResponse",i,"AutomationTrigger")}catch{return!1}return!0},e=(s,i)=>M(s)||lt(s)||pt(s)||mt(s)?`${i} is required`:!0,t=(s,i)=>{try{JSON.parse(s)}catch{return`${i} must be valid JSON`}return!0},{state:p,error:c}=J(n,"Trigger",[e,t,o]);return(s,i)=>{const m=v("p-message"),r=v("p-label");return _(),k($,null,[d(m,{info:""},{action:y(()=>[d(l(it),{to:l(rt).docs.automationTriggers,small:""},null,8,["to"])]),default:y(()=>[i[1]||(i[1]=C(" Custom triggers allow advanced configuration of the conditions on which a trigger executes its actions. "))]),_:1,__:[1]}),d(r,{label:"Trigger",state:l(p),message:l(c)},{default:y(()=>[d(l(ut),{modelValue:n.value,"onUpdate:modelValue":i[0]||(i[0]=u=>n.value=u),class:"automation-trigger-json-input__json-input","show-format-button":"",state:l(p)},null,8,["modelValue","state"])]),_:1},8,["state","message"])],64)}}}),Nt=w({__name:"AutomationTriggerTemplateSelect",props:{template:{required:!0},templateModifiers:{}},emits:["update:template"],setup(f){const n=q(f,"template"),o=S(()=>ct.map(e=>({label:dt(e),value:e})));return(e,t)=>{const p=v("p-select");return _(),V(p,{modelValue:n.value,"onUpdate:modelValue":t[0]||(t[0]=c=>n.value=c),"empty-message":"Select template",options:o.value,class:"automation-trigger-template-select"},null,8,["modelValue","options"])}}}),$t=w({__name:"AutomationWizardStepTrigger",props:{automation:{required:!0},automationModifiers:{}},emits:["update:automation"],setup(f){const n=q(f,"automation"),o=S({get(){return n.value.trigger?I(n.value.trigger):null},set(a){if(M(a)){b(void 0);return}b(H(a))}}),{values:e}=gt(["Form","JSON"]),t=O("Form");function p(){const a=F.map("AutomationTrigger",n.value.trigger,"AutomationTriggerRequest");return vt(a)}const c=O(p());Q(()=>n.value.trigger,()=>c.value=p());function s(){const a=JSON.parse(c.value),T=F.map("AutomationTriggerResponse",a,"AutomationTrigger");return b(T),T}async function i(a){t.value!==a&&await r()&&(a==="Form"&&s(),a==="JSON"&&(c.value=p()),t.value=a)}const{defineValidate:m}=R(),{validate:r}=D(),{state:u,error:g}=J(o,"Trigger template",a=>a?!0:"Trigger type is required");m(async()=>{const a=await r();return a&&t.value==="JSON"&&s(),a});const A=S(()=>{if(!o.value)return null;const a=n.value.trigger??H(o.value);switch(a.type){case"event":return h(_t,{template:o.value,trigger:a,"onUpdate:trigger":b});case"compound":case"sequence":return h(ft,{trigger:a,"onUpdate:trigger":b});default:const T=a;throw new Error(`AutomationWizardStepTrigger is missing case for trigger type ${T.type}`)}});function b(a){n.value={...n.value,trigger:a}}return(a,T)=>{const U=v("p-label"),N=v("p-button-group"),Y=v("p-content");return _(),V(Y,{class:"automation-wizard-step-trigger"},{default:y(()=>[d(U,{label:"Trigger Template",state:l(u),message:l(g)},{default:y(({id:z})=>[d(Nt,{id:z,template:o.value,"onUpdate:template":T[0]||(T[0]=K=>o.value=K),state:l(u)},null,8,["id","template","state"])]),_:1},8,["state","message"]),A.value?(_(),k($,{key:0},[d(N,{class:"automation-wizard-step-trigger__mode-switcher","model-value":t.value,options:l(e),small:"","onUpdate:modelValue":T[1]||(T[1]=z=>i(z))},null,8,["model-value","options"]),t.value==="Form"&&o.value?(_(),V(P(A.value.component),Z({key:o.value},A.value.props),null,16)):t.value==="JSON"?(_(),V(Jt,{key:1,modelValue:c.value,"onUpdate:modelValue":T[2]||(T[2]=z=>c.value=z)},null,8,["modelValue"])):x("",!0)],64)):x("",!0)]),_:1})}}}),Ct=w({__name:"AutomationWizard",props:{automation:{},editing:{type:Boolean}},emits:["submit"],setup(f,{emit:n}){const o=f,e=O(o.automation??{}),t=n,p=At(),c=S(()=>o.automation?"Save":"Create"),s=[{title:"Trigger",key:"trigger-step"},{title:"Actions",key:"actions-step"},{title:"Details",key:"details-step"}],i=O();function m(){t("submit",new Tt(e.value))}function r(){p.back()}return(u,g)=>(_(),V(l(yt),{ref_key:"wizardRef",ref:i,class:"automation-wizard",steps:s,"last-step-text":c.value,"show-cancel":"",nonlinear:u.editing,"show-save-and-exit":u.editing,onCancel:r,onSubmit:m},{"trigger-step":y(()=>[d($t,{automation:e.value,"onUpdate:automation":g[0]||(g[0]=A=>e.value=A)},null,8,["automation"])]),"actions-step":y(()=>[l(bt)(e.value)?(_(),V(xt,{key:0,automation:e.value,"onUpdate:automation":g[1]||(g[1]=A=>e.value=A)},null,8,["automation"])):x("",!0)]),"details-step":y(()=>[d(qt,{automation:e.value,"onUpdate:automation":g[2]||(g[2]=A=>e.value=A)},null,8,["automation"])]),_:1},8,["last-step-text","nonlinear","show-save-and-exit"]))}});export{Ct as _};
//# sourceMappingURL=AutomationWizard.vue_vue_type_script_setup_true_lang-CQQMIE-5.js.map
