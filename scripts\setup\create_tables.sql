-- Create tables for MCP RAG Server
-- Run this in Supabase SQL Editor

-- Enable vector extension
CREATE EXTENSION IF NOT EXISTS vector;

-- Chunks table with vector embeddings
CREATE TABLE IF NOT EXISTS chunks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    content_length INTEGER NOT NULL DEFAULT 0,
    chunk_index INTEGER NOT NULL DEFAULT 0,
    start_position INTEGER NOT NULL DEFAULT 0,
    end_position INTEGER NOT NULL DEFAULT 0,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    embedding_model VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    relevance_score FLOAT,
    readability_score FLOAT
);

-- Crawl sessions table
CREATE TABLE IF NOT EXISTS crawl_sessions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255),
    start_urls TEXT[] NOT NULL,
    config JSONB DEFAULT '{}',
    strategy VARCHAR(20) NOT NULL DEFAULT 'breadth_first',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    current_depth INTEGER DEFAULT 0,
    urls_to_crawl TEXT[],
    urls_crawled TEXT[],
    urls_failed TEXT[],
    urls_skipped TEXT[],
    stats JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    error_message TEXT,
    last_error_at TIMESTAMPTZ,
    tags TEXT[],
    description TEXT,
    created_by VARCHAR(255)
);

-- Search queries table
CREATE TABLE IF NOT EXISTS search_queries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    query_text TEXT NOT NULL,
    search_type VARCHAR(20) NOT NULL DEFAULT 'semantic',
    search_scope VARCHAR(20) NOT NULL DEFAULT 'all',
    filters JSONB DEFAULT '{}',
    embedding vector(1536),
    embedding_model VARCHAR(100),
    similarity_threshold FLOAT DEFAULT 0.7,
    results JSONB DEFAULT '[]',
    total_results INTEGER DEFAULT 0,
    execution_time_ms FLOAT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    executed_at TIMESTAMPTZ,
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    context JSONB DEFAULT '{}',
    index_hits INTEGER DEFAULT 0,
    cache_hits INTEGER DEFAULT 0
);

-- Create indexes for better performance

-- Chunks indexes
CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_chunks_chunk_index ON chunks(chunk_index);
CREATE INDEX IF NOT EXISTS idx_chunks_created_at ON chunks(created_at);
CREATE INDEX IF NOT EXISTS idx_chunks_embedding_model ON chunks(embedding_model);
CREATE INDEX IF NOT EXISTS idx_chunks_content_length ON chunks(content_length);

-- Vector similarity index using HNSW
CREATE INDEX IF NOT EXISTS idx_chunks_embedding_hnsw ON chunks 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Crawl sessions indexes
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_status ON crawl_sessions(status);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_created_at ON crawl_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_created_by ON crawl_sessions(created_by);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_strategy ON crawl_sessions(strategy);

-- Search queries indexes
CREATE INDEX IF NOT EXISTS idx_search_queries_query_text ON search_queries(query_text);
CREATE INDEX IF NOT EXISTS idx_search_queries_search_type ON search_queries(search_type);
CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX IF NOT EXISTS idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_search_queries_session_id ON search_queries(session_id);

-- Vector similarity index for search queries
CREATE INDEX IF NOT EXISTS idx_search_queries_embedding_hnsw ON search_queries 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_chunks_updated_at BEFORE UPDATE ON chunks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crawl_sessions_updated_at BEFORE UPDATE ON crawl_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create function for similarity search
CREATE OR REPLACE FUNCTION search_similar_chunks(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.7,
    max_results int DEFAULT 10,
    document_ids uuid[] DEFAULT NULL
)
RETURNS TABLE (
    chunk_id uuid,
    document_id uuid,
    content text,
    similarity float,
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as chunk_id,
        c.document_id,
        c.content,
        1 - (c.embedding <=> query_embedding) as similarity,
        c.metadata
    FROM chunks c
    WHERE 
        c.embedding IS NOT NULL
        AND (document_ids IS NULL OR c.document_id = ANY(document_ids))
        AND (1 - (c.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY c.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;
