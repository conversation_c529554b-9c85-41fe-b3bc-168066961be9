import{d as D,e as v,W as C,f as b,g as l,i as g,c as h,a as w,o as k,j as s,k as r,n as c,c5 as x,c6 as E,L as p,c7 as m,be as I,$}from"./index-ei-kaitd.js";import{u as B}from"./usePageTitle-LeBMnqrg.js";const W=D({__name:"DeploymentDuplicate",setup(N){const a=v(),u=C("deploymentId"),d=b(a.deployments.getDeployment,[u.value],{}),e=l(()=>d.response);function i(t){return"name"in t}async function y(t){try{if(!i(t))throw new Error("Invalid request");const n=await a.deployments.createDeployment(t);p("Deployment created","success"),m.push(I.deployment(n.id))}catch(n){const o=$(n,"Error creating deployment");p(o,"error"),console.warn(n)}}function f(){m.back()}const _=l(()=>e.value?`Duplicate Deployment: ${e.value.name}`:"Duplicate Deployment");return B(_),(t,n)=>{const o=g("p-layout-default");return e.value?(k(),h(o,{key:0,class:"deployment-edit"},{header:s(()=>[r(c(E),{deployment:e.value},null,8,["deployment"])]),default:s(()=>[r(c(x),{deployment:e.value,mode:"duplicate",onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):w("",!0)}}});export{W as default};
//# sourceMappingURL=DeploymentDuplicate-C_VmqbOW.js.map
