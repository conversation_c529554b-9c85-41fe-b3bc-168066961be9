"""Document service for the MCP RAG Server."""

import hashlib
from typing import List, Optional, Dict, Any
from datetime import datetime

from ...domain.entities import Document, DocumentStatus, DocumentType
from ...domain.repositories import DocumentRepository


class DocumentService:
    """Service for document operations."""
    
    def __init__(self, document_repository: DocumentRepository):
        self.document_repository = document_repository
    
    async def create_document(
        self,
        url: str,
        content: str = "",
        document_type: DocumentType = DocumentType.HTML,
        metadata: Optional[Dict[str, Any]] = None,
        crawl_session_id: Optional[str] = None,
    ) -> Document:
        """Create a new document."""
        # Check if document already exists
        existing = await self.document_repository.get_by_url(url)
        if existing:
            return existing
        
        # Create content hash
        content_hash = self._generate_content_hash(content) if content else None
        
        # Check for duplicate content
        if content_hash:
            existing_by_hash = await self.document_repository.get_by_content_hash(content_hash)
            if existing_by_hash:
                return existing_by_hash
        
        # Create new document
        document = Document(
            url=url,
            content=content,
            document_type=document_type,
            content_hash=content_hash,
            crawl_session_id=crawl_session_id,
        )
        
        # Set metadata if provided
        if metadata:
            for key, value in metadata.items():
                if hasattr(document.metadata, key):
                    setattr(document.metadata, key, value)
                else:
                    document.metadata.custom_fields[key] = value
        
        return await self.document_repository.create(document)
    
    async def get_document(self, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        return await self.document_repository.get_by_id(document_id)
    
    async def get_document_by_url(self, url: str) -> Optional[Document]:
        """Get document by URL."""
        return await self.document_repository.get_by_url(url)
    
    async def update_document_content(
        self,
        document_id: str,
        content: str,
        raw_content: Optional[str] = None,
    ) -> Optional[Document]:
        """Update document content."""
        document = await self.document_repository.get_by_id(document_id)
        if not document:
            return None
        
        document.content = content
        document.raw_content = raw_content
        document.content_hash = self._generate_content_hash(content)
        document.updated_at = datetime.utcnow()
        
        return await self.document_repository.update(document)
    
    async def update_document_status(
        self,
        document_id: str,
        status: DocumentStatus,
        error_message: Optional[str] = None,
    ) -> bool:
        """Update document status."""
        return await self.document_repository.update_document_status(
            document_id, status, error_message
        )
    
    async def mark_document_processed(
        self,
        document_id: str,
        embedding_model: str,
        chunk_count: int,
    ) -> Optional[Document]:
        """Mark document as processed."""
        document = await self.document_repository.get_by_id(document_id)
        if not document:
            return None
        
        document.update_status(DocumentStatus.COMPLETED)
        document.embedding_model = embedding_model
        document.chunk_count = chunk_count
        
        return await self.document_repository.update(document)
    
    async def mark_document_failed(
        self,
        document_id: str,
        error_message: str,
    ) -> Optional[Document]:
        """Mark document as failed."""
        document = await self.document_repository.get_by_id(document_id)
        if not document:
            return None
        
        document.update_status(DocumentStatus.FAILED, error_message)
        document.increment_retry()
        
        return await self.document_repository.update(document)
    
    async def retry_failed_document(self, document_id: str) -> Optional[Document]:
        """Retry processing a failed document."""
        document = await self.document_repository.get_by_id(document_id)
        if not document or not document.is_processable():
            return None
        
        document.update_status(DocumentStatus.PENDING)
        document.error_message = None
        
        return await self.document_repository.update(document)
    
    async def list_documents(
        self,
        limit: int = 100,
        offset: int = 0,
        status: Optional[DocumentStatus] = None,
        document_type: Optional[DocumentType] = None,
        crawl_session_id: Optional[str] = None,
    ) -> List[Document]:
        """List documents with filters."""
        return await self.document_repository.list_documents(
            limit=limit,
            offset=offset,
            status=status,
            document_type=document_type,
            crawl_session_id=crawl_session_id,
        )
    
    async def get_documents_needing_processing(self, limit: int = 100) -> List[Document]:
        """Get documents that need processing."""
        return await self.document_repository.get_documents_needing_processing(limit)
    
    async def search_documents(
        self,
        query: str,
        limit: int = 100,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Document]:
        """Search documents."""
        return await self.document_repository.search_documents(
            query=query,
            limit=limit,
            offset=offset,
            filters=filters,
        )
    
    async def get_documents_by_domain(self, domain: str) -> List[Document]:
        """Get documents from a specific domain."""
        return await self.document_repository.get_documents_by_domain(domain)
    
    async def get_documents_by_crawl_session(self, crawl_session_id: str) -> List[Document]:
        """Get documents from a specific crawl session."""
        return await self.document_repository.get_documents_by_crawl_session(crawl_session_id)
    
    async def delete_document(self, document_id: str) -> bool:
        """Delete a document."""
        return await self.document_repository.delete(document_id)
    
    async def bulk_create_documents(self, documents: List[Document]) -> List[Document]:
        """Create multiple documents in bulk."""
        # Generate content hashes for documents that don't have them
        for doc in documents:
            if doc.content and not doc.content_hash:
                doc.content_hash = self._generate_content_hash(doc.content)
        
        return await self.document_repository.bulk_create(documents)
    
    async def bulk_update_status(
        self,
        document_ids: List[str],
        status: DocumentStatus,
    ) -> int:
        """Update status for multiple documents."""
        return await self.document_repository.bulk_update_status(document_ids, status)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get document statistics."""
        stats = await self.document_repository.get_statistics()
        
        # Add computed statistics
        total_docs = stats.get("total_documents", 0)
        if total_docs > 0:
            stats["processing_rate"] = (
                stats.get("completed_documents", 0) / total_docs * 100
            )
            stats["failure_rate"] = (
                stats.get("failed_documents", 0) / total_docs * 100
            )
        
        return stats
    
    async def cleanup_old_documents(
        self,
        older_than: datetime,
        status: Optional[DocumentStatus] = None,
    ) -> int:
        """Clean up old documents."""
        return await self.document_repository.cleanup_old_documents(older_than, status)
    
    def _generate_content_hash(self, content: str) -> str:
        """Generate SHA-256 hash of content."""
        return hashlib.sha256(content.encode('utf-8')).hexdigest()
    
    def _extract_domain(self, url: str) -> str:
        """Extract domain from URL."""
        from urllib.parse import urlparse
        parsed = urlparse(url)
        return parsed.netloc.lower()
