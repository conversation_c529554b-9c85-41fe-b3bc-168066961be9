"""Search query entity for the MCP RAG Server."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid


class SearchType(str, Enum):
    """Search type enumeration."""
    SEMANTIC = "semantic"
    KEYWORD = "keyword"
    HYBRID = "hybrid"
    SIMILARITY = "similarity"


class SearchScope(str, Enum):
    """Search scope enumeration."""
    ALL = "all"
    DOCUMENTS = "documents"
    CHUNKS = "chunks"
    SPECIFIC_DOMAIN = "specific_domain"


@dataclass
class SearchFilters:
    """Search filters container."""
    document_types: List[str] = field(default_factory=list)
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
    domains: List[str] = field(default_factory=list)
    languages: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    min_relevance_score: Optional[float] = None
    max_results: int = 10
    custom_filters: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SearchResult:
    """Individual search result."""
    id: str
    content: str
    score: float
    document_id: str
    chunk_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    highlights: List[str] = field(default_factory=list)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary."""
        return {
            "id": self.id,
            "content": self.content,
            "score": self.score,
            "document_id": self.document_id,
            "chunk_id": self.chunk_id,
            "metadata": self.metadata,
            "highlights": self.highlights,
        }


@dataclass
class SearchQuery:
    """Search query entity."""
    
    # Primary identifiers
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    query_text: str = ""
    
    # Search configuration
    search_type: SearchType = SearchType.SEMANTIC
    search_scope: SearchScope = SearchScope.ALL
    filters: SearchFilters = field(default_factory=SearchFilters)
    
    # Vector search parameters
    embedding: Optional[List[float]] = None
    embedding_model: Optional[str] = None
    similarity_threshold: float = 0.7
    
    # Results
    results: List[SearchResult] = field(default_factory=list)
    total_results: int = 0
    execution_time_ms: Optional[float] = None
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    executed_at: Optional[datetime] = None
    
    # Context and metadata
    user_id: Optional[str] = None
    session_id: Optional[str] = None
    context: Dict[str, Any] = field(default_factory=dict)
    
    # Performance metrics
    index_hits: int = 0
    cache_hits: int = 0
    
    def execute_search(self, results: List[SearchResult], execution_time: float):
        """Record search execution results."""
        self.results = results
        self.total_results = len(results)
        self.execution_time_ms = execution_time
        self.executed_at = datetime.utcnow()
    
    def add_result(self, result: SearchResult):
        """Add a search result."""
        self.results.append(result)
        self.total_results = len(self.results)
    
    def get_top_results(self, limit: int = None) -> List[SearchResult]:
        """Get top search results."""
        if limit is None:
            limit = self.filters.max_results
        
        return sorted(self.results, key=lambda r: r.score, reverse=True)[:limit]
    
    def filter_by_score(self, min_score: float) -> List[SearchResult]:
        """Filter results by minimum score."""
        return [r for r in self.results if r.score >= min_score]
    
    def get_unique_documents(self) -> List[str]:
        """Get unique document IDs from results."""
        return list(set(r.document_id for r in self.results))
    
    def get_average_score(self) -> float:
        """Get average relevance score."""
        if not self.results:
            return 0.0
        return sum(r.score for r in self.results) / len(self.results)
    
    def get_score_distribution(self) -> Dict[str, int]:
        """Get score distribution in ranges."""
        distribution = {
            "0.9-1.0": 0,
            "0.8-0.9": 0,
            "0.7-0.8": 0,
            "0.6-0.7": 0,
            "0.5-0.6": 0,
            "0.0-0.5": 0,
        }
        
        for result in self.results:
            score = result.score
            if score >= 0.9:
                distribution["0.9-1.0"] += 1
            elif score >= 0.8:
                distribution["0.8-0.9"] += 1
            elif score >= 0.7:
                distribution["0.7-0.8"] += 1
            elif score >= 0.6:
                distribution["0.6-0.7"] += 1
            elif score >= 0.5:
                distribution["0.5-0.6"] += 1
            else:
                distribution["0.0-0.5"] += 1
        
        return distribution
    
    def is_executed(self) -> bool:
        """Check if query has been executed."""
        return self.executed_at is not None
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics."""
        return {
            "execution_time_ms": self.execution_time_ms,
            "total_results": self.total_results,
            "index_hits": self.index_hits,
            "cache_hits": self.cache_hits,
            "average_score": self.get_average_score(),
            "unique_documents": len(self.get_unique_documents()),
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "query_text": self.query_text,
            "search_type": self.search_type.value,
            "search_scope": self.search_scope.value,
            "filters": {
                "document_types": self.filters.document_types,
                "date_from": self.filters.date_from.isoformat() if self.filters.date_from else None,
                "date_to": self.filters.date_to.isoformat() if self.filters.date_to else None,
                "domains": self.filters.domains,
                "languages": self.filters.languages,
                "tags": self.filters.tags,
                "min_relevance_score": self.filters.min_relevance_score,
                "max_results": self.filters.max_results,
                "custom_filters": self.filters.custom_filters,
            },
            "embedding": self.embedding,
            "embedding_model": self.embedding_model,
            "similarity_threshold": self.similarity_threshold,
            "results": [r.to_dict() for r in self.results],
            "total_results": self.total_results,
            "execution_time_ms": self.execution_time_ms,
            "created_at": self.created_at.isoformat(),
            "executed_at": self.executed_at.isoformat() if self.executed_at else None,
            "user_id": self.user_id,
            "session_id": self.session_id,
            "context": self.context,
            "index_hits": self.index_hits,
            "cache_hits": self.cache_hits,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "SearchQuery":
        """Create search query from dictionary."""
        filters = SearchFilters(
            document_types=data.get("filters", {}).get("document_types", []),
            date_from=datetime.fromisoformat(data["filters"]["date_from"]) if data.get("filters", {}).get("date_from") else None,
            date_to=datetime.fromisoformat(data["filters"]["date_to"]) if data.get("filters", {}).get("date_to") else None,
            domains=data.get("filters", {}).get("domains", []),
            languages=data.get("filters", {}).get("languages", []),
            tags=data.get("filters", {}).get("tags", []),
            min_relevance_score=data.get("filters", {}).get("min_relevance_score"),
            max_results=data.get("filters", {}).get("max_results", 10),
            custom_filters=data.get("filters", {}).get("custom_filters", {}),
        )
        
        results = [
            SearchResult(
                id=r["id"],
                content=r["content"],
                score=r["score"],
                document_id=r["document_id"],
                chunk_id=r.get("chunk_id"),
                metadata=r.get("metadata", {}),
                highlights=r.get("highlights", []),
            )
            for r in data.get("results", [])
        ]
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            query_text=data.get("query_text", ""),
            search_type=SearchType(data.get("search_type", SearchType.SEMANTIC)),
            search_scope=SearchScope(data.get("search_scope", SearchScope.ALL)),
            filters=filters,
            embedding=data.get("embedding"),
            embedding_model=data.get("embedding_model"),
            similarity_threshold=data.get("similarity_threshold", 0.7),
            results=results,
            total_results=data.get("total_results", 0),
            execution_time_ms=data.get("execution_time_ms"),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.utcnow(),
            executed_at=datetime.fromisoformat(data["executed_at"]) if data.get("executed_at") else None,
            user_id=data.get("user_id"),
            session_id=data.get("session_id"),
            context=data.get("context", {}),
            index_hits=data.get("index_hits", 0),
            cache_hits=data.get("cache_hits", 0),
        )
