"""Chunk service for the MCP RAG Server."""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from ...domain.entities import Chunk, ChunkMetadata
from ...domain.repositories import ChunkRepository


class ChunkService:
    """Service for chunk operations."""
    
    def __init__(self, chunk_repository: ChunkRepository):
        self.chunk_repository = chunk_repository
    
    async def create_chunk(
        self,
        document_id: str,
        content: str,
        chunk_index: int = 0,
        start_position: int = 0,
        end_position: int = 0,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> Chunk:
        """Create a new chunk."""
        chunk = Chunk(
            document_id=document_id,
            content=content,
            chunk_index=chunk_index,
            start_position=start_position,
            end_position=end_position,
        )
        
        # Set metadata if provided
        if metadata:
            chunk_metadata = ChunkMetadata()
            for key, value in metadata.items():
                if hasattr(chunk_metadata, key):
                    setattr(chunk_metadata, key, value)
                else:
                    chunk_metadata.custom_fields[key] = value
            chunk.metadata = chunk_metadata
        
        return await self.chunk_repository.create(chunk)
    
    async def get_chunk(self, chunk_id: str) -> Optional[Chunk]:
        """Get chunk by ID."""
        return await self.chunk_repository.get_by_id(chunk_id)
    
    async def update_chunk(self, chunk: Chunk) -> Chunk:
        """Update an existing chunk."""
        chunk.updated_at = datetime.utcnow()
        return await self.chunk_repository.update(chunk)
    
    async def delete_chunk(self, chunk_id: str) -> bool:
        """Delete a chunk."""
        return await self.chunk_repository.delete(chunk_id)
    
    async def get_chunks_by_document(
        self,
        document_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Chunk]:
        """Get all chunks for a document."""
        return await self.chunk_repository.get_chunks_by_document(
            document_id, limit, offset
        )
    
    async def count_chunks_by_document(self, document_id: str) -> int:
        """Count chunks for a document."""
        return await self.chunk_repository.count_chunks_by_document(document_id)
    
    async def delete_chunks_by_document(self, document_id: str) -> int:
        """Delete all chunks for a document."""
        return await self.chunk_repository.delete_chunks_by_document(document_id)
    
    async def create_chunks_from_text(
        self,
        document_id: str,
        text: str,
        chunk_size: int = 1000,
        overlap: int = 200,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> List[Chunk]:
        """Create chunks from text with specified size and overlap."""
        chunks = []
        text_length = len(text)
        
        if text_length <= chunk_size:
            # Single chunk
            chunk = await self.create_chunk(
                document_id=document_id,
                content=text,
                chunk_index=0,
                start_position=0,
                end_position=text_length,
                metadata=metadata,
            )
            chunks.append(chunk)
        else:
            # Multiple chunks with overlap
            chunk_index = 0
            start = 0
            
            while start < text_length:
                end = min(start + chunk_size, text_length)
                chunk_content = text[start:end]
                
                # Skip very small chunks at the end
                if len(chunk_content.strip()) < 50 and chunk_index > 0:
                    break
                
                chunk = await self.create_chunk(
                    document_id=document_id,
                    content=chunk_content,
                    chunk_index=chunk_index,
                    start_position=start,
                    end_position=end,
                    metadata=metadata,
                )
                chunks.append(chunk)
                
                chunk_index += 1
                start = end - overlap
                
                # Prevent infinite loop
                if start >= end:
                    break
        
        return chunks
    
    async def bulk_create_chunks(self, chunks: List[Chunk]) -> List[Chunk]:
        """Create multiple chunks in bulk."""
        return await self.chunk_repository.bulk_create(chunks)
    
    async def update_chunk_embedding(
        self,
        chunk_id: str,
        embedding: List[float],
        model: str,
    ) -> Optional[Chunk]:
        """Update chunk embedding."""
        chunk = await self.chunk_repository.get_by_id(chunk_id)
        if not chunk:
            return None
        
        chunk.update_embedding(embedding, model)
        return await self.chunk_repository.update(chunk)
    
    async def bulk_update_embeddings(
        self,
        chunk_embeddings: List[Tuple[str, List[float], str]],
    ) -> int:
        """Update embeddings for multiple chunks."""
        return await self.chunk_repository.bulk_update_embeddings(chunk_embeddings)
    
    async def search_by_embedding(
        self,
        embedding: List[float],
        limit: int = 10,
        similarity_threshold: float = 0.7,
        document_ids: Optional[List[str]] = None,
    ) -> List[Tuple[Chunk, float]]:
        """Search chunks by embedding similarity."""
        return await self.chunk_repository.search_by_embedding(
            embedding=embedding,
            limit=limit,
            similarity_threshold=similarity_threshold,
            document_ids=document_ids,
        )
    
    async def search_by_text(
        self,
        query: str,
        limit: int = 10,
        document_ids: Optional[List[str]] = None,
    ) -> List[Chunk]:
        """Search chunks by text content."""
        return await self.chunk_repository.search_by_text(
            query=query,
            limit=limit,
            document_ids=document_ids,
        )
    
    async def get_chunks_without_embeddings(
        self,
        limit: int = 100,
        embedding_model: Optional[str] = None,
    ) -> List[Chunk]:
        """Get chunks that need embeddings."""
        return await self.chunk_repository.get_chunks_without_embeddings(
            limit=limit,
            embedding_model=embedding_model,
        )
    
    async def get_similar_chunks(
        self,
        chunk_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.8,
    ) -> List[Tuple[Chunk, float]]:
        """Get chunks similar to a specific chunk."""
        return await self.chunk_repository.get_similar_chunks(
            chunk_id=chunk_id,
            limit=limit,
            similarity_threshold=similarity_threshold,
        )
    
    async def update_chunk_scores(
        self,
        chunk_scores: List[Tuple[str, float, float]],
    ) -> int:
        """Update quality scores for chunks."""
        return await self.chunk_repository.update_chunk_scores(chunk_scores)
    
    async def get_chunks_by_content_length(
        self,
        min_length: int = 0,
        max_length: int = 10000,
        limit: int = 100,
    ) -> List[Chunk]:
        """Get chunks by content length range."""
        return await self.chunk_repository.get_chunks_by_content_length(
            min_length=min_length,
            max_length=max_length,
            limit=limit,
        )
    
    async def get_random_chunks(self, limit: int = 10) -> List[Chunk]:
        """Get random chunks for sampling."""
        return await self.chunk_repository.get_random_chunks(limit)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get chunk statistics."""
        stats = await self.chunk_repository.get_statistics()
        
        # Add computed statistics
        total_chunks = stats.get("total_chunks", 0)
        chunks_with_embeddings = stats.get("chunks_with_embeddings", 0)
        
        if total_chunks > 0:
            stats["embedding_coverage"] = (chunks_with_embeddings / total_chunks * 100)
        
        return stats
    
    async def cleanup_orphaned_chunks(self) -> int:
        """Clean up chunks without corresponding documents."""
        return await self.chunk_repository.cleanup_orphaned_chunks()
    
    async def get_chunk_distribution_by_document(self) -> Dict[str, int]:
        """Get chunk count distribution by document."""
        return await self.chunk_repository.get_chunk_distribution_by_document()
    
    async def reindex_embeddings(self, embedding_model: str) -> int:
        """Reindex all embeddings for a specific model."""
        return await self.chunk_repository.reindex_embeddings(embedding_model)
    
    def calculate_optimal_chunk_size(
        self,
        text: str,
        target_chunks: int = 10,
        min_size: int = 500,
        max_size: int = 2000,
    ) -> int:
        """Calculate optimal chunk size for text."""
        text_length = len(text)
        
        if text_length <= min_size:
            return text_length
        
        # Calculate size based on target number of chunks
        calculated_size = text_length // target_chunks
        
        # Clamp to min/max bounds
        return max(min_size, min(calculated_size, max_size))
