#!/usr/bin/env python3
"""Database initialization script for MCP RAG Server."""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from supabase import create_client
from config.settings import get_settings


async def init_database():
    """Initialize the database with schema and initial data."""
    print("🔧 Initializing MCP RAG Server database...")
    
    settings = get_settings()
    
    # Create Supabase client
    supabase = create_client(settings.supabase_url, settings.supabase_service_key)
    
    # Read schema file
    schema_path = project_root / "config" / "database" / "schema.sql"
    
    if not schema_path.exists():
        print(f"❌ Schema file not found: {schema_path}")
        return False
    
    print(f"📖 Reading schema from: {schema_path}")
    
    with open(schema_path, 'r', encoding='utf-8') as f:
        schema_sql = f.read()
    
    # Split SQL into individual statements
    statements = [stmt.strip() for stmt in schema_sql.split(';') if stmt.strip()]
    
    print(f"📝 Executing {len(statements)} SQL statements...")
    
    success_count = 0
    error_count = 0
    
    for i, statement in enumerate(statements, 1):
        try:
            # Skip comments and empty statements
            if statement.startswith('--') or not statement:
                continue
            
            print(f"  [{i}/{len(statements)}] Executing statement...")
            
            # Execute the statement
            result = supabase.rpc('exec_sql', {'sql': statement}).execute()
            
            if result.data:
                success_count += 1
                print(f"    ✅ Success")
            else:
                print(f"    ⚠️  No data returned")
                
        except Exception as e:
            error_count += 1
            print(f"    ❌ Error: {str(e)}")
            
            # Continue with other statements unless it's a critical error
            if "does not exist" in str(e) and "extension" in str(e):
                print(f"    ℹ️  Extension error - this might be expected in some environments")
            elif "already exists" in str(e):
                print(f"    ℹ️  Object already exists - skipping")
            else:
                print(f"    ⚠️  Unexpected error - continuing anyway")
    
    print(f"\n📊 Database initialization completed:")
    print(f"   ✅ Successful statements: {success_count}")
    print(f"   ❌ Failed statements: {error_count}")
    
    # Test basic functionality
    print("\n🧪 Testing basic functionality...")
    
    try:
        # Test documents table
        result = supabase.table('documents').select('id').limit(1).execute()
        print("   ✅ Documents table accessible")
        
        # Test chunks table
        result = supabase.table('chunks').select('id').limit(1).execute()
        print("   ✅ Chunks table accessible")
        
        # Test crawl_sessions table
        result = supabase.table('crawl_sessions').select('id').limit(1).execute()
        print("   ✅ Crawl sessions table accessible")
        
        # Test search_queries table
        result = supabase.table('search_queries').select('id').limit(1).execute()
        print("   ✅ Search queries table accessible")
        
        print("\n🎉 Database initialization successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Database test failed: {str(e)}")
        return False


async def create_test_data():
    """Create some test data for development."""
    print("\n🧪 Creating test data...")
    
    settings = get_settings()
    supabase = create_client(settings.supabase_url, settings.supabase_service_key)
    
    try:
        # Create a test document
        test_document = {
            "url": "https://example.com/test-document",
            "content": "This is a test document for the MCP RAG Server. It contains sample content for testing purposes.",
            "document_type": "html",
            "status": "completed",
            "metadata": {
                "title": "Test Document",
                "description": "A sample document for testing",
                "language": "en"
            }
        }
        
        doc_result = supabase.table('documents').insert(test_document).execute()
        
        if doc_result.data:
            document_id = doc_result.data[0]['id']
            print(f"   ✅ Created test document: {document_id}")
            
            # Create test chunks
            test_chunks = [
                {
                    "document_id": document_id,
                    "content": "This is the first chunk of the test document.",
                    "content_length": 45,
                    "chunk_index": 0,
                    "start_position": 0,
                    "end_position": 45
                },
                {
                    "document_id": document_id,
                    "content": "This is the second chunk with more test content.",
                    "content_length": 48,
                    "chunk_index": 1,
                    "start_position": 46,
                    "end_position": 94
                }
            ]
            
            chunk_result = supabase.table('chunks').insert(test_chunks).execute()
            
            if chunk_result.data:
                print(f"   ✅ Created {len(chunk_result.data)} test chunks")
            
            # Create test crawl session
            test_session = {
                "name": "Test Crawl Session",
                "start_urls": ["https://example.com"],
                "status": "completed",
                "strategy": "breadth_first",
                "description": "A test crawl session for development"
            }
            
            session_result = supabase.table('crawl_sessions').insert(test_session).execute()
            
            if session_result.data:
                print(f"   ✅ Created test crawl session: {session_result.data[0]['id']}")
        
        print("🎉 Test data created successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Failed to create test data: {str(e)}")
        return False


async def main():
    """Main function."""
    print("🚀 MCP RAG Server Database Setup")
    print("=" * 50)
    
    # Initialize database
    db_success = await init_database()
    
    if not db_success:
        print("\n❌ Database initialization failed!")
        sys.exit(1)
    
    # Ask if user wants to create test data
    if len(sys.argv) > 1 and sys.argv[1] == "--with-test-data":
        await create_test_data()
    else:
        print("\n💡 Tip: Run with --with-test-data to create sample data for development")
    
    print("\n✅ Setup completed successfully!")
    print("\nNext steps:")
    print("1. Start the server: python main.py")
    print("2. Test health endpoint: curl http://localhost:8000/health/")
    print("3. Check the API docs: http://localhost:8000/docs")


if __name__ == "__main__":
    asyncio.run(main())
