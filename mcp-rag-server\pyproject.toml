[build-system]
requires = ["setuptools>=68.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mcp-rag-server"
version = "0.1.0"
description = "MCP RAG Server with advanced crawling and vector search"
authors = [
    {name = "Your Name", email = "<EMAIL>"}
]
readme = "README.md"
license = {text = "MIT"}
requires-python = ">=3.11"

dependencies = [
    # Core Framework
    "fastapi>=0.104.1",
    "uvicorn[standard]>=0.24.0",
    "pydantic>=2.5.0",
    "pydantic-settings>=2.1.0",
    
    # Database & Storage
    "supabase>=2.3.0",
    "pgvector>=0.2.4",
    "redis>=5.0.1",
    
    # Async & HTTP
    "aiohttp>=3.9.1",
    "httpx>=0.25.2",
    "asyncio-throttle>=1.0.2",
    
    # Web Crawling
    "scrapy>=2.11.0",
    "playwright>=1.40.0",
    "beautifulsoup4>=4.12.2",
    
    # Document Processing
    "pymupdf>=1.23.8",
    "langdetect>=1.0.9",
    
    # AI & Embeddings
    "openai>=1.6.0",
    
    # Workflow Orchestration
    "prefect>=2.14.0",
    
    # Monitoring & Observability
    "prometheus-client>=0.16.0",
    "structlog>=23.2.0",
    "opentelemetry-api>=1.21.0",
    "opentelemetry-sdk>=1.21.0",
    
    # Security
    "cryptography>=40.0.2",
    
    # Utilities
    "tenacity>=8.2.2",
    "python-dotenv>=1.0.0",
    "orjson>=3.9.10",
]

[project.optional-dependencies]
dev = [
    "pytest>=7.4.3",
    "pytest-asyncio>=0.21.1",
    "pytest-cov>=4.1.0",
    "black>=23.11.0",
    "isort>=5.12.0",
    "mypy>=1.7.1",
    "flake8>=6.1.0",
    "pre-commit>=3.6.0",
]

test = [
    "factory-boy>=3.3.0",
    "testcontainers>=3.7.1",
    "httpx>=0.25.2",
]

docs = [
    "mkdocs>=1.5.3",
    "mkdocs-material>=9.4.8",
    "mkdocs-mermaid2-plugin>=1.1.1",
]

[project.scripts]
mcp-rag-server = "main:main"

[tool.setuptools.packages.find]
where = ["."]
include = ["core*", "crawlers*", "api*", "workflows*", "config*", "monitoring*"]

[tool.black]
line-length = 100
target-version = ['py311']

[tool.isort]
profile = "black"
line_length = 100

[tool.mypy]
python_version = "3.11"
strict = true
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
addopts = "-v --tb=short --strict-markers"
markers = [
    "unit: Unit tests",
    "integration: Integration tests",
    "e2e: End-to-end tests",
    "slow: Slow tests",
]
