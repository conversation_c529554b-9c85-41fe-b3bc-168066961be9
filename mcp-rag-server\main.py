"""Main entry point for the MCP RAG Server."""

import async<PERSON>
import uvicorn
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager

from config.settings import get_settings
from api.health import router as health_router
from api.search import router as search_router


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    # Startup
    print("🚀 Starting MCP RAG Server...")
    
    # Initialize services here
    # TODO: Add service initialization
    
    yield
    
    # Shutdown
    print("🛑 Shutting down MCP RAG Server...")


def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    settings = get_settings()
    
    app = FastAPI(
        title="MCP RAG Server",
        description="A production-ready Model Context Protocol server with RAG capabilities",
        version="0.1.0",
        lifespan=lifespan,
    )
    
    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # Configure appropriately for production
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )
    
    # Include routers
    app.include_router(health_router, prefix="/health", tags=["health"])
    app.include_router(search_router, prefix="/api/search", tags=["search"])
    
    return app


app = create_app()


def main():
    """Main function to run the server."""
    settings = get_settings()
    
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.api_reload,
        workers=1 if settings.api_reload else settings.api_workers,
    )


if __name__ == "__main__":
    main()
