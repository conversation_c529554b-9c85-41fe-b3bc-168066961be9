{"version": 3, "file": "WorkPool-Bn_qnZCe.js", "sources": ["../../src/pages/WorkPool.vue"], "sourcesContent": ["<template>\n  <p-layout-well v-if=\"workPool\" class=\"work-pool\">\n    <template #header>\n      <PageHeadingWorkPool :work-pool=\"workPool\" @update=\"workPoolSubscription.refresh\" />\n      <template v-if=\"showCodeBanner\">\n        <CodeBanner class=\"work-pool__code-banner\" :command=\"codeBannerCliCommand\" title=\"Your work pool is almost ready!\" subtitle=\"Run this command to start.\" />\n      </template>\n    </template>\n    <p-tabs v-model:selected=\"tab\" :tabs=\"tabs\">\n      <template #details>\n        <WorkPoolDetails :work-pool=\"workPool\" />\n      </template>\n\n      <template #runs>\n        <FlowRunFilteredList :filter=\"flowRunFilter\" prefix=\"runs\" />\n      </template>\n\n      <template #work-queues>\n        <WorkPoolQueuesTable :work-pool-name=\"workPoolName\" />\n      </template>\n\n      <template #workers>\n        <WorkersTable :work-pool-name=\"workPoolName\" />\n      </template>\n\n      <template #deployments>\n        <DeploymentList :filter=\"deploymentsFilter\" />\n      </template>\n    </p-tabs>\n\n    <template #well>\n      <WorkPoolDetails alternate :work-pool=\"workPool\" />\n    </template>\n  </p-layout-well>\n</template>\n\n<script lang=\"ts\" setup>\n  import { media } from '@prefecthq/prefect-design'\n  import {\n    useWorkspaceApi,\n    PageHeadingWorkPool,\n    WorkPoolDetails,\n    FlowRunFilteredList,\n    WorkPoolQueuesTable,\n    useFlowRunsFilter,\n    useTabs,\n    WorkersTable,\n    CodeBanner,\n    DeploymentList,\n    useDeploymentsFilter\n  } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useRouteQueryParam, useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const workPoolName = useRouteParam('workPoolName')\n\n  const subscriptionOptions = {\n    interval: 300000,\n  }\n  const workPoolSubscription = useSubscription(api.workPools.getWorkPoolByName, [workPoolName.value], subscriptionOptions)\n  const workPool = computed(() => workPoolSubscription.response)\n  const isAgentWorkPool = computed(() => workPool.value?.type === 'prefect-agent')\n\n  const computedTabs = computed(() => [\n    { label: 'Details', hidden: media.xl },\n    { label: 'Runs' },\n    { label: 'Work Queues' },\n    { label: 'Workers', hidden: isAgentWorkPool.value },\n    { label: 'Deployments' },\n  ])\n\n  const tab = useRouteQueryParam('tab', 'Details')\n  const { tabs } = useTabs(computedTabs, tab)\n\n  const showCodeBanner = computed(() => workPool.value?.status !== 'ready')\n  const codeBannerCliCommand = computed(() => `prefect ${isAgentWorkPool.value ? 'agent' : 'worker'} start --pool \"${workPool.value?.name}\"`)\n\n  const { filter: flowRunFilter } = useFlowRunsFilter({\n    workPools: {\n      name: [workPoolName.value],\n    },\n  })\n\n  const { filter: deploymentsFilter } = useDeploymentsFilter({\n    workPools: {\n      name: [workPoolName.value],\n    },\n  })\n\n  const title = computed(() => {\n    if (!workPool.value) {\n      return 'Work Pool'\n    }\n    return `Work Pool: ${workPool.value.name}`\n  })\n\n  usePageTitle(title)\n</script>\n\n<style>\n.work-pool__code-banner { @apply\n  mt-4\n}\n</style>"], "names": ["api", "useWorkspaceApi", "workPoolName", "useRouteParam", "subscriptionOptions", "workPoolSubscription", "useSubscription", "workPool", "computed", "isAgentWorkPool", "_a", "computedTabs", "media", "tab", "useRouteQueryParam", "tabs", "useTabs", "showCodeBanner", "codeBannerCliCommand", "flowRunFilter", "useFlowRunsFilter", "deploymentsFilter", "useDeploymentsFilter", "title", "usePageTitle", "_createBlock", "_component_p_layout_well", "_createVNode", "_unref", "PageHeadingWorkPool", "CodeBanner", "WorkPoolDetails", "_component_p_tabs", "$event", "FlowRunFilteredList", "WorkPoolQueuesTable", "WorkersTable", "DeploymentList"], "mappings": "6SAuDE,MAAMA,EAAMC,EAAgB,EACtBC,EAAeC,EAAc,cAAc,EAE3CC,EAAsB,CAC1B,SAAU,GACZ,EACMC,EAAuBC,EAAgBN,EAAI,UAAU,kBAAmB,CAACE,EAAa,KAAK,EAAGE,CAAmB,EACjHG,EAAWC,EAAS,IAAMH,EAAqB,QAAQ,EACvDI,EAAkBD,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAS,QAAT,YAAAG,EAAgB,QAAS,gBAAe,EAEzEC,EAAeH,EAAS,IAAM,CAClC,CAAE,MAAO,UAAW,OAAQI,EAAM,EAAG,EACrC,CAAE,MAAO,MAAO,EAChB,CAAE,MAAO,aAAc,EACvB,CAAE,MAAO,UAAW,OAAQH,EAAgB,KAAM,EAClD,CAAE,MAAO,aAAc,CAAA,CACxB,EAEKI,EAAMC,EAAmB,MAAO,SAAS,EACzC,CAAE,KAAAC,CAAS,EAAAC,EAAQL,EAAcE,CAAG,EAEpCI,EAAiBT,EAAS,IAAA,OAAM,QAAAE,EAAAH,EAAS,QAAT,YAAAG,EAAgB,UAAW,QAAO,EAClEQ,EAAuBV,EAAS,IAAM,OAAA,iBAAWC,EAAgB,MAAQ,QAAU,QAAQ,mBAAkBC,EAAAH,EAAS,QAAT,YAAAG,EAAgB,IAAI,IAAG,EAEpI,CAAE,OAAQS,CAAc,EAAIC,EAAkB,CAClD,UAAW,CACT,KAAM,CAAClB,EAAa,KAAK,CAAA,CAC3B,CACD,EAEK,CAAE,OAAQmB,CAAkB,EAAIC,EAAqB,CACzD,UAAW,CACT,KAAM,CAACpB,EAAa,KAAK,CAAA,CAC3B,CACD,EAEKqB,EAAQf,EAAS,IAChBD,EAAS,MAGP,cAAcA,EAAS,MAAM,IAAI,GAF/B,WAGV,EAED,OAAAiB,EAAaD,CAAK,0DAjGGhB,EAAQ,WAA7BkB,EAgCgBC,EAAA,OAhCe,MAAM,WAAA,GACxB,SACT,IAAoF,CAApFC,EAAoFC,EAAAC,CAAA,EAAA,CAA9D,YAAWtB,EAAQ,MAAG,SAAQqB,EAAoBvB,CAAA,EAAC,0CACzDY,EAAc,WAC5BQ,EAA2JG,EAAAE,CAAA,EAAA,OAA/I,MAAM,yBAA0B,QAASZ,EAAoB,MAAE,MAAM,kCAAkC,SAAS,4BAAA,kCAyBrH,OACT,IAAmD,CAAnDS,EAAmDC,EAAAG,CAAA,EAAA,CAAlC,UAAA,GAAW,YAAWxB,EAAQ,KAAA,oCAvBjD,IAoBS,CApBToB,EAoBSK,EAAA,CApBO,SAAUJ,EAAGf,CAAA,0CAAHA,EAAG,MAAAoB,EAAA,MAAG,KAAML,EAAIb,CAAA,CAAA,GAC7B,UACT,IAAyC,CAAzCY,EAAyCC,EAAAG,CAAA,EAAA,CAAvB,YAAWxB,EAAQ,OAAA,KAAA,EAAA,CAAA,WAAA,CAAA,CAAA,GAG5B,OACT,IAA6D,CAA7DoB,EAA6DC,EAAAM,CAAA,EAAA,CAAvC,OAAQN,EAAaT,CAAA,EAAE,OAAO,MAAA,uBAG3C,gBACT,IAAsD,CAAtDQ,EAAsDC,EAAAO,CAAA,EAAA,CAAhC,iBAAgBP,EAAY1B,CAAA,GAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,GAGzC,UACT,IAA+C,CAA/CyB,EAA+CC,EAAAQ,CAAA,EAAA,CAAhC,iBAAgBR,EAAY1B,CAAA,GAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,GAGlC,cACT,IAA8C,CAA9CyB,EAA8CC,EAAAS,CAAA,EAAA,CAA7B,OAAQT,EAAiBP,CAAA,GAAA,KAAA,EAAA,CAAA,QAAA,CAAA,CAAA"}