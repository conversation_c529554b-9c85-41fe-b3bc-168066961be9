"""Create `flow_run_input` table

Revision ID: a299308852a7
Revises: 9c493c02ca6d
Create Date: 2023-12-07 09:51:12.569778

"""

import sqlalchemy as sa
from alembic import op

import prefect

# revision identifiers, used by Alembic.
revision = "a299308852a7"
down_revision = "9c493c02ca6d"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "flow_run_input",
        sa.Column(
            "id",
            prefect.server.utilities.database.UUID(),
            server_default=sa.text(
                "(\n    (\n        lower(hex(randomblob(4)))\n        || '-'\n       "
                " || lower(hex(randomblob(2)))\n        || '-4'\n        ||"
                " substr(lower(hex(randomblob(2))),2)\n        || '-'\n        ||"
                " substr('89ab',abs(random()) % 4 + 1, 1)\n        ||"
                " substr(lower(hex(randomblob(2))),2)\n        || '-'\n        ||"
                " lower(hex(randomblob(6)))\n    )\n    )"
            ),
            nullable=False,
        ),
        sa.Column(
            "created",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column(
            "updated",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("(strftime('%Y-%m-%d %H:%M:%f000', 'now'))"),
            nullable=False,
        ),
        sa.Column("key", sa.String(), nullable=False),
        sa.Column("value", sa.Text(), nullable=False),
        sa.Column(
            "flow_run_id", prefect.server.utilities.database.UUID(), nullable=False
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_flow_run_input")),
        sa.UniqueConstraint(
            "flow_run_id", "key", name=op.f("uq_flow_run_input__flow_run_id_key")
        ),
    )
    with op.batch_alter_table("flow_run_input", schema=None) as batch_op:
        batch_op.create_index(
            batch_op.f("ix_flow_run_input__updated"), ["updated"], unique=False
        )


def downgrade():
    with op.batch_alter_table("flow_run_input", schema=None) as batch_op:
        batch_op.drop_index(batch_op.f("ix_flow_run_input__updated"))

    op.drop_table("flow_run_input")
