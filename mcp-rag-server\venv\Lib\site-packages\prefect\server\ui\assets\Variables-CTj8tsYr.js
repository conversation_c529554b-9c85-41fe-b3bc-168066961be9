import{d,ae as m,e as _,g as o,f as v,I as b,i as k,c as r,o as s,j as c,q as h,a as y,F as C,n,cE as g,cF as x,k as D,cG as V}from"./index-ei-kaitd.js";import{u as B}from"./usePageTitle-LeBMnqrg.js";const j=d({__name:"Variables",setup(F){const l=m(),a=()=>{var e;t.value.refresh(),(e=l.value)==null||e.refreshSubscriptions()},u=_(),t=o(()=>v(u.variables.getVariables)),i=o(()=>{var e;return t.value.executed&&((e=t.value.response)==null?void 0:e.length)===0}),p=o(()=>t.value.executed);return B(b.info.variables),(e,E)=>{const f=k("p-layout-default");return s(),r(f,{class:"variables"},{header:c(()=>[D(n(V),{onCreate:a})]),default:c(()=>[p.value?(s(),h(C,{key:0},[i.value?(s(),r(n(g),{key:0,onCreate:a})):(s(),r(n(x),{key:1,ref_key:"table",ref:l,onDelete:a,onUpdate:a},null,512))],64)):y("",!0)]),_:1})}}});export{j as default};
//# sourceMappingURL=Variables-CTj8tsYr.js.map
