{"version": 3, "file": "Dashboard-ZEHiiZMn.js", "sources": ["../../src/pages/Dashboard.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"workspace-dashboard\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\" class=\"workspace-dashboard__page-heading\">\n        <template v-if=\"loaded && !empty\" #actions>\n          <div class=\"workspace-dashboard__header-actions\">\n            <div class=\"workspace-dashboard__subflows-toggle\">\n              <p-toggle v-model=\"filter.hideSubflows\" append=\"Hide subflows\" />\n            </div>\n            <FlowRunTagsInput v-model:selected=\"filter.tags\" empty-message=\"All tags\" />\n            <DateRangeSelect v-model=\"filter.range\" class=\"workspace-dashboard__date-select\" />\n          </div>\n        </template>\n      </PageHeading>\n    </template>\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <FlowRunsPageEmptyState />\n      </template>\n      <template v-else>\n        <div class=\"workspace-dashboard__grid\">\n          <WorkspaceDashboardFlowRunsCard :filter=\"filter\" />\n          <div class=\"workspace-dashboard__side\">\n            <CumulativeTaskRunsCard :filter=\"tasksFilter\" />\n            <DashboardWorkPoolsCard class=\"workspace-dashboard__work-pools\" :filter=\"filter\" />\n          </div>\n        </div>\n      </template>\n    </template>\n    <MarketingBanner\n      title=\"Ready to scale?\"\n      subtitle=\"Webhooks, role and object-level security, and serverless push work pools on Prefect Cloud\"\n    >\n      <template #actions>\n        <p-button to=\"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none\" target=\"_blank\" primary>\n          Upgrade to Cloud\n        </p-button>\n      </template>\n    </MarketingBanner>\n  </p-layout-default>\n</template>\n\n<script setup lang=\"ts\">\n  import { Crumb } from '@prefecthq/prefect-design'\n  import {\n    DashboardWorkPoolsCard,\n    WorkspaceDashboardFlowRunsCard,\n    CumulativeTaskRunsCard,\n    PageHeading,\n    FlowRunTagsInput,\n    FlowRunsPageEmptyState,\n    useWorkspaceApi,\n    subscriptionIntervalKey,\n    mapper,\n    TaskRunsFilter,\n    MarketingBanner,\n    Getter,\n    DateRangeSelect,\n    useWorkspaceDashboardFilterFromRoute\n  } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { secondsInDay, secondsToMilliseconds } from 'date-fns'\n  import { computed, provide } from 'vue'\n\n  provide(subscriptionIntervalKey, {\n    interval: secondsToMilliseconds(30),\n  })\n\n  const api = useWorkspaceApi()\n  const flowRunsCountAllSubscription = useSubscription(api.flowRuns.getFlowRunsCount, [{}])\n  const loaded = computed(() => flowRunsCountAllSubscription.executed)\n  const empty = computed(() => flowRunsCountAllSubscription.response === 0)\n  const crumbs: Crumb[] = [{ text: 'Dashboard' }]\n\n  const filter = useWorkspaceDashboardFilterFromRoute({\n    range: { type: 'span', seconds: -secondsInDay },\n    tags: [],\n  })\n\n  const tasksFilter: Getter<TaskRunsFilter> = () => mapper.map('WorkspaceDashboardFilter', filter, 'TaskRunsFilter')\n</script>\n\n<style>\n.workspace-dashboard__page-heading { @apply\n  grid\n  md:flex\n  md:flex-row\n  md:items-center\n  min-h-11\n}\n\n.workspace-dashboard__header-actions { @apply\n  flex\n  flex-col\n  w-full\n  max-w-full\n  gap-2\n  md:w-auto\n  md:inline-flex\n  md:flex-row\n  items-center\n}\n\n.workspace-dashboard__date-select { @apply\n  min-w-0\n}\n\n.workspace-dashboard__grid { @apply\n  grid\n  grid-cols-1\n  gap-4\n  items-start\n  xl:grid-cols-2\n}\n\n.workspace-dashboard__side { @apply\n  grid\n  grid-cols-1\n  gap-4\n}\n\n.workspace-dashboard__subflows-toggle { @apply\n  pr-2\n  w-full\n  md:w-auto\n}\n</style>"], "names": ["provide", "subscriptionIntervalKey", "secondsToMilliseconds", "api", "useWorkspaceApi", "flowRunsCountAllSubscription", "useSubscription", "loaded", "computed", "empty", "crumbs", "filter", "useWorkspaceDashboardFilterFromRoute", "tasksFilter", "mapper", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeading", "_createElementVNode", "_hoisted_1", "_hoisted_2", "_component_p_toggle", "_cache", "$event", "FlowRunTagsInput", "DateRangeSelect", "_createElementBlock", "_Fragment", "FlowRunsPageEmptyState", "_openBlock", "_hoisted_3", "WorkspaceDashboardFlowRunsCard", "_hoisted_4", "CumulativeTaskRunsCard", "DashboardWorkPoolsCard", "MarketingBanner", "_component_p_button"], "mappings": "ifAgEEA,EAAQC,EAAyB,CAC/B,SAAUC,EAAsB,EAAE,CAAA,CACnC,EAED,MAAMC,EAAMC,EAAgB,EACtBC,EAA+BC,EAAgBH,EAAI,SAAS,iBAAkB,CAAC,CAAA,CAAE,CAAC,EAClFI,EAASC,EAAS,IAAMH,EAA6B,QAAQ,EAC7DI,EAAQD,EAAS,IAAMH,EAA6B,WAAa,CAAC,EAClEK,EAAkB,CAAC,CAAE,KAAM,YAAa,EAExCC,EAASC,EAAqC,CAClD,MAAO,CAAE,KAAM,OAAQ,QAAS,MAAc,EAC9C,KAAM,CAAA,CAAC,CACR,EAEKC,EAAsC,IAAMC,EAAO,IAAI,2BAA4BH,EAAQ,gBAAgB,uFA9EjH,EAAAI,EAsCmBC,EAAA,CAtCD,MAAM,uBAAqB,CAChC,SACT,IAUc,CAVdC,EAUcC,EAAAC,CAAA,EAAA,CAVA,OAAAT,EAAgB,MAAM,8CAClBH,EAAA,QAAWE,EAAK,YAAG,eACjC,IAMM,CANNW,EAMM,MANNC,EAMM,CALJD,EAEM,MAFNE,EAEM,CADJL,EAAiEM,EAAA,CAA9C,WAAAL,EAAAP,CAAA,EAAO,aAAP,sBAAAa,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,GAAAP,EAAAP,CAAA,EAAO,aAAYc,GAAE,OAAO,eAAA,2BAEjDR,EAA4EC,EAAAQ,CAAA,EAAA,CAAlD,SAAUR,EAAMP,CAAA,EAAC,KAAP,oBAAAa,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,GAAAP,EAAAP,CAAA,EAAO,KAAIc,GAAE,gBAAc,iCAC/DR,EAAmFC,EAAAS,CAAA,EAAA,CAAzD,WAAAT,EAAAP,CAAA,EAAO,MAAP,sBAAAa,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,GAAAP,EAAAP,CAAA,EAAO,MAAKc,GAAE,MAAM,kCAAA,iEAKtD,IAaW,CAbKlB,EAAM,WAAtBqB,EAaWC,EAAA,CAAA,IAAA,GAAA,CAZOpB,EAAK,WACnBM,EAA0BG,EAAAY,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,IAG1BC,EAAA,EAAAH,EAMM,MANNI,EAMM,CALJf,EAAmDC,EAAAe,CAAA,EAAA,CAAlB,OAAQf,EAAMP,CAAA,CAAA,EAAA,KAAA,EAAA,CAAA,QAAA,CAAA,EAC/CS,EAGM,MAHNc,EAGM,CAFJjB,EAAgDC,EAAAiB,CAAA,EAAA,CAAvB,OAAQtB,EAAW,EAC5CI,EAAmFC,EAAAkB,CAAA,EAAA,CAA3D,MAAM,kCAAmC,OAAQlB,EAAMP,CAAA,CAAA,yCAKvFM,EASkBC,EAAAmB,CAAA,EAAA,CARhB,MAAM,kBACN,SAAS,2FAAA,GAEE,UACT,IAEW,CAFXpB,EAEWqB,EAAA,CAFD,GAAG,oHAAoH,OAAO,SAAS,QAAA,EAAA,aAAQ,IAEzJd,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFyJ,oBAEzJ,CAAA"}