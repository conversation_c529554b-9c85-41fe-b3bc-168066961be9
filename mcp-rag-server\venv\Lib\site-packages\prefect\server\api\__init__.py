from . import (
    artifacts,
    admin,
    automations,
    block_capabilities,
    block_documents,
    block_schemas,
    block_types,
    collections,
    concurrency_limits,
    concurrency_limits_v2,
    csrf_token,
    dependencies,
    deployments,
    events,
    flow_run_states,
    flow_runs,
    flows,
    logs,
    middleware,
    root,
    run_history,
    saved_searches,
    task_run_states,
    task_runs,
    task_workers,
    templates,
    ui,
    variables,
    work_queues,
    workers,
)
from . import server  # Server relies on all of the above routes
