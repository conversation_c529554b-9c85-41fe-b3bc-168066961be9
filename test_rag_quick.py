#!/usr/bin/env python3
"""Quick RAG Server Performance Test."""

import asyncio
import aiohttp
import time
import statistics
from datetime import datetime


async def test_api_performance():
    """Quick API performance test."""
    print('🚀 Quick RAG Server Performance Test')
    print('=' * 50)
    
    base_url = "http://localhost:8000"
    
    # Test endpoints
    endpoints = [
        ("/health/", "Health Check"),
        ("/api/search/test", "Search Test"),
        ("/api/search/simple?q=artificial%20intelligence&max_results=5", "Simple Search")
    ]
    
    results = {}
    
    async with aiohttp.ClientSession() as session:
        for endpoint, name in endpoints:
            print(f'\n🔍 Testing {name}...')
            
            latencies = []
            errors = 0
            num_requests = 20
            
            # Test latency
            for i in range(num_requests):
                start_time = time.time()
                try:
                    async with session.get(f"{base_url}{endpoint}") as response:
                        await response.text()
                        latency = (time.time() - start_time) * 1000  # ms
                        latencies.append(latency)
                        
                        if response.status != 200:
                            errors += 1
                            
                except Exception as e:
                    errors += 1
                    print(f'   Error: {e}')
            
            if latencies:
                avg_latency = statistics.mean(latencies)
                min_latency = min(latencies)
                max_latency = max(latencies)
                p95_latency = statistics.quantiles(latencies, n=20)[18] if len(latencies) > 20 else max_latency
                success_rate = (num_requests - errors) / num_requests * 100
                
                results[name] = {
                    "avg_latency_ms": avg_latency,
                    "min_latency_ms": min_latency,
                    "max_latency_ms": max_latency,
                    "p95_latency_ms": p95_latency,
                    "success_rate": success_rate,
                    "total_requests": num_requests,
                    "errors": errors
                }
                
                print(f'   ✅ Avg: {avg_latency:.2f}ms')
                print(f'   ✅ Min: {min_latency:.2f}ms')
                print(f'   ✅ Max: {max_latency:.2f}ms')
                print(f'   ✅ P95: {p95_latency:.2f}ms')
                print(f'   ✅ Success: {success_rate:.1f}%')
            else:
                print(f'   ❌ All requests failed')
                results[name] = {"error": "All requests failed"}
    
    return results


async def test_concurrent_load():
    """Test concurrent load."""
    print(f'\n⚡ Testing Concurrent Load...')
    
    base_url = "http://localhost:8000"
    concurrent_users = 5
    requests_per_user = 10
    
    async def user_session(user_id: int):
        """Simulate a user session."""
        latencies = []
        async with aiohttp.ClientSession() as session:
            for i in range(requests_per_user):
                start_time = time.time()
                try:
                    endpoint = f"/api/search/simple?q=user{user_id}_query{i}&max_results=3"
                    async with session.get(f"{base_url}{endpoint}") as response:
                        await response.text()
                        latency = (time.time() - start_time) * 1000
                        latencies.append(latency)
                except Exception as e:
                    print(f'   User {user_id} error: {e}')
        return latencies
    
    # Run concurrent users
    start_time = time.time()
    tasks = [user_session(i) for i in range(concurrent_users)]
    all_latencies = await asyncio.gather(*tasks)
    total_time = time.time() - start_time
    
    # Flatten all latencies
    flat_latencies = [lat for user_lats in all_latencies for lat in user_lats]
    total_requests = len(flat_latencies)
    
    if flat_latencies:
        throughput = total_requests / total_time
        avg_latency = statistics.mean(flat_latencies)
        p95_latency = statistics.quantiles(flat_latencies, n=20)[18] if len(flat_latencies) > 20 else max(flat_latencies)
        
        print(f'   ✅ Concurrent Users: {concurrent_users}')
        print(f'   ✅ Total Requests: {total_requests}')
        print(f'   ✅ Total Time: {total_time:.2f}s')
        print(f'   ✅ Throughput: {throughput:.2f} RPS')
        print(f'   ✅ Avg Latency: {avg_latency:.2f}ms')
        print(f'   ✅ P95 Latency: {p95_latency:.2f}ms')
        
        return {
            "concurrent_users": concurrent_users,
            "total_requests": total_requests,
            "total_time_seconds": total_time,
            "throughput_rps": throughput,
            "avg_latency_ms": avg_latency,
            "p95_latency_ms": p95_latency,
        }
    else:
        print(f'   ❌ Load test failed')
        return {"error": "Load test failed"}


async def test_search_queries():
    """Test various search queries."""
    print(f'\n🔍 Testing Search Queries...')
    
    base_url = "http://localhost:8000"
    
    test_queries = [
        "artificial intelligence",
        "machine learning",
        "natural language processing",
        "vector embeddings",
        "semantic search"
    ]
    
    search_latencies = []
    errors = 0
    
    async with aiohttp.ClientSession() as session:
        for query in test_queries:
            start_time = time.time()
            try:
                async with session.get(f"{base_url}/api/search/simple?q={query}&max_results=5") as response:
                    data = await response.json()
                    latency = (time.time() - start_time) * 1000
                    search_latencies.append(latency)
                    
                    print(f'   Query: "{query}" -> {latency:.2f}ms, {len(data.get("results", []))} results')
                    
                    if response.status != 200:
                        errors += 1
                        
            except Exception as e:
                errors += 1
                print(f'   Error with query "{query}": {e}')
    
    if search_latencies:
        avg_latency = statistics.mean(search_latencies)
        success_rate = len(search_latencies) / len(test_queries) * 100
        
        print(f'   ✅ Avg Search Latency: {avg_latency:.2f}ms')
        print(f'   ✅ Success Rate: {success_rate:.1f}%')
        
        return {
            "total_queries": len(test_queries),
            "avg_search_latency_ms": avg_latency,
            "success_rate": success_rate,
            "errors": errors
        }
    else:
        print(f'   ❌ All search queries failed')
        return {"error": "All search queries failed"}


def generate_quick_report(api_results, load_results, search_results):
    """Generate a quick performance report."""
    print('\n🎯 QUICK PERFORMANCE REPORT')
    print('=' * 40)
    print(f'📅 Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    # Overall assessment
    scores = []
    
    # API Score
    if api_results:
        api_success = sum(1 for result in api_results.values() 
                         if "error" not in result and result.get("success_rate", 0) > 95)
        api_score = (api_success / len(api_results)) * 100
        scores.append(api_score)
        print(f'\n🌐 API Performance: {api_score:.0f}%')
    
    # Load Score
    if load_results and "error" not in load_results:
        load_score = 100 if load_results["throughput_rps"] > 50 else 75
        scores.append(load_score)
        print(f'⚡ Load Performance: {load_score:.0f}%')
        print(f'   Throughput: {load_results["throughput_rps"]:.1f} RPS')
    
    # Search Score
    if search_results and "error" not in search_results:
        search_score = search_results["success_rate"]
        scores.append(search_score)
        print(f'🔍 Search Performance: {search_score:.0f}%')
        print(f'   Avg Latency: {search_results["avg_search_latency_ms"]:.1f}ms')
    
    # Overall Score
    if scores:
        overall_score = statistics.mean(scores)
        print(f'\n🎯 OVERALL SCORE: {overall_score:.1f}/100')
        
        if overall_score >= 90:
            print('🟢 EXCELLENT - System performing optimally!')
        elif overall_score >= 75:
            print('🟡 GOOD - System performing well!')
        elif overall_score >= 50:
            print('🟠 FAIR - System functional, needs optimization')
        else:
            print('🔴 POOR - System needs immediate attention')
    
    print('\n✅ Quick performance test completed!')


async def main():
    """Main function."""
    print('🚀 MCP RAG Server - Quick Performance Test')
    print('=' * 60)
    
    start_time = time.time()
    
    try:
        # Run quick tests
        api_results = await test_api_performance()
        load_results = await test_concurrent_load()
        search_results = await test_search_queries()
        
        total_time = time.time() - start_time
        print(f'\n⏱️  Total test time: {total_time:.2f} seconds')
        
        # Generate report
        generate_quick_report(api_results, load_results, search_results)
        
    except Exception as e:
        print(f'\n❌ Test failed: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
