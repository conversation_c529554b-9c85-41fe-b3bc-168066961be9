import{d as V,V as B,u as D,W as N,f as T,g as n,i as s,c as _,a as q,o as l,j as e,k as a,l as d,n as o,p as F,q as I,t as P,x as U,F as W,v as j,y as w,z,C as E,A as L}from"./index-ei-kaitd.js";import{u as R}from"./usePageTitle-LeBMnqrg.js";import{u as S}from"./usePrefectApi-qsKG6mzx.js";import"./api-DGOAIix_.js";import"./mapper-BuxGYc8V.js";const $={class:"automation-card__label"},Q=V({__name:"Automation",setup(G){const c=B(),f=D(),v=S(),g=N("automationId"),i=T(v.automations.getAutomation,[g]),t=n(()=>i.response),y=n(()=>{var u;return((u=t.value)==null?void 0:u.name)??""}),A=n(()=>[{text:"Automations",to:c.automations()},{text:y.value}]),k=n(()=>t.value?`Automation: ${t.value.name}`:"Automation");R(k);function b(){f.push(c.automations())}return(u,m)=>{const x=s("p-key-value"),r=s("p-content"),C=s("p-card"),h=s("p-layout-default");return t.value?(l(),_(h,{key:0,class:"automation"},{header:e(()=>[a(o(z),{crumbs:A.value},{actions:e(()=>[a(o(E),{automation:t.value,onUpdate:o(i).refresh},null,8,["automation","onUpdate"]),a(o(L),{automation:t.value,onDelete:b},null,8,["automation"])]),_:1},8,["crumbs"])]),default:e(()=>[a(r,null,{default:e(()=>[a(x,{label:"Description",value:t.value.description},null,8,["value"]),a(r,{secondary:""},{default:e(()=>[m[0]||(m[0]=d("span",{class:"automation-card__label"},"Trigger",-1)),a(o(F),{trigger:t.value.trigger},null,8,["trigger"])]),_:1,__:[0]}),a(r,{secondary:""},{default:e(()=>[d("span",$,P(o(U)("Action",t.value.actions.length)),1),(l(!0),I(W,null,j(t.value.actions,p=>(l(),_(C,{key:p.id},{default:e(()=>[a(o(w),{action:p},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})):q("",!0)}}});export{Q as default};
//# sourceMappingURL=Automation-wIt15gU_.js.map
