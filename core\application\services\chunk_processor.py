"""Chunk processor for creating and managing document chunks."""

import asyncio
import logging
from typing import List, Optional, Dict, Any
from datetime import datetime

from .document_service import DocumentService
from .chunk_service import ChunkService
from .embedding_service import EmbeddingService
from ...domain.entities import Document, Chunk, DocumentStatus


class ChunkProcessor:
    """Processes documents into chunks and generates embeddings."""
    
    def __init__(
        self,
        document_service: DocumentService,
        chunk_service: ChunkService,
        embedding_service: EmbeddingService,
        chunk_size: int = 1000,
        chunk_overlap: int = 200,
        max_concurrent: int = 5,
    ):
        self.document_service = document_service
        self.chunk_service = chunk_service
        self.embedding_service = embedding_service
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.max_concurrent = max_concurrent
        
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            "documents_processed": 0,
            "documents_failed": 0,
            "chunks_created": 0,
            "embeddings_generated": 0,
            "total_processing_time": 0.0,
        }
    
    async def process_document(self, document_id: str) -> bool:
        """Process a single document into chunks with embeddings."""
        async with self._semaphore:
            import time
            start_time = time.time()
            
            try:
                # Get document
                document = await self.document_service.get_document(document_id)
                if not document:
                    self.logger.error(f"Document {document_id} not found")
                    return False
                
                if not document.content or not document.content.strip():
                    self.logger.warning(f"Document {document_id} has no content")
                    return False
                
                # Update document status to processing
                await self.document_service.update_document_status(
                    document_id, DocumentStatus.PROCESSING
                )
                
                # Delete existing chunks
                await self.chunk_service.delete_chunks_by_document(document_id)
                
                # Create chunks
                chunks = await self.chunk_service.create_chunks_from_text(
                    document_id=document_id,
                    text=document.content,
                    chunk_size=self.chunk_size,
                    overlap=self.chunk_overlap,
                    metadata={
                        "document_url": document.url,
                        "document_type": document.document_type.value,
                        "created_at": datetime.utcnow().isoformat(),
                    }
                )
                
                if not chunks:
                    self.logger.warning(f"No chunks created for document {document_id}")
                    await self.document_service.update_document_status(
                        document_id, DocumentStatus.FAILED, "No chunks created"
                    )
                    return False
                
                # Generate embeddings
                await self.embedding_service.embed_chunks_batch(chunks)
                
                # Mark document as completed
                await self.document_service.mark_document_processed(
                    document_id=document_id,
                    embedding_model=self.embedding_service.model,
                    chunk_count=len(chunks),
                )
                
                # Update statistics
                processing_time = time.time() - start_time
                self.stats["documents_processed"] += 1
                self.stats["chunks_created"] += len(chunks)
                self.stats["embeddings_generated"] += len(chunks)
                self.stats["total_processing_time"] += processing_time
                
                self.logger.info(
                    f"Processed document {document_id}: {len(chunks)} chunks, "
                    f"{processing_time:.2f}s"
                )
                
                return True
                
            except Exception as e:
                self.stats["documents_failed"] += 1
                error_msg = str(e)
                self.logger.error(f"Error processing document {document_id}: {error_msg}")
                
                # Mark document as failed
                await self.document_service.mark_document_failed(document_id, error_msg)
                
                return False
    
    async def process_pending_documents(self, limit: int = 10) -> Dict[str, Any]:
        """Process all pending documents."""
        self.logger.info(f"Processing up to {limit} pending documents")
        
        # Get documents needing processing
        documents = await self.document_service.get_documents_needing_processing(limit)
        
        if not documents:
            self.logger.info("No documents need processing")
            return {
                "processed": 0,
                "failed": 0,
                "total": 0,
                "message": "No documents need processing"
            }
        
        self.logger.info(f"Found {len(documents)} documents to process")
        
        # Process documents concurrently
        tasks = [self.process_document(doc.id) for doc in documents]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count results
        processed = sum(1 for r in results if r is True)
        failed = sum(1 for r in results if r is False or isinstance(r, Exception))
        
        self.logger.info(f"Processed {processed} documents, {failed} failed")
        
        return {
            "processed": processed,
            "failed": failed,
            "total": len(documents),
            "success_rate": processed / len(documents) * 100 if documents else 0,
        }
    
    async def process_documents_by_crawl_session(
        self,
        crawl_session_id: str,
        limit: Optional[int] = None,
    ) -> Dict[str, Any]:
        """Process all documents from a specific crawl session."""
        self.logger.info(f"Processing documents from crawl session {crawl_session_id}")
        
        # Get documents from crawl session
        documents = await self.document_service.get_documents_by_crawl_session(
            crawl_session_id
        )
        
        if limit:
            documents = documents[:limit]
        
        if not documents:
            return {
                "processed": 0,
                "failed": 0,
                "total": 0,
                "message": "No documents found for crawl session"
            }
        
        # Process documents
        tasks = [self.process_document(doc.id) for doc in documents]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Count results
        processed = sum(1 for r in results if r is True)
        failed = sum(1 for r in results if r is False or isinstance(r, Exception))
        
        return {
            "processed": processed,
            "failed": failed,
            "total": len(documents),
            "crawl_session_id": crawl_session_id,
            "success_rate": processed / len(documents) * 100 if documents else 0,
        }
    
    async def reprocess_document(self, document_id: str) -> bool:
        """Reprocess a document (useful for failed documents)."""
        self.logger.info(f"Reprocessing document {document_id}")
        
        # Reset document status
        await self.document_service.update_document_status(
            document_id, DocumentStatus.PENDING
        )
        
        # Process document
        return await self.process_document(document_id)
    
    async def process_chunks_without_embeddings(self, limit: int = 100) -> int:
        """Process chunks that don't have embeddings."""
        self.logger.info(f"Processing up to {limit} chunks without embeddings")
        
        chunks = await self.chunk_service.get_chunks_without_embeddings(
            limit=limit,
            embedding_model=self.embedding_service.model
        )
        
        if not chunks:
            self.logger.info("No chunks need embeddings")
            return 0
        
        # Generate embeddings
        await self.embedding_service.embed_chunks_batch(chunks)
        
        self.stats["embeddings_generated"] += len(chunks)
        
        self.logger.info(f"Generated embeddings for {len(chunks)} chunks")
        return len(chunks)
    
    async def update_embeddings_for_new_model(
        self,
        new_model: str,
        limit: int = 100,
    ) -> int:
        """Update embeddings when switching to a new model."""
        self.logger.info(f"Updating embeddings to new model: {new_model}")
        
        old_model = self.embedding_service.model
        
        try:
            # Update embedding service model
            self.embedding_service.model = new_model
            
            # Get chunks with old model
            chunks = await self.chunk_service.get_chunks_by_embedding_model(old_model)
            
            if not chunks:
                return 0
            
            # Process in batches
            processed = 0
            for i in range(0, len(chunks), limit):
                batch = chunks[i:i + limit]
                await self.embedding_service.embed_chunks_batch(batch)
                processed += len(batch)
                
                self.logger.info(f"Updated {processed}/{len(chunks)} chunks")
            
            return processed
            
        finally:
            # Restore original model if something went wrong
            if self.embedding_service.model != new_model:
                self.embedding_service.model = old_model
    
    async def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics."""
        # Get chunk statistics
        chunk_stats = await self.chunk_service.get_statistics()
        
        # Get embedding statistics
        embedding_stats = await self.embedding_service.get_embedding_statistics()
        
        # Combine with processor statistics
        return {
            **self.stats,
            "average_processing_time": (
                self.stats["total_processing_time"] / max(1, self.stats["documents_processed"])
            ),
            "chunk_statistics": chunk_stats,
            "embedding_statistics": embedding_stats,
        }
    
    async def validate_processing_quality(self, sample_size: int = 50) -> Dict[str, Any]:
        """Validate the quality of processing."""
        # Get random chunks
        chunks = await self.chunk_service.get_random_chunks(sample_size)
        
        if not chunks:
            return {"error": "No chunks found for validation"}
        
        # Check for issues
        issues = {
            "chunks_without_content": 0,
            "chunks_without_embeddings": 0,
            "chunks_too_short": 0,
            "chunks_too_long": 0,
            "embedding_dimension_issues": 0,
        }
        
        for chunk in chunks:
            if not chunk.content or not chunk.content.strip():
                issues["chunks_without_content"] += 1
            
            if not chunk.embedding:
                issues["chunks_without_embeddings"] += 1
            
            if len(chunk.content) < 50:
                issues["chunks_too_short"] += 1
            
            if len(chunk.content) > self.chunk_size * 1.5:
                issues["chunks_too_long"] += 1
            
            if chunk.embedding and len(chunk.embedding) != 1536:  # Expected dimension
                issues["embedding_dimension_issues"] += 1
        
        # Calculate quality score
        total_issues = sum(issues.values())
        quality_score = max(0, 100 - (total_issues / len(chunks) * 100))
        
        return {
            "sample_size": len(chunks),
            "quality_score": quality_score,
            "issues": issues,
            "recommendations": self._get_quality_recommendations(issues, len(chunks)),
        }
    
    def _get_quality_recommendations(self, issues: Dict[str, int], sample_size: int) -> List[str]:
        """Get recommendations based on quality issues."""
        recommendations = []
        
        if issues["chunks_without_content"] > 0:
            recommendations.append("Some chunks have no content - check document processing")
        
        if issues["chunks_without_embeddings"] > sample_size * 0.1:
            recommendations.append("Many chunks missing embeddings - run embedding generation")
        
        if issues["chunks_too_short"] > sample_size * 0.2:
            recommendations.append("Many chunks are too short - consider adjusting chunk size")
        
        if issues["chunks_too_long"] > sample_size * 0.1:
            recommendations.append("Some chunks are too long - check chunking algorithm")
        
        if issues["embedding_dimension_issues"] > 0:
            recommendations.append("Embedding dimension issues - check model consistency")
        
        return recommendations
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processor statistics."""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset processor statistics."""
        self.stats = {
            "documents_processed": 0,
            "documents_failed": 0,
            "chunks_created": 0,
            "embeddings_generated": 0,
            "total_processing_time": 0.0,
        }
