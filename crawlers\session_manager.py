"""Crawl session manager for coordinating crawlers."""

import asyncio
import uuid
from typing import List, Optional, Dict, Any, Set
from datetime import datetime
from urllib.parse import urlparse
import logging

from core.domain.entities import CrawlSession, CrawlStatus, CrawlStrategy, Document
from core.application.services import DocumentService
from .html import AiohttpCrawler
from .pdf import SimplePDFCrawler


class CrawlSessionManager:
    """Manages crawl sessions and coordinates different crawlers."""
    
    def __init__(
        self,
        document_service: DocumentService,
        max_concurrent_sessions: int = 3,
        max_pages_per_session: int = 100,
        max_depth: int = 3,
    ):
        self.document_service = document_service
        self.max_concurrent_sessions = max_concurrent_sessions
        self.max_pages_per_session = max_pages_per_session
        self.max_depth = max_depth
        
        # Initialize crawlers
        self.html_crawler = AiohttpCrawler(
            document_service=document_service,
            max_concurrent=8,
            delay_range=(1.0, 3.0),
        )
        
        self.pdf_crawler = SimplePDFCrawler(
            document_service=document_service,
        )
        
        # Active sessions
        self.active_sessions: Dict[str, CrawlSession] = {}
        self._session_semaphore = asyncio.Semaphore(max_concurrent_sessions)
        
        self.logger = logging.getLogger(__name__)
    
    async def create_session(
        self,
        name: str,
        start_urls: List[str],
        strategy: CrawlStrategy = CrawlStrategy.BREADTH_FIRST,
        allowed_domains: Optional[List[str]] = None,
        max_pages: Optional[int] = None,
        max_depth: Optional[int] = None,
        description: Optional[str] = None,
        created_by: Optional[str] = None,
    ) -> CrawlSession:
        """Create a new crawl session."""
        session_id = str(uuid.uuid4())
        
        if allowed_domains is None:
            allowed_domains = [urlparse(url).netloc for url in start_urls]
        
        session = CrawlSession(
            id=session_id,
            name=name,
            start_urls=start_urls,
            strategy=strategy,
            description=description,
            created_by=created_by,
        )
        
        # Set configuration
        session.config = {
            "allowed_domains": allowed_domains,
            "max_pages": max_pages or self.max_pages_per_session,
            "max_depth": max_depth or self.max_depth,
            "crawl_pdfs": True,
            "follow_external_links": False,
        }
        
        # Initialize URL queues
        session.urls_to_crawl = start_urls.copy()
        session.urls_crawled = []
        session.urls_failed = []
        session.urls_skipped = []
        
        self.active_sessions[session_id] = session
        
        self.logger.info(f"Created crawl session: {session_id} - {name}")
        return session
    
    async def start_session(self, session_id: str) -> bool:
        """Start a crawl session."""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        
        if session.status != CrawlStatus.PENDING:
            return False
        
        session.status = CrawlStatus.RUNNING
        session.started_at = datetime.utcnow()
        
        # Start crawling in background
        asyncio.create_task(self._execute_session(session))
        
        self.logger.info(f"Started crawl session: {session_id}")
        return True
    
    async def _execute_session(self, session: CrawlSession):
        """Execute a crawl session."""
        async with self._session_semaphore:
            try:
                await self._crawl_session(session)
                session.status = CrawlStatus.COMPLETED
                session.completed_at = datetime.utcnow()
                
            except Exception as e:
                session.status = CrawlStatus.FAILED
                session.error_message = str(e)
                session.last_error_at = datetime.utcnow()
                self.logger.error(f"Session {session.id} failed: {e}")
            
            finally:
                # Update statistics
                session.stats = {
                    "pages_crawled": len(session.urls_crawled),
                    "pages_failed": len(session.urls_failed),
                    "pages_skipped": len(session.urls_skipped),
                    "total_pages": len(session.urls_crawled) + len(session.urls_failed) + len(session.urls_skipped),
                    "success_rate": len(session.urls_crawled) / max(1, len(session.urls_crawled) + len(session.urls_failed)) * 100,
                }
                
                self.logger.info(f"Session {session.id} completed with {session.stats['pages_crawled']} pages")
    
    async def _crawl_session(self, session: CrawlSession):
        """Execute the actual crawling for a session."""
        max_pages = session.config.get("max_pages", self.max_pages_per_session)
        max_depth = session.config.get("max_depth", self.max_depth)
        allowed_domains = session.config.get("allowed_domains", [])
        
        visited_urls: Set[str] = set()
        current_depth = 0
        
        while (session.urls_to_crawl and 
               len(session.urls_crawled) < max_pages and 
               current_depth < max_depth and
               session.status == CrawlStatus.RUNNING):
            
            # Get URLs for current batch
            batch_size = min(8, len(session.urls_to_crawl))
            current_batch = session.urls_to_crawl[:batch_size]
            session.urls_to_crawl = session.urls_to_crawl[batch_size:]
            
            # Process batch
            await self._process_url_batch(session, current_batch, visited_urls, allowed_domains)
            
            current_depth += 1
            session.current_depth = current_depth
            
            # Small delay between batches
            await asyncio.sleep(1.0)
    
    async def _process_url_batch(
        self,
        session: CrawlSession,
        urls: List[str],
        visited_urls: Set[str],
        allowed_domains: List[str],
    ):
        """Process a batch of URLs."""
        html_urls = []
        pdf_urls = []
        
        # Categorize URLs
        for url in urls:
            if url in visited_urls:
                session.urls_skipped.append(url)
                continue
            
            visited_urls.add(url)
            
            if url.lower().endswith('.pdf'):
                pdf_urls.append(url)
            else:
                html_urls.append(url)
        
        # Process HTML URLs
        if html_urls:
            await self._process_html_urls(session, html_urls, visited_urls, allowed_domains)
        
        # Process PDF URLs
        if pdf_urls:
            await self._process_pdf_urls(session, pdf_urls)
    
    async def _process_html_urls(
        self,
        session: CrawlSession,
        urls: List[str],
        visited_urls: Set[str],
        allowed_domains: List[str],
    ):
        """Process HTML URLs."""
        try:
            results = await self.html_crawler.crawl_urls(urls, session.id)
            
            for result in results:
                if result.error:
                    session.urls_failed.append(result.url)
                    self.logger.warning(f"Failed to crawl {result.url}: {result.error}")
                else:
                    session.urls_crawled.append(result.url)
                    
                    # Extract new URLs
                    new_urls = self._filter_urls(result.links, allowed_domains, visited_urls)
                    session.urls_to_crawl.extend(new_urls)
                    
        except Exception as e:
            self.logger.error(f"Error processing HTML URLs: {e}")
            session.urls_failed.extend(urls)
    
    async def _process_pdf_urls(self, session: CrawlSession, urls: List[str]):
        """Process PDF URLs."""
        for url in urls:
            try:
                result = await self.pdf_crawler.download_and_process_pdf(url, session.id)
                
                if result.error:
                    session.urls_failed.append(url)
                    self.logger.warning(f"Failed to process PDF {url}: {result.error}")
                else:
                    session.urls_crawled.append(url)
                    
            except Exception as e:
                self.logger.error(f"Error processing PDF {url}: {e}")
                session.urls_failed.append(url)
    
    def _filter_urls(
        self,
        urls: List[str],
        allowed_domains: List[str],
        visited_urls: Set[str],
    ) -> List[str]:
        """Filter URLs based on domain and visited status."""
        filtered = []
        
        for url in urls:
            if url in visited_urls:
                continue
            
            parsed = urlparse(url)
            
            # Check domain
            if parsed.netloc not in allowed_domains:
                continue
            
            # Skip certain file types
            skip_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg',
                              '.mp3', '.mp4', '.avi', '.mov', '.zip', '.rar',
                              '.tar', '.gz', '.exe', '.dmg'}
            
            if any(url.lower().endswith(ext) for ext in skip_extensions):
                continue
            
            filtered.append(url)
        
        return filtered
    
    async def pause_session(self, session_id: str) -> bool:
        """Pause a crawl session."""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        
        if session.status == CrawlStatus.RUNNING:
            session.status = CrawlStatus.PAUSED
            self.logger.info(f"Paused crawl session: {session_id}")
            return True
        
        return False
    
    async def resume_session(self, session_id: str) -> bool:
        """Resume a paused crawl session."""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        
        if session.status == CrawlStatus.PAUSED:
            session.status = CrawlStatus.RUNNING
            # Restart execution
            asyncio.create_task(self._execute_session(session))
            self.logger.info(f"Resumed crawl session: {session_id}")
            return True
        
        return False
    
    async def stop_session(self, session_id: str) -> bool:
        """Stop a crawl session."""
        if session_id not in self.active_sessions:
            return False
        
        session = self.active_sessions[session_id]
        session.status = CrawlStatus.CANCELLED
        session.completed_at = datetime.utcnow()
        
        self.logger.info(f"Stopped crawl session: {session_id}")
        return True
    
    def get_session(self, session_id: str) -> Optional[CrawlSession]:
        """Get a crawl session by ID."""
        return self.active_sessions.get(session_id)
    
    def list_sessions(self) -> List[CrawlSession]:
        """List all active sessions."""
        return list(self.active_sessions.values())
    
    def get_session_statistics(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get statistics for a specific session."""
        session = self.active_sessions.get(session_id)
        if not session:
            return None
        
        return {
            "session_id": session_id,
            "name": session.name,
            "status": session.status.value,
            "created_at": session.created_at.isoformat(),
            "started_at": session.started_at.isoformat() if session.started_at else None,
            "completed_at": session.completed_at.isoformat() if session.completed_at else None,
            "current_depth": session.current_depth,
            "urls_to_crawl": len(session.urls_to_crawl),
            "urls_crawled": len(session.urls_crawled),
            "urls_failed": len(session.urls_failed),
            "urls_skipped": len(session.urls_skipped),
            "stats": session.stats,
        }
    
    def get_global_statistics(self) -> Dict[str, Any]:
        """Get global statistics across all sessions."""
        total_sessions = len(self.active_sessions)
        running_sessions = sum(1 for s in self.active_sessions.values() if s.status == CrawlStatus.RUNNING)
        completed_sessions = sum(1 for s in self.active_sessions.values() if s.status == CrawlStatus.COMPLETED)
        failed_sessions = sum(1 for s in self.active_sessions.values() if s.status == CrawlStatus.FAILED)
        
        total_pages_crawled = sum(len(s.urls_crawled) for s in self.active_sessions.values())
        total_pages_failed = sum(len(s.urls_failed) for s in self.active_sessions.values())
        
        return {
            "total_sessions": total_sessions,
            "running_sessions": running_sessions,
            "completed_sessions": completed_sessions,
            "failed_sessions": failed_sessions,
            "total_pages_crawled": total_pages_crawled,
            "total_pages_failed": total_pages_failed,
            "success_rate": total_pages_crawled / max(1, total_pages_crawled + total_pages_failed) * 100,
            "html_crawler_stats": self.html_crawler.get_statistics(),
            "pdf_crawler_stats": self.pdf_crawler.get_statistics(),
        }
