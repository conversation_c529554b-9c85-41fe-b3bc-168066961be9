"""Settings configuration for the MCP RAG Server."""

import os
from functools import lru_cache
from typing import Optional
from pydantic import Field
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """Application settings."""
    
    # API Configuration
    api_host: str = Field(default="0.0.0.0", description="API host")
    api_port: int = Field(default=8000, description="API port")
    api_workers: int = Field(default=4, description="Number of API workers")
    api_reload: bool = Field(default=False, description="Enable auto-reload")
    
    # Database Configuration
    supabase_url: str = Field(..., description="Supabase URL")
    supabase_service_key: str = Field(..., description="Supabase service key")
    supabase_anon_key: str = Field(..., description="Supabase anonymous key")
    
    # Database Pool Settings
    database_pool_size: int = Field(default=10, description="Database pool size")
    database_max_overflow: int = Field(default=20, description="Database max overflow")
    database_pool_timeout: int = Field(default=30, description="Database pool timeout")
    
    # OpenAI Configuration
    openai_api_key: str = Field(..., description="OpenAI API key")
    openai_model: str = Field(default="text-embedding-3-small", description="OpenAI embedding model")
    openai_max_tokens: int = Field(default=8192, description="OpenAI max tokens")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379/0", description="Redis URL")
    redis_password: Optional[str] = Field(default=None, description="Redis password")
    redis_db: int = Field(default=0, description="Redis database number")
    
    # Security
    secret_key: str = Field(..., description="Secret key for encryption")
    encryption_key: str = Field(..., description="Encryption key (32 characters)")
    
    # Crawling Configuration
    crawl_delay_min: float = Field(default=1.0, description="Minimum crawl delay")
    crawl_delay_max: float = Field(default=3.0, description="Maximum crawl delay")
    crawl_concurrent_requests: int = Field(default=8, description="Concurrent crawl requests")
    crawl_user_agent: str = Field(default="MCP-RAG-Server/1.0", description="Crawl user agent")
    
    # Monitoring
    prometheus_port: int = Field(default=9090, description="Prometheus metrics port")
    log_level: str = Field(default="INFO", description="Logging level")
    structured_logging: bool = Field(default=True, description="Enable structured logging")
    
    # Environment
    environment: str = Field(default="development", description="Environment name")
    debug: bool = Field(default=False, description="Debug mode")
    
    # Rate Limiting
    rate_limit_requests: int = Field(default=100, description="Rate limit requests per window")
    rate_limit_window: int = Field(default=60, description="Rate limit window in seconds")
    
    # Prefect Configuration
    prefect_api_url: str = Field(default="http://localhost:4200/api", description="Prefect API URL")
    prefect_logging_level: str = Field(default="INFO", description="Prefect logging level")
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


@lru_cache()
def get_settings() -> Settings:
    """Get cached settings instance."""
    return Settings()
