#!/usr/bin/env python3
"""Comprehensive RAG test for eufunds.bg crawling and analysis."""

import asyncio
import time
import json
from datetime import datetime
from typing import List, Dict, Any

from crawlers.session_manager import CrawlSessionManager
from core.application.services import (
    DocumentService, ChunkService, EmbeddingService
)
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.infrastructure.database.supabase_chunk_repository import SupabaseChunkRepository
from core.domain.entities import CrawlStrategy, DocumentType
from config.settings import get_settings


class EUFundsRAGTester:
    """Comprehensive RAG tester for eufunds.bg."""
    
    def __init__(self):
        self.settings = get_settings()
        self.setup_services()
        self.crawl_session_id = None
        self.test_questions = [
            "Какви са условията за кандидатстване за европейски фондове?",
            "Кои са основните програми за финансиране в България?",
            "Какви документи са необходими за подаване на проект?",
            "Каква е максималната сума за финансиране?",
            "Кога е крайният срок за кандидатстване?",
            "Какви са критериите за оценка на проектите?",
            "Кои сектори могат да кандидатстват за финансиране?",
            "Какъв е процесът на одобрение на проектите?",
            "Какви са задълженията на бенефициентите?",
            "Как се извършва мониторингът на проектите?"
        ]
        
    def setup_services(self):
        """Setup all required services."""
        # Repositories
        self.doc_repository = SupabaseDocumentRepository(
            self.settings.supabase_url, 
            self.settings.supabase_service_key
        )
        self.chunk_repository = SupabaseChunkRepository(
            self.settings.supabase_url, 
            self.settings.supabase_service_key
        )
        
        # Services
        self.doc_service = DocumentService(self.doc_repository)
        self.chunk_service = ChunkService(self.chunk_repository)
        self.embedding_service = EmbeddingService(
            chunk_repository=self.chunk_repository,
            openai_api_key=self.settings.openai_api_key,
            model='text-embedding-3-small'
        )
        
        # Session manager
        self.session_manager = CrawlSessionManager(
            document_service=self.doc_service,
            max_concurrent_sessions=1,
            max_pages_per_session=20,  # Increased for depth 2
            max_depth=2,
        )
        
        # Chunk processor
        self.chunk_processor = ChunkProcessor(
            document_service=self.doc_service,
            chunk_service=self.chunk_service,
            embedding_service=self.embedding_service,
            chunk_size=800,  # Larger chunks for better context
            chunk_overlap=200,
        )
    
    async def crawl_eufunds(self) -> Dict[str, Any]:
        """Crawl eufunds.bg with depth 2."""
        print('🕷️  PHASE 1: Crawling eufunds.bg')
        print('=' * 50)
        
        start_time = time.time()
        
        # Create crawl session
        session = await self.session_manager.create_session(
            name="EUFunds.bg Deep Crawl",
            start_urls=["https://eufunds.bg"],
            strategy=CrawlStrategy.BREADTH_FIRST,
            allowed_domains=["eufunds.bg"],
            max_pages=20,
            max_depth=2,
            description="Deep crawl of eufunds.bg for RAG testing",
            created_by="rag_tester",
        )
        
        self.crawl_session_id = session.id
        print(f'✅ Created crawl session: {session.id}')
        
        # Start crawling
        started = await self.session_manager.start_session(session.id)
        
        if not started:
            return {"error": "Failed to start crawl session"}
        
        print('🚀 Crawling started...')
        
        # Wait for completion
        for i in range(120):  # Wait up to 2 minutes
            await asyncio.sleep(1)
            
            if i % 10 == 0:  # Progress every 10 seconds
                stats = self.session_manager.get_session_statistics(session.id)
                if stats:
                    print(f'   Progress: {stats["urls_crawled"]} crawled, {stats["urls_to_crawl"]} remaining')
            
            current_session = self.session_manager.get_session(session.id)
            if current_session and current_session.status.value in ['completed', 'failed', 'cancelled']:
                break
        
        crawl_time = time.time() - start_time
        
        # Get final statistics
        final_stats = self.session_manager.get_session_statistics(session.id)
        
        if final_stats:
            print(f'\n📊 Crawl Results:')
            print(f'   Status: {final_stats["status"]}')
            print(f'   Pages crawled: {final_stats["urls_crawled"]}')
            print(f'   Pages failed: {final_stats["urls_failed"]}')
            print(f'   Pages skipped: {final_stats["urls_skipped"]}')
            print(f'   Crawl time: {crawl_time:.2f}s')
            
            if final_stats.get("stats"):
                print(f'   Success rate: {final_stats["stats"].get("success_rate", 0):.1f}%')
        
        return final_stats or {"error": "No statistics available"}
    
    async def analyze_crawled_data(self) -> Dict[str, Any]:
        """Analyze the quality of crawled data."""
        print('\n🔍 PHASE 2: Analyzing Crawled Data Quality')
        print('=' * 50)
        
        if not self.crawl_session_id:
            return {"error": "No crawl session available"}
        
        # Get documents from crawl session
        documents = await self.doc_service.get_documents_by_crawl_session(self.crawl_session_id)
        
        if not documents:
            print('❌ No documents found from crawl session')
            return {"error": "No documents found"}
        
        print(f'📄 Found {len(documents)} documents')
        
        # Analyze document quality
        analysis = {
            "total_documents": len(documents),
            "documents_with_content": 0,
            "total_content_length": 0,
            "avg_content_length": 0,
            "content_quality_score": 0,
            "document_types": {},
            "sample_content": []
        }
        
        for doc in documents:
            if doc.content and doc.content.strip():
                analysis["documents_with_content"] += 1
                analysis["total_content_length"] += len(doc.content)
                
                # Document type distribution
                doc_type = doc.document_type.value
                analysis["document_types"][doc_type] = analysis["document_types"].get(doc_type, 0) + 1
                
                # Sample content (first 200 chars)
                if len(analysis["sample_content"]) < 3:
                    analysis["sample_content"].append({
                        "url": doc.url,
                        "content_preview": doc.content[:200] + "..." if len(doc.content) > 200 else doc.content,
                        "content_length": len(doc.content)
                    })
        
        if analysis["documents_with_content"] > 0:
            analysis["avg_content_length"] = analysis["total_content_length"] / analysis["documents_with_content"]
            analysis["content_quality_score"] = min(100, (analysis["documents_with_content"] / len(documents)) * 100)
        
        print(f'📊 Data Quality Analysis:')
        print(f'   Documents with content: {analysis["documents_with_content"]}/{analysis["total_documents"]}')
        print(f'   Average content length: {analysis["avg_content_length"]:.0f} chars')
        print(f'   Content quality score: {analysis["content_quality_score"]:.1f}%')
        print(f'   Document types: {analysis["document_types"]}')
        
        # Show sample content
        print(f'\n📝 Sample Content:')
        for i, sample in enumerate(analysis["sample_content"], 1):
            print(f'   {i}. {sample["url"]} ({sample["content_length"]} chars)')
            print(f'      "{sample["content_preview"]}"')
        
        return analysis
    
    async def process_documents_to_chunks(self) -> Dict[str, Any]:
        """Process documents into chunks with embeddings."""
        print('\n🧩 PHASE 3: Processing Documents to Chunks')
        print('=' * 50)
        
        if not self.crawl_session_id:
            return {"error": "No crawl session available"}
        
        start_time = time.time()
        
        # Process documents from crawl session
        result = await self.chunk_processor.process_documents_by_crawl_session(
            self.crawl_session_id,
            limit=None  # Process all documents
        )
        
        processing_time = time.time() - start_time
        
        print(f'📊 Processing Results:')
        print(f'   Documents processed: {result["processed"]}')
        print(f'   Documents failed: {result["failed"]}')
        print(f'   Success rate: {result["success_rate"]:.1f}%')
        print(f'   Processing time: {processing_time:.2f}s')
        
        # Get chunk statistics
        chunk_stats = await self.chunk_service.get_statistics()
        print(f'\n🧩 Chunk Statistics:')
        print(f'   Total chunks: {chunk_stats["total_chunks"]}')
        print(f'   Chunks with embeddings: {chunk_stats["chunks_with_embeddings"]}')
        
        result["processing_time_seconds"] = processing_time
        result["chunk_statistics"] = chunk_stats
        
        return result
    
    async def test_rag_retrieval(self) -> Dict[str, Any]:
        """Test RAG retrieval with predefined questions."""
        print('\n🔍 PHASE 4: Testing RAG Retrieval')
        print('=' * 50)
        
        retrieval_results = {
            "total_questions": len(self.test_questions),
            "successful_retrievals": 0,
            "failed_retrievals": 0,
            "avg_retrieval_time": 0,
            "retrieval_scores": [],
            "question_results": []
        }
        
        total_retrieval_time = 0
        
        for i, question in enumerate(self.test_questions, 1):
            print(f'\n❓ Question {i}: {question}')
            
            start_time = time.time()
            
            try:
                # Generate embedding for question
                question_embedding = await self.embedding_service.generate_embedding(question)
                
                # Search for relevant chunks
                similar_chunks = await self.chunk_repository.search_by_embedding(
                    embedding=question_embedding,
                    limit=5,
                    similarity_threshold=0.7
                )
                
                retrieval_time = time.time() - start_time
                total_retrieval_time += retrieval_time
                
                if similar_chunks:
                    retrieval_results["successful_retrievals"] += 1
                    
                    # Calculate average similarity score
                    avg_similarity = sum(score for _, score in similar_chunks) / len(similar_chunks)
                    retrieval_results["retrieval_scores"].append(avg_similarity)
                    
                    print(f'   ✅ Found {len(similar_chunks)} relevant chunks')
                    print(f'   ⏱️  Retrieval time: {retrieval_time:.3f}s')
                    print(f'   📊 Avg similarity: {avg_similarity:.3f}')
                    
                    # Show top result
                    if similar_chunks:
                        top_chunk, top_score = similar_chunks[0]
                        preview = top_chunk.content[:150] + "..." if len(top_chunk.content) > 150 else top_chunk.content
                        print(f'   🎯 Top result ({top_score:.3f}): "{preview}"')
                    
                    retrieval_results["question_results"].append({
                        "question": question,
                        "success": True,
                        "num_chunks": len(similar_chunks),
                        "avg_similarity": avg_similarity,
                        "retrieval_time": retrieval_time,
                        "top_chunk_preview": preview if similar_chunks else None
                    })
                else:
                    retrieval_results["failed_retrievals"] += 1
                    print(f'   ❌ No relevant chunks found')
                    print(f'   ⏱️  Retrieval time: {retrieval_time:.3f}s')
                    
                    retrieval_results["question_results"].append({
                        "question": question,
                        "success": False,
                        "retrieval_time": retrieval_time
                    })
                    
            except Exception as e:
                retrieval_results["failed_retrievals"] += 1
                print(f'   ❌ Error: {e}')
                
                retrieval_results["question_results"].append({
                    "question": question,
                    "success": False,
                    "error": str(e)
                })
        
        # Calculate final metrics
        if retrieval_results["total_questions"] > 0:
            retrieval_results["success_rate"] = (retrieval_results["successful_retrievals"] / retrieval_results["total_questions"]) * 100
            retrieval_results["avg_retrieval_time"] = total_retrieval_time / retrieval_results["total_questions"]
        
        if retrieval_results["retrieval_scores"]:
            retrieval_results["avg_similarity_score"] = sum(retrieval_results["retrieval_scores"]) / len(retrieval_results["retrieval_scores"])
        
        print(f'\n📊 RAG Retrieval Summary:')
        print(f'   Success rate: {retrieval_results["success_rate"]:.1f}%')
        print(f'   Avg retrieval time: {retrieval_results["avg_retrieval_time"]:.3f}s')
        if "avg_similarity_score" in retrieval_results:
            print(f'   Avg similarity score: {retrieval_results["avg_similarity_score"]:.3f}')
        
        return retrieval_results
    
    def generate_comprehensive_report(self, crawl_results, data_analysis, processing_results, retrieval_results):
        """Generate comprehensive RAG evaluation report."""
        print('\n🎯 COMPREHENSIVE RAG EVALUATION REPORT')
        print('=' * 60)
        print(f'📅 Generated: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
        print(f'🌐 Target: eufunds.bg (depth 2)')
        
        # Crawling Quality
        print(f'\n🕷️  CRAWLING QUALITY:')
        if "error" not in crawl_results:
            crawl_score = min(100, (crawl_results.get("urls_crawled", 0) / max(1, crawl_results.get("urls_crawled", 0) + crawl_results.get("urls_failed", 0))) * 100)
            print(f'   Pages crawled: {crawl_results.get("urls_crawled", 0)}')
            print(f'   Success rate: {crawl_score:.1f}%')
            print(f'   Status: {"✅ GOOD" if crawl_score > 70 else "⚠️ FAIR" if crawl_score > 50 else "❌ POOR"}')
        else:
            print(f'   ❌ Crawling failed: {crawl_results["error"]}')
            crawl_score = 0
        
        # Data Quality
        print(f'\n📊 DATA QUALITY:')
        if "error" not in data_analysis:
            data_score = data_analysis.get("content_quality_score", 0)
            print(f'   Documents with content: {data_analysis["documents_with_content"]}/{data_analysis["total_documents"]}')
            print(f'   Avg content length: {data_analysis["avg_content_length"]:.0f} chars')
            print(f'   Quality score: {data_score:.1f}%')
            print(f'   Status: {"✅ EXCELLENT" if data_score > 90 else "✅ GOOD" if data_score > 70 else "⚠️ FAIR" if data_score > 50 else "❌ POOR"}')
        else:
            print(f'   ❌ Data analysis failed: {data_analysis["error"]}')
            data_score = 0
        
        # Processing Quality
        print(f'\n🧩 PROCESSING QUALITY:')
        if "error" not in processing_results:
            processing_score = processing_results.get("success_rate", 0)
            chunk_stats = processing_results.get("chunk_statistics", {})
            print(f'   Documents processed: {processing_results["processed"]}/{processing_results["total"]}')
            print(f'   Processing success rate: {processing_score:.1f}%')
            print(f'   Total chunks: {chunk_stats.get("total_chunks", 0)}')
            print(f'   Chunks with embeddings: {chunk_stats.get("chunks_with_embeddings", 0)}')
            print(f'   Status: {"✅ EXCELLENT" if processing_score > 90 else "✅ GOOD" if processing_score > 70 else "⚠️ FAIR" if processing_score > 50 else "❌ POOR"}')
        else:
            print(f'   ❌ Processing failed: {processing_results["error"]}')
            processing_score = 0
        
        # RAG Retrieval Quality
        print(f'\n🔍 RAG RETRIEVAL QUALITY:')
        if "error" not in retrieval_results:
            retrieval_score = retrieval_results.get("success_rate", 0)
            print(f'   Successful retrievals: {retrieval_results["successful_retrievals"]}/{retrieval_results["total_questions"]}')
            print(f'   Success rate: {retrieval_score:.1f}%')
            print(f'   Avg retrieval time: {retrieval_results["avg_retrieval_time"]:.3f}s')
            if "avg_similarity_score" in retrieval_results:
                print(f'   Avg similarity score: {retrieval_results["avg_similarity_score"]:.3f}')
            print(f'   Status: {"✅ EXCELLENT" if retrieval_score > 80 else "✅ GOOD" if retrieval_score > 60 else "⚠️ FAIR" if retrieval_score > 40 else "❌ POOR"}')
        else:
            print(f'   ❌ Retrieval failed: {retrieval_results["error"]}')
            retrieval_score = 0
        
        # Overall Assessment
        scores = [crawl_score, data_score, processing_score, retrieval_score]
        valid_scores = [s for s in scores if s > 0]
        overall_score = sum(valid_scores) / len(valid_scores) if valid_scores else 0
        
        print(f'\n🎯 OVERALL ASSESSMENT:')
        print(f'   Overall Score: {overall_score:.1f}/100')
        
        if overall_score >= 85:
            print('   🟢 EXCELLENT - RAG system performing optimally!')
        elif overall_score >= 70:
            print('   🟡 GOOD - RAG system performing well!')
        elif overall_score >= 50:
            print('   🟠 FAIR - RAG system functional, needs optimization')
        else:
            print('   🔴 POOR - RAG system needs significant improvement')
        
        # Recommendations
        print(f'\n💡 RECOMMENDATIONS:')
        if crawl_score < 70:
            print('   - Improve crawling reliability and error handling')
        if data_score < 70:
            print('   - Enhance content extraction and filtering')
        if processing_score < 70:
            print('   - Optimize document processing and chunking')
        if retrieval_score < 60:
            print('   - Improve embedding quality and similarity thresholds')
        
        if overall_score >= 85:
            print('   - System is production-ready!')
        
        return overall_score
    
    async def run_comprehensive_test(self):
        """Run the complete RAG evaluation test."""
        print('🚀 COMPREHENSIVE RAG EVALUATION FOR EUFUNDS.BG')
        print('=' * 70)
        
        start_time = time.time()
        
        try:
            # Phase 1: Crawl eufunds.bg
            crawl_results = await self.crawl_eufunds()
            
            # Phase 2: Analyze data quality
            data_analysis = await self.analyze_crawled_data()
            
            # Phase 3: Process to chunks
            processing_results = await self.process_documents_to_chunks()
            
            # Phase 4: Test RAG retrieval
            retrieval_results = await self.test_rag_retrieval()
            
            # Generate comprehensive report
            overall_score = self.generate_comprehensive_report(
                crawl_results, data_analysis, processing_results, retrieval_results
            )
            
            total_time = time.time() - start_time
            print(f'\n⏱️  Total evaluation time: {total_time:.2f} seconds')
            
            # Save detailed results
            detailed_results = {
                "timestamp": datetime.now().isoformat(),
                "target_url": "https://eufunds.bg",
                "crawl_depth": 2,
                "overall_score": overall_score,
                "crawl_results": crawl_results,
                "data_analysis": data_analysis,
                "processing_results": processing_results,
                "retrieval_results": retrieval_results,
                "total_evaluation_time": total_time
            }
            
            filename = f"eufunds_rag_evaluation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(filename, "w", encoding="utf-8") as f:
                json.dump(detailed_results, f, indent=2, ensure_ascii=False)
            
            print(f'\n💾 Detailed results saved to: {filename}')
            
        except Exception as e:
            print(f'\n❌ Evaluation failed: {e}')
            import traceback
            traceback.print_exc()


async def main():
    """Main function."""
    tester = EUFundsRAGTester()
    await tester.run_comprehensive_test()


if __name__ == "__main__":
    asyncio.run(main())
