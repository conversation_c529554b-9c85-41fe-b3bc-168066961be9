import{d as C,V as b,i as r,c,o as s,j as a,k as t,l as m,q as d,a as A,n as e,B as h,t as v,C as T,A as U,p as $,_ as q,v as z,y as E,F as k,f as F,g as y,E as H,Y as I,z as S,G as Y,H as j,I as w}from"./index-ei-kaitd.js";import{u as G}from"./usePageTitle-LeBMnqrg.js";import{u as L}from"./usePrefectApi-qsKG6mzx.js";import"./api-DGOAIix_.js";import"./mapper-BuxGYc8V.js";const J={class:"automation-card__header"},K={class:"automation-card__header-actions"},M={key:0,class:"automation-card__description"},O={class:"automation-card__label"},Q=C({__name:"AutomationCard",props:{automation:{}},emits:["update"],setup(V,{emit:p}){const l=p,_=b();return(o,n)=>{const f=r("p-link"),u=r("p-content"),g=r("p-card");return s(),c(g,{class:"automation-card"},{default:a(()=>[t(u,null,{default:a(()=>[t(u,{secondary:""},{default:a(()=>[m("div",J,[t(f,{class:"automation-card__name",to:e(_).automation(o.automation.id)},{default:a(()=>[h(v(o.automation.name),1)]),_:1},8,["to"]),m("div",K,[t(e(T),{automation:o.automation,onUpdate:n[0]||(n[0]=i=>l("update"))},null,8,["automation"]),t(e(U),{automation:o.automation,onDelete:n[1]||(n[1]=i=>l("update"))},null,8,["automation"])])]),o.automation.description?(s(),d("p",M,v(o.automation.description),1)):A("",!0)]),_:1}),t(u,{secondary:""},{default:a(()=>[n[2]||(n[2]=m("span",{class:"automation-card__label"},"Trigger",-1)),t(e($),{trigger:o.automation.trigger},null,8,["trigger"])]),_:1,__:[2]}),t(u,{secondary:""},{default:a(()=>[m("span",O,v(e(q)("Action",o.automation.actions.length)),1),(s(!0),d(k,null,z(o.automation.actions,i=>(s(),c(g,{key:i.id},{default:a(()=>[t(e(E),{action:i},null,8,["action"])]),_:2},1024))),128))]),_:1})]),_:1})]),_:1})}}}),tt=C({__name:"Automations",setup(V){const p=b();G("Automations");const l=[{text:"Automations"}],_=L(),o=F(_.automations.getAutomations),n=y(()=>o.response??[]),f=y(()=>o.executed),u=y(()=>n.value.length===0);return(g,i)=>{const B=r("p-button"),N=r("p-virtual-scroller"),D=r("p-layout-default");return s(),c(D,{class:"automations"},{header:a(()=>[t(e(S),{crumbs:l},Y({"after-crumbs":a(()=>[t(B,{size:"sm",icon:"PlusIcon",to:e(p).automationCreate()},null,8,["to"])]),_:2},[u.value?void 0:{name:"actions",fn:a(()=>[t(e(j),{to:e(w).docs.automations},{default:a(()=>i[0]||(i[0]=[h(" Documentation ")])),_:1,__:[0]},8,["to"])]),key:"0"}]),1024)]),default:a(()=>[f.value?(s(),d(k,{key:0},[u.value?(s(),c(e(H),{key:0})):(s(),d(k,{key:1},[t(e(I),{count:n.value.length,label:"automation"},null,8,["count"]),t(N,{items:n.value,class:"automations-list"},{default:a(({item:P})=>[t(Q,{automation:P,onUpdate:e(o).refresh},null,8,["automation","onUpdate"])]),_:1},8,["items"])],64))],64)):A("",!0)]),_:1})}}});export{tt as default};
//# sourceMappingURL=Automations-DyvTyTYg.js.map
