#!/usr/bin/env python3
"""Simple check of Supabase data."""

import asyncio
from config.settings import get_settings


async def check_supabase_simple():
    """Simple check of what's in Supabase."""
    print('🔍 SIMPLE SUPABASE CHECK')
    print('=' * 40)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Check documents table
        print('📋 Checking documents table...')
        try:
            # Get count
            count_result = client.table('documents').select('id', count='exact').execute()
            total_docs = count_result.count
            print(f'   📊 Total documents: {total_docs}')
            
            if total_docs > 0:
                # Get recent documents
                docs_result = client.table('documents').select('*').order('created_at', desc=True).limit(10).execute()
                documents = docs_result.data
                
                print(f'\n📝 Recent documents:')
                for i, doc in enumerate(documents, 1):
                    url = doc.get('url', 'No URL')
                    content_length = len(doc.get('content', '')) if doc.get('content') else 0
                    status = doc.get('status', 'unknown')
                    created_at = doc.get('created_at', 'unknown')
                    crawl_session_id = doc.get('crawl_session_id', 'none')
                    
                    print(f'   {i}. {url}')
                    print(f'      Status: {status}')
                    print(f'      Content: {content_length} chars')
                    print(f'      Created: {created_at}')
                    print(f'      Session: {crawl_session_id}')
                    
                    # Show content preview
                    if doc.get('content'):
                        content = doc['content']
                        # Clean content
                        clean_content = ' '.join(content.split())
                        preview = clean_content[:200] if clean_content else "No content"
                        print(f'      Preview: "{preview}..."')
                    print()
                
                # Check for eufunds documents
                eufunds_result = client.table('documents').select('*').ilike('url', '%eufunds%').execute()
                eufunds_docs = eufunds_result.data
                
                print(f'🇪🇺 EUFunds documents: {len(eufunds_docs)}')
                
                if eufunds_docs:
                    print('\n📝 EUFunds documents:')
                    for i, doc in enumerate(eufunds_docs, 1):
                        url = doc.get('url', 'No URL')
                        content_length = len(doc.get('content', '')) if doc.get('content') else 0
                        print(f'   {i}. {url} ({content_length} chars)')
                        
                        if doc.get('content'):
                            content = doc['content']
                            clean_content = ' '.join(content.split())
                            preview = clean_content[:300] if clean_content else "No content"
                            print(f'      Content: "{preview}..."')
                        print()
                
                # Statistics
                docs_with_content = len([d for d in documents if d.get('content') and d.get('content').strip()])
                total_content = sum(len(d.get('content', '')) for d in documents)
                
                print(f'📊 Statistics:')
                print(f'   Total documents: {total_docs}')
                print(f'   EUFunds documents: {len(eufunds_docs)}')
                print(f'   Documents with content: {docs_with_content}/{len(documents)}')
                print(f'   Total content length: {total_content:,} chars')
                if docs_with_content > 0:
                    print(f'   Average content length: {total_content // docs_with_content:,} chars')
                
                return {
                    "total_documents": total_docs,
                    "eufunds_documents": len(eufunds_docs),
                    "documents_with_content": docs_with_content,
                    "total_content_length": total_content,
                    "has_data": True
                }
            else:
                print('❌ NO DOCUMENTS IN DATABASE!')
                return {"total_documents": 0, "has_data": False}
                
        except Exception as e:
            print(f'   ❌ Error accessing documents: {e}')
            return {"error": str(e), "has_data": False}
        
    except Exception as e:
        print(f'❌ Error connecting to Supabase: {e}')
        return {"error": str(e), "has_data": False}


async def check_chunks_table():
    """Check chunks table."""
    print('\n📦 Checking chunks table...')
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        try:
            count_result = client.table('chunks').select('id', count='exact').execute()
            total_chunks = count_result.count
            print(f'   📊 Total chunks: {total_chunks}')
            return {"chunks_exist": True, "total_chunks": total_chunks}
        except Exception as e:
            print(f'   ❌ Chunks table error: {e}')
            print('   💡 Chunks table does not exist - this is why RAG processing failed!')
            return {"chunks_exist": False, "error": str(e)}
            
    except Exception as e:
        print(f'❌ Error checking chunks: {e}')
        return {"chunks_exist": False, "error": str(e)}


async def main():
    """Main function."""
    print('🔍 SUPABASE REALITY CHECK')
    print('=' * 50)
    
    # Check documents
    doc_stats = await check_supabase_simple()
    
    # Check chunks
    chunk_stats = await check_chunks_table()
    
    # Final assessment
    print('\n🎯 REALITY CHECK RESULTS')
    print('=' * 30)
    
    if doc_stats.get("has_data"):
        total_docs = doc_stats["total_documents"]
        eufunds_docs = doc_stats["eufunds_documents"]
        docs_with_content = doc_stats["documents_with_content"]
        
        print(f'📊 CRAWLING REALITY:')
        if total_docs > 0:
            print(f'   ✅ Crawling IS working - {total_docs} documents stored')
            if eufunds_docs > 0:
                print(f'   ✅ EUFunds crawling worked - {eufunds_docs} eufunds documents')
            else:
                print(f'   ⚠️  No recent EUFunds data found')
            
            if docs_with_content > 0:
                print(f'   ✅ Content extraction working - {docs_with_content} docs with content')
            else:
                print(f'   ❌ Content extraction failed - no content in documents')
        else:
            print(f'   ❌ Crawling NOT working - no documents stored')
    else:
        print(f'❌ CRAWLING FAILED - cannot access database or no data')
    
    print(f'\n🤖 RAG SYSTEM REALITY:')
    if not doc_stats.get("has_data"):
        print(f'   🔴 RAG CANNOT WORK - No documents in database')
    elif not chunk_stats.get("chunks_exist"):
        print(f'   🔴 RAG CANNOT WORK - Chunks table missing')
        print(f'   💡 Need to create chunks table in Supabase')
    elif doc_stats["documents_with_content"] == 0:
        print(f'   🔴 RAG CANNOT WORK - No content to process')
    else:
        print(f'   🟡 RAG PARTIALLY WORKING - Has documents but missing chunks table')
    
    # Honest assessment
    print(f'\n💯 HONEST ASSESSMENT:')
    if doc_stats.get("total_documents", 0) > 0:
        print(f'   ✅ Crawling system: WORKING')
        print(f'   ✅ Data storage: WORKING')
        print(f'   ✅ Content extraction: WORKING')
        print(f'   ❌ Chunk processing: BROKEN (missing table)')
        print(f'   ❌ RAG search: BROKEN (no chunks)')
        print(f'\n   🎯 CONCLUSION: System is 60% functional')
        print(f'   🔧 FIX NEEDED: Create chunks table in Supabase')
    else:
        print(f'   ❌ Crawling system: NOT WORKING')
        print(f'   ❌ Data storage: EMPTY')
        print(f'   ❌ RAG system: CANNOT FUNCTION')
        print(f'\n   🎯 CONCLUSION: System is NOT working')


if __name__ == "__main__":
    asyncio.run(main())
