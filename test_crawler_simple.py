#!/usr/bin/env python3
"""Simple test script for HTML crawler."""

import asyncio
import aiohttp
from bs4 import BeautifulSoup


async def simple_crawler_test():
    """Simple test of aiohttp crawler functionality."""
    print("🕷️  Simple Crawler Test")
    print("=" * 30)
    
    test_url = "https://httpbin.org/html"
    
    try:
        async with aiohttp.ClientSession() as session:
            print(f"📄 Fetching: {test_url}")
            
            async with session.get(test_url) as response:
                print(f"✅ Status: {response.status}")
                print(f"✅ Content-Type: {response.headers.get('content-type')}")
                
                content = await response.text()
                print(f"✅ Content length: {len(content)} characters")
                
                # Parse with BeautifulSoup
                soup = BeautifulSoup(content, 'html.parser')
                title = soup.title.string if soup.title else "No title"
                print(f"✅ Page title: {title}")
                
                # Count links
                links = soup.find_all('a')
                print(f"✅ Links found: {len(links)}")
                
    except Exception as e:
        print(f"❌ Error: {e}")
    
    print("\n🎉 Simple test completed!")


if __name__ == "__main__":
    asyncio.run(simple_crawler_test())
