"""Supabase implementation of ChunkRepository."""

from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime
import json

from supabase import create_client, Client
from ...domain.entities import Chunk, ChunkMetadata
from ...domain.repositories import ChunkRepository


class SupabaseChunkRepository(ChunkRepository):
    """Supabase implementation of chunk repository."""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        self.client: Client = create_client(supabase_url, supabase_key)
        self.table_name = "chunks"
    
    async def create(self, chunk: Chunk) -> Chunk:
        """Create a new chunk."""
        data = self._chunk_to_db_dict(chunk)
        
        result = self.client.table(self.table_name).insert(data).execute()
        
        if result.data:
            return self._db_dict_to_chunk(result.data[0])
        
        raise Exception("Failed to create chunk")
    
    async def get_by_id(self, chunk_id: str) -> Optional[Chunk]:
        """Get chunk by ID."""
        result = self.client.table(self.table_name).select("*").eq("id", chunk_id).execute()
        
        if result.data:
            return self._db_dict_to_chunk(result.data[0])
        
        return None
    
    async def update(self, chunk: Chunk) -> Chunk:
        """Update an existing chunk."""
        data = self._chunk_to_db_dict(chunk)
        data["updated_at"] = datetime.utcnow().isoformat()
        
        result = self.client.table(self.table_name).update(data).eq("id", chunk.id).execute()
        
        if result.data:
            return self._db_dict_to_chunk(result.data[0])
        
        raise Exception("Failed to update chunk")
    
    async def delete(self, chunk_id: str) -> bool:
        """Delete a chunk."""
        result = self.client.table(self.table_name).delete().eq("id", chunk_id).execute()
        return len(result.data) > 0
    
    async def get_chunks_by_document(
        self,
        document_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Chunk]:
        """Get all chunks for a specific document."""
        result = self.client.table(self.table_name).select("*").eq(
            "document_id", document_id
        ).range(offset, offset + limit - 1).order("chunk_index").execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def count_chunks_by_document(self, document_id: str) -> int:
        """Count chunks for a specific document."""
        result = self.client.table(self.table_name).select("id", count="exact").eq(
            "document_id", document_id
        ).execute()
        
        return result.count or 0
    
    async def delete_chunks_by_document(self, document_id: str) -> int:
        """Delete all chunks for a specific document."""
        result = self.client.table(self.table_name).delete().eq(
            "document_id", document_id
        ).execute()
        
        return len(result.data)
    
    async def bulk_create(self, chunks: List[Chunk]) -> List[Chunk]:
        """Create multiple chunks in bulk."""
        data = [self._chunk_to_db_dict(chunk) for chunk in chunks]
        
        result = self.client.table(self.table_name).insert(data).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def bulk_update_embeddings(
        self,
        chunk_embeddings: List[Tuple[str, List[float], str]],
    ) -> int:
        """Update embeddings for multiple chunks."""
        updated_count = 0
        
        for chunk_id, embedding, model in chunk_embeddings:
            try:
                data = {
                    "embedding": embedding,
                    "embedding_model": model,
                    "updated_at": datetime.utcnow().isoformat(),
                }
                
                result = self.client.table(self.table_name).update(data).eq(
                    "id", chunk_id
                ).execute()
                
                if result.data:
                    updated_count += 1
                    
            except Exception as e:
                print(f"Error updating embedding for chunk {chunk_id}: {e}")
        
        return updated_count
    
    async def search_by_embedding(
        self,
        embedding: List[float],
        limit: int = 10,
        similarity_threshold: float = 0.7,
        document_ids: Optional[List[str]] = None,
    ) -> List[Tuple[Chunk, float]]:
        """Search chunks by embedding similarity."""
        # Use Supabase's vector similarity search
        try:
            # Build the RPC call for vector similarity
            rpc_params = {
                "query_embedding": embedding,
                "similarity_threshold": similarity_threshold,
                "max_results": limit,
            }
            
            if document_ids:
                rpc_params["document_ids"] = document_ids
            
            # Call the search_similar_chunks function
            result = self.client.rpc("search_similar_chunks", rpc_params).execute()
            
            chunks_with_similarity = []
            for row in result.data:
                chunk = self._db_dict_to_chunk(row)
                similarity = row.get("similarity", 0.0)
                chunks_with_similarity.append((chunk, similarity))
            
            return chunks_with_similarity
            
        except Exception as e:
            print(f"Error in vector search: {e}")
            # Fallback to basic search without vector similarity
            return await self._fallback_search_by_text("", limit, document_ids)
    
    async def search_by_text(
        self,
        query: str,
        limit: int = 10,
        document_ids: Optional[List[str]] = None,
    ) -> List[Chunk]:
        """Search chunks by text content."""
        return await self._fallback_search_by_text(query, limit, document_ids)
    
    async def _fallback_search_by_text(
        self,
        query: str,
        limit: int = 10,
        document_ids: Optional[List[str]] = None,
    ) -> List[Chunk]:
        """Fallback text search implementation."""
        db_query = self.client.table(self.table_name).select("*")
        
        if query:
            db_query = db_query.ilike("content", f"%{query}%")
        
        if document_ids:
            db_query = db_query.in_("document_id", document_ids)
        
        result = db_query.limit(limit).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def get_chunks_without_embeddings(
        self,
        limit: int = 100,
        embedding_model: Optional[str] = None,
    ) -> List[Chunk]:
        """Get chunks that don't have embeddings."""
        db_query = self.client.table(self.table_name).select("*").is_("embedding", "null")
        
        if embedding_model:
            # Also include chunks with different embedding model
            db_query = db_query.or_(f"embedding_model.neq.{embedding_model}")
        
        result = db_query.limit(limit).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def get_chunks_by_embedding_model(self, model: str) -> List[Chunk]:
        """Get chunks processed with a specific embedding model."""
        result = self.client.table(self.table_name).select("*").eq(
            "embedding_model", model
        ).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def get_similar_chunks(
        self,
        chunk_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.8,
    ) -> List[Tuple[Chunk, float]]:
        """Get chunks similar to a specific chunk."""
        # First get the reference chunk
        reference_chunk = await self.get_by_id(chunk_id)
        if not reference_chunk or not reference_chunk.embedding:
            return []
        
        # Search for similar chunks
        return await self.search_by_embedding(
            embedding=reference_chunk.embedding,
            limit=limit + 1,  # +1 to exclude the reference chunk
            similarity_threshold=similarity_threshold,
        )
    
    async def get_chunks_by_content_length(
        self,
        min_length: int = 0,
        max_length: int = 10000,
        limit: int = 100,
    ) -> List[Chunk]:
        """Get chunks by content length range."""
        result = self.client.table(self.table_name).select("*").gte(
            "content_length", min_length
        ).lte("content_length", max_length).limit(limit).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def get_chunks_created_after(self, date: datetime) -> List[Chunk]:
        """Get chunks created after a specific date."""
        result = self.client.table(self.table_name).select("*").gte(
            "created_at", date.isoformat()
        ).execute()
        
        return [self._db_dict_to_chunk(row) for row in result.data]
    
    async def get_random_chunks(self, limit: int = 10) -> List[Chunk]:
        """Get random chunks for sampling."""
        # Supabase doesn't have a direct random function, so we'll use a workaround
        result = self.client.table(self.table_name).select("*").limit(limit * 3).execute()
        
        chunks = [self._db_dict_to_chunk(row) for row in result.data]
        
        # Return a subset (simulating randomness)
        import random
        if len(chunks) > limit:
            chunks = random.sample(chunks, limit)
        
        return chunks
    
    async def update_chunk_scores(
        self,
        chunk_scores: List[Tuple[str, float, float]],
    ) -> int:
        """Update quality scores for multiple chunks."""
        updated_count = 0
        
        for chunk_id, relevance_score, readability_score in chunk_scores:
            try:
                data = {
                    "relevance_score": relevance_score,
                    "readability_score": readability_score,
                    "updated_at": datetime.utcnow().isoformat(),
                }
                
                result = self.client.table(self.table_name).update(data).eq(
                    "id", chunk_id
                ).execute()
                
                if result.data:
                    updated_count += 1
                    
            except Exception as e:
                print(f"Error updating scores for chunk {chunk_id}: {e}")
        
        return updated_count
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get chunk repository statistics."""
        # Total chunks
        total_result = self.client.table(self.table_name).select("id", count="exact").execute()
        total_chunks = total_result.count or 0
        
        # Chunks with embeddings
        embedded_result = self.client.table(self.table_name).select("id", count="exact").not_.is_("embedding", "null").execute()
        chunks_with_embeddings = embedded_result.count or 0
        
        return {
            "total_chunks": total_chunks,
            "chunks_with_embeddings": chunks_with_embeddings,
            "chunks_without_embeddings": total_chunks - chunks_with_embeddings,
            "embedding_coverage": chunks_with_embeddings / max(1, total_chunks) * 100,
        }
    
    async def cleanup_orphaned_chunks(self) -> int:
        """Clean up chunks that don't have corresponding documents."""
        # This would require a more complex query joining with documents table
        # For now, return 0 as a placeholder
        return 0
    
    async def get_chunk_distribution_by_document(self) -> Dict[str, int]:
        """Get chunk count distribution by document."""
        # This would require aggregation which is complex in Supabase
        # For now, return empty dict as placeholder
        return {}
    
    async def reindex_embeddings(self, embedding_model: str) -> int:
        """Reindex all embeddings for a specific model."""
        # This would be handled by the embedding service
        return 0
    
    def _chunk_to_db_dict(self, chunk: Chunk) -> Dict[str, Any]:
        """Convert Chunk entity to database dictionary."""
        return {
            "id": chunk.id,
            "document_id": chunk.document_id,
            "content": chunk.content,
            "content_length": len(chunk.content),
            "chunk_index": chunk.chunk_index,
            "start_position": chunk.start_position,
            "end_position": chunk.end_position,
            "embedding": chunk.embedding,
            "embedding_model": chunk.embedding_model,
            "metadata": chunk.metadata.__dict__ if chunk.metadata else {},
            "created_at": chunk.created_at.isoformat(),
            "updated_at": chunk.updated_at.isoformat(),
            "relevance_score": chunk.relevance_score,
            "readability_score": chunk.readability_score,
        }
    
    def _db_dict_to_chunk(self, data: Dict[str, Any]) -> Chunk:
        """Convert database dictionary to Chunk entity."""
        return Chunk.from_dict(data)
