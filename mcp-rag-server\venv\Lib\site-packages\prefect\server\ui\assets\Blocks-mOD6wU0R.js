import{d as m,e as p,f as d,g as a,i as _,c as s,o as t,j as c,q as f,a as k,F as i,n as o,cf as C,cg as y,k as B,ch as g}from"./index-ei-kaitd.js";import{u as h}from"./usePageTitle-LeBMnqrg.js";const N=m({__name:"Blocks",setup(x){const n=p(),e=d(n.blockDocuments.getBlockDocumentsCount),l=a(()=>e.executed&&e.response==0),r=a(()=>e.executed);return h("Blocks"),(b,D)=>{const u=_("p-layout-default");return t(),s(u,{class:"blocks"},{header:c(()=>[B(o(g))]),default:c(()=>[r.value?(t(),f(i,{key:0},[l.value?(t(),s(o(C),{key:0})):(t(),s(o(y),{key:1,onDelete:o(e).refresh},null,8,["onDelete"]))],64)):k("",!0)]),_:1})}}});export{N as default};
//# sourceMappingURL=Blocks-mOD6wU0R.js.map
