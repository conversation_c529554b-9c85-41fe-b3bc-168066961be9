import{d as A,V as y,u as b,W as g,K as w,g as h,i as k,c as v,o as C,j as a,k as u,n as e,z as V,H as $,I as r,B,L as l,$ as E}from"./index-ei-kaitd.js";import{_ as I}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-CQQMIE-5.js";import{u as H}from"./usePageTitle-LeBMnqrg.js";import{u as N}from"./usePrefectApi-qsKG6mzx.js";import"./mapper-BuxGYc8V.js";import"./api-DGOAIix_.js";const D=A({__name:"AutomationEdit",async setup(P){let o,m;const i=N(),c=y(),d=b(),p=g("automationId"),s=([o,m]=w(()=>i.automations.getAutomation(p.value)),o=await o,m(),o);H(`Edit Automation: ${s.name}`);const f=h(()=>[{text:"Automations",to:c.automations()},{text:s.name}]);async function x(_){try{await i.automations.updateAutomation(p.value,_),l(r.success.automationUpdate),d.push(c.automations())}catch(t){console.error(t);const n=E(t,r.error.automationUpdate);l(n,"error",{timeout:!1})}}return(_,t)=>{const n=k("p-layout-default");return C(),v(n,{class:"workspace-automation-create"},{header:a(()=>[u(e(V),{crumbs:f.value},{actions:a(()=>[u(e($),{to:e(r).docs.automations},{default:a(()=>t[0]||(t[0]=[B(" Documentation ")])),_:1,__:[0]},8,["to"])]),_:1},8,["crumbs"])]),default:a(()=>[u(I,{automation:e(s),editing:"",onSubmit:x},null,8,["automation"])]),_:1})}}});export{D as default};
//# sourceMappingURL=AutomationEdit-C7nxdJ6k.js.map
