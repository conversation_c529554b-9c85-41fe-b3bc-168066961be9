"""Supabase implementation of DocumentRepository."""

from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from supabase import create_client, Client
from ...domain.entities import Document, DocumentStatus, DocumentType
from ...domain.repositories import DocumentRepository


class SupabaseDocumentRepository(DocumentRepository):
    """Supabase implementation of document repository."""
    
    def __init__(self, supabase_url: str, supabase_key: str):
        self.client: Client = create_client(supabase_url, supabase_key)
        self.table_name = "documents"
    
    async def create(self, document: Document) -> Document:
        """Create a new document."""
        data = self._document_to_db_dict(document)
        
        result = self.client.table(self.table_name).insert(data).execute()
        
        if result.data:
            return self._db_dict_to_document(result.data[0])
        
        raise Exception("Failed to create document")
    
    async def get_by_id(self, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        result = self.client.table(self.table_name).select("*").eq("id", document_id).execute()
        
        if result.data:
            return self._db_dict_to_document(result.data[0])
        
        return None
    
    async def get_by_url(self, url: str) -> Optional[Document]:
        """Get document by URL."""
        result = self.client.table(self.table_name).select("*").eq("url", url).execute()
        
        if result.data:
            return self._db_dict_to_document(result.data[0])
        
        return None
    
    async def get_by_content_hash(self, content_hash: str) -> Optional[Document]:
        """Get document by content hash."""
        result = self.client.table(self.table_name).select("*").eq("content_hash", content_hash).execute()
        
        if result.data:
            return self._db_dict_to_document(result.data[0])
        
        return None
    
    async def update(self, document: Document) -> Document:
        """Update an existing document."""
        data = self._document_to_db_dict(document)
        data["updated_at"] = datetime.utcnow().isoformat()
        
        result = self.client.table(self.table_name).update(data).eq("id", document.id).execute()
        
        if result.data:
            return self._db_dict_to_document(result.data[0])
        
        raise Exception("Failed to update document")
    
    async def delete(self, document_id: str) -> bool:
        """Delete a document."""
        result = self.client.table(self.table_name).delete().eq("id", document_id).execute()
        return len(result.data) > 0
    
    async def list_documents(
        self,
        limit: int = 100,
        offset: int = 0,
        status: Optional[DocumentStatus] = None,
        document_type: Optional[DocumentType] = None,
        crawl_session_id: Optional[str] = None,
    ) -> List[Document]:
        """List documents with optional filters."""
        query = self.client.table(self.table_name).select("*")
        
        if status:
            query = query.eq("status", status.value)
        
        if document_type:
            query = query.eq("document_type", document_type.value)
        
        if crawl_session_id:
            query = query.eq("crawl_session_id", crawl_session_id)
        
        result = query.range(offset, offset + limit - 1).execute()
        
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def count_documents(
        self,
        status: Optional[DocumentStatus] = None,
        document_type: Optional[DocumentType] = None,
        crawl_session_id: Optional[str] = None,
    ) -> int:
        """Count documents with optional filters."""
        query = self.client.table(self.table_name).select("id", count="exact")
        
        if status:
            query = query.eq("status", status.value)
        
        if document_type:
            query = query.eq("document_type", document_type.value)
        
        if crawl_session_id:
            query = query.eq("crawl_session_id", crawl_session_id)
        
        result = query.execute()
        return result.count or 0
    
    async def get_documents_by_status(self, status: DocumentStatus) -> List[Document]:
        """Get all documents with specific status."""
        result = self.client.table(self.table_name).select("*").eq("status", status.value).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_by_crawl_session(self, crawl_session_id: str) -> List[Document]:
        """Get all documents from a specific crawl session."""
        result = self.client.table(self.table_name).select("*").eq("crawl_session_id", crawl_session_id).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_by_domain(self, domain: str) -> List[Document]:
        """Get all documents from a specific domain."""
        # Use ilike for case-insensitive pattern matching
        result = self.client.table(self.table_name).select("*").ilike("url", f"%{domain}%").execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_created_after(self, date: datetime) -> List[Document]:
        """Get documents created after a specific date."""
        result = self.client.table(self.table_name).select("*").gte("created_at", date.isoformat()).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_updated_after(self, date: datetime) -> List[Document]:
        """Get documents updated after a specific date."""
        result = self.client.table(self.table_name).select("*").gte("updated_at", date.isoformat()).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def search_documents(
        self,
        query: str,
        limit: int = 100,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Document]:
        """Search documents by content or metadata."""
        # Use text search on content and metadata
        db_query = self.client.table(self.table_name).select("*")
        
        # Search in content and title
        db_query = db_query.or_(f"content.ilike.%{query}%,metadata->>title.ilike.%{query}%")
        
        result = db_query.range(offset, offset + limit - 1).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_needing_processing(self, limit: int = 100) -> List[Document]:
        """Get documents that need processing."""
        result = self.client.table(self.table_name).select("*").in_(
            "status", [DocumentStatus.PENDING.value, DocumentStatus.FAILED.value]
        ).lt("retry_count", 3).limit(limit).execute()
        
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def get_documents_by_embedding_model(self, model: str) -> List[Document]:
        """Get documents processed with a specific embedding model."""
        result = self.client.table(self.table_name).select("*").eq("embedding_model", model).execute()
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def update_document_status(
        self,
        document_id: str,
        status: DocumentStatus,
        error_message: Optional[str] = None,
    ) -> bool:
        """Update document status."""
        data = {
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat(),
        }
        
        if status == DocumentStatus.COMPLETED:
            data["processed_at"] = datetime.utcnow().isoformat()
        
        if error_message:
            data["error_message"] = error_message
        
        result = self.client.table(self.table_name).update(data).eq("id", document_id).execute()
        return len(result.data) > 0
    
    async def increment_retry_count(self, document_id: str) -> bool:
        """Increment document retry count."""
        # First get current retry count
        current = await self.get_by_id(document_id)
        if not current:
            return False
        
        data = {
            "retry_count": current.retry_count + 1,
            "updated_at": datetime.utcnow().isoformat(),
        }
        
        result = self.client.table(self.table_name).update(data).eq("id", document_id).execute()
        return len(result.data) > 0
    
    async def bulk_create(self, documents: List[Document]) -> List[Document]:
        """Create multiple documents in bulk."""
        data = [self._document_to_db_dict(doc) for doc in documents]
        
        result = self.client.table(self.table_name).insert(data).execute()
        
        return [self._db_dict_to_document(row) for row in result.data]
    
    async def bulk_update_status(
        self,
        document_ids: List[str],
        status: DocumentStatus,
    ) -> int:
        """Update status for multiple documents."""
        data = {
            "status": status.value,
            "updated_at": datetime.utcnow().isoformat(),
        }
        
        if status == DocumentStatus.COMPLETED:
            data["processed_at"] = datetime.utcnow().isoformat()
        
        result = self.client.table(self.table_name).update(data).in_("id", document_ids).execute()
        return len(result.data)
    
    async def get_statistics(self) -> Dict[str, Any]:
        """Get document repository statistics."""
        # Get total count
        total_result = self.client.table(self.table_name).select("id", count="exact").execute()
        total_documents = total_result.count or 0
        
        # Get status counts
        stats = {"total_documents": total_documents}
        
        for status in DocumentStatus:
            status_result = self.client.table(self.table_name).select("id", count="exact").eq("status", status.value).execute()
            stats[f"{status.value}_documents"] = status_result.count or 0
        
        return stats
    
    async def cleanup_old_documents(
        self,
        older_than: datetime,
        status: Optional[DocumentStatus] = None,
    ) -> int:
        """Clean up old documents."""
        query = self.client.table(self.table_name).delete().lt("created_at", older_than.isoformat())
        
        if status:
            query = query.eq("status", status.value)
        
        result = query.execute()
        return len(result.data)
    
    def _document_to_db_dict(self, document: Document) -> Dict[str, Any]:
        """Convert Document entity to database dictionary."""
        return {
            "id": document.id,
            "url": document.url,
            "content": document.content,
            "raw_content": document.raw_content,
            "document_type": document.document_type.value,
            "status": document.status.value,
            "metadata": {
                "title": document.metadata.title,
                "author": document.metadata.author,
                "language": document.metadata.language,
                "keywords": document.metadata.keywords,
                "description": document.metadata.description,
                "source_url": document.metadata.source_url,
                "content_type": document.metadata.content_type,
                "file_size": document.metadata.file_size,
                "page_count": document.metadata.page_count,
                "custom_fields": document.metadata.custom_fields,
            },
            "content_hash": document.content_hash,
            "embedding_model": document.embedding_model,
            "chunk_count": document.chunk_count,
            "created_at": document.created_at.isoformat(),
            "updated_at": document.updated_at.isoformat(),
            "processed_at": document.processed_at.isoformat() if document.processed_at else None,
            "crawl_depth": document.crawl_depth,
            "parent_url": document.parent_url,
            "crawl_session_id": document.crawl_session_id,
            "error_message": document.error_message,
            "retry_count": document.retry_count,
        }
    
    def _db_dict_to_document(self, data: Dict[str, Any]) -> Document:
        """Convert database dictionary to Document entity."""
        return Document.from_dict(data)
