"""Domain entities for the MCP RAG Server."""

from .document import Document, DocumentType, DocumentStatus, DocumentMetadata
from .chunk import Chunk, ChunkMetadata
from .crawl_session import CrawlSession, CrawlStatus, CrawlStrategy, CrawlConfig, CrawlStats
from .search_query import SearchQuery, SearchType, SearchScope, SearchFilters, SearchResult

__all__ = [
    # Document entities
    "Document",
    "DocumentType", 
    "DocumentStatus",
    "DocumentMetadata",
    
    # Chunk entities
    "Chunk",
    "ChunkMetadata",
    
    # Crawl session entities
    "CrawlSession",
    "CrawlStatus",
    "CrawlStrategy", 
    "CrawlConfig",
    "CrawlStats",
    
    # Search entities
    "SearchQuery",
    "SearchType",
    "SearchScope",
    "SearchFilters",
    "SearchResult",
]
