import{d as f,cz as d,al as w,i as u,c as M,o as y,j as a,k as l,a6 as r,n as o,z as i,cA as h,cB as x,cC as z,cD as G}from"./index-ei-kaitd.js";const v=f({__name:"ConcurrencyLimits",setup(R){const{showModal:t,open:b}=d(),{showModal:e,open:m}=d(),_=[{label:"Global"},{label:"Task Run"}],c=w("tab","Global");return(T,s)=>{const p=u("p-button"),k=u("p-tabs"),C=u("p-layout-default");return y(),M(C,{class:"concurrency-limits"},{header:a(()=>[l(o(i),{crumbs:[{text:"Concurrency"}]})]),default:a(()=>[l(k,{selected:o(c),"onUpdate:selected":s[2]||(s[2]=n=>r(c)?c.value=n:null),tabs:_},{global:a(()=>[l(o(i),{size:"lg",crumbs:[{text:"Global Concurrency Limits"}]},{"after-crumbs":a(()=>[l(p,{small:"",icon:"PlusIcon",onClick:o(b)},null,8,["onClick"])]),_:1}),l(o(z),{showModal:o(t),"onUpdate:showModal":s[0]||(s[0]=n=>r(t)?t.value=n:null)},null,8,["showModal"]),l(o(G),{class:"concurrency-limits__global-table"})]),"task-run":a(()=>[l(o(i),{size:"lg",crumbs:[{text:"Task Run Concurrency Limits"}]},{"after-crumbs":a(()=>[l(p,{small:"",icon:"PlusIcon",onClick:o(m)},null,8,["onClick"])]),_:1}),l(o(h),{showModal:o(e),"onUpdate:showModal":s[1]||(s[1]=n=>r(e)?e.value=n:null)},null,8,["showModal"]),l(o(x),{class:"concurrency-limits__task-limits-table"})]),_:1},8,["selected"])]),_:1})}}});export{v as default};
//# sourceMappingURL=ConcurrencyLimits-DPYOMnQN.js.map
