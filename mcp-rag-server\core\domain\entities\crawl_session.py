"""Crawl session entity for the MCP RAG Server."""

from datetime import datetime
from typing import Dict, List, Optional, Set, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid


class CrawlStatus(str, Enum):
    """Crawl session status."""
    PENDING = "pending"
    RUNNING = "running"
    PAUSED = "paused"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class CrawlStrategy(str, Enum):
    """Crawl strategy enumeration."""
    BREADTH_FIRST = "breadth_first"
    DEPTH_FIRST = "depth_first"
    FOCUSED = "focused"
    SITEMAP = "sitemap"


@dataclass
class CrawlConfig:
    """Crawl configuration container."""
    max_depth: int = 3
    max_pages: int = 100
    delay_between_requests: float = 1.0
    concurrent_requests: int = 5
    follow_external_links: bool = False
    respect_robots_txt: bool = True
    user_agent: str = "MCP-RAG-Server/1.0"
    allowed_domains: List[str] = field(default_factory=list)
    excluded_patterns: List[str] = field(default_factory=list)
    include_patterns: List[str] = field(default_factory=list)
    custom_headers: Dict[str, str] = field(default_factory=dict)


@dataclass
class CrawlStats:
    """Crawl statistics container."""
    pages_discovered: int = 0
    pages_crawled: int = 0
    pages_failed: int = 0
    pages_skipped: int = 0
    total_size_bytes: int = 0
    average_response_time: float = 0.0
    errors_by_type: Dict[str, int] = field(default_factory=dict)


@dataclass
class CrawlSession:
    """Crawl session entity."""
    
    # Primary identifiers
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    name: Optional[str] = None
    
    # Configuration
    start_urls: List[str] = field(default_factory=list)
    config: CrawlConfig = field(default_factory=CrawlConfig)
    strategy: CrawlStrategy = CrawlStrategy.BREADTH_FIRST
    
    # Status and progress
    status: CrawlStatus = CrawlStatus.PENDING
    current_depth: int = 0
    
    # URL management
    urls_to_crawl: Set[str] = field(default_factory=set)
    urls_crawled: Set[str] = field(default_factory=set)
    urls_failed: Set[str] = field(default_factory=set)
    urls_skipped: Set[str] = field(default_factory=set)
    
    # Statistics
    stats: CrawlStats = field(default_factory=CrawlStats)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Error handling
    error_message: Optional[str] = None
    last_error_at: Optional[datetime] = None
    
    # Metadata
    tags: List[str] = field(default_factory=list)
    description: Optional[str] = None
    created_by: Optional[str] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if self.start_urls:
            self.urls_to_crawl.update(self.start_urls)
        
        if not self.name:
            self.name = f"Crawl Session {self.created_at.strftime('%Y-%m-%d %H:%M')}"
    
    def start_session(self):
        """Start the crawl session."""
        self.status = CrawlStatus.RUNNING
        self.started_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def complete_session(self):
        """Complete the crawl session."""
        self.status = CrawlStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def fail_session(self, error_message: str):
        """Fail the crawl session."""
        self.status = CrawlStatus.FAILED
        self.error_message = error_message
        self.last_error_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def pause_session(self):
        """Pause the crawl session."""
        self.status = CrawlStatus.PAUSED
        self.updated_at = datetime.utcnow()
    
    def resume_session(self):
        """Resume the crawl session."""
        if self.status == CrawlStatus.PAUSED:
            self.status = CrawlStatus.RUNNING
            self.updated_at = datetime.utcnow()
    
    def cancel_session(self):
        """Cancel the crawl session."""
        self.status = CrawlStatus.CANCELLED
        self.updated_at = datetime.utcnow()
    
    def add_url_to_crawl(self, url: str):
        """Add URL to crawl queue."""
        if url not in self.urls_crawled and url not in self.urls_failed:
            self.urls_to_crawl.add(url)
            self.stats.pages_discovered += 1
            self.updated_at = datetime.utcnow()
    
    def mark_url_crawled(self, url: str, size_bytes: int = 0, response_time: float = 0.0):
        """Mark URL as successfully crawled."""
        self.urls_to_crawl.discard(url)
        self.urls_crawled.add(url)
        self.stats.pages_crawled += 1
        self.stats.total_size_bytes += size_bytes
        
        # Update average response time
        if self.stats.pages_crawled > 1:
            self.stats.average_response_time = (
                (self.stats.average_response_time * (self.stats.pages_crawled - 1) + response_time) /
                self.stats.pages_crawled
            )
        else:
            self.stats.average_response_time = response_time
        
        self.updated_at = datetime.utcnow()
    
    def mark_url_failed(self, url: str, error_type: str = "unknown"):
        """Mark URL as failed."""
        self.urls_to_crawl.discard(url)
        self.urls_failed.add(url)
        self.stats.pages_failed += 1
        self.stats.errors_by_type[error_type] = self.stats.errors_by_type.get(error_type, 0) + 1
        self.updated_at = datetime.utcnow()
    
    def mark_url_skipped(self, url: str):
        """Mark URL as skipped."""
        self.urls_to_crawl.discard(url)
        self.urls_skipped.add(url)
        self.stats.pages_skipped += 1
        self.updated_at = datetime.utcnow()
    
    def get_next_url(self) -> Optional[str]:
        """Get next URL to crawl."""
        if not self.urls_to_crawl:
            return None
        
        if self.strategy == CrawlStrategy.BREADTH_FIRST:
            # Return the first URL (FIFO)
            return next(iter(self.urls_to_crawl))
        elif self.strategy == CrawlStrategy.DEPTH_FIRST:
            # Return the last URL (LIFO)
            return list(self.urls_to_crawl)[-1]
        else:
            # Default to breadth-first
            return next(iter(self.urls_to_crawl))
    
    def is_complete(self) -> bool:
        """Check if crawl session is complete."""
        return (
            not self.urls_to_crawl or
            self.stats.pages_crawled >= self.config.max_pages or
            self.current_depth >= self.config.max_depth
        )
    
    def get_progress_percentage(self) -> float:
        """Get crawl progress as percentage."""
        if self.stats.pages_discovered == 0:
            return 0.0
        
        completed = self.stats.pages_crawled + self.stats.pages_failed + self.stats.pages_skipped
        return min(100.0, (completed / self.stats.pages_discovered) * 100.0)
    
    def get_duration(self) -> Optional[float]:
        """Get session duration in seconds."""
        if not self.started_at:
            return None
        
        end_time = self.completed_at or datetime.utcnow()
        return (end_time - self.started_at).total_seconds()
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "name": self.name,
            "start_urls": list(self.start_urls),
            "config": {
                "max_depth": self.config.max_depth,
                "max_pages": self.config.max_pages,
                "delay_between_requests": self.config.delay_between_requests,
                "concurrent_requests": self.config.concurrent_requests,
                "follow_external_links": self.config.follow_external_links,
                "respect_robots_txt": self.config.respect_robots_txt,
                "user_agent": self.config.user_agent,
                "allowed_domains": self.config.allowed_domains,
                "excluded_patterns": self.config.excluded_patterns,
                "include_patterns": self.config.include_patterns,
                "custom_headers": self.config.custom_headers,
            },
            "strategy": self.strategy.value,
            "status": self.status.value,
            "current_depth": self.current_depth,
            "urls_to_crawl": list(self.urls_to_crawl),
            "urls_crawled": list(self.urls_crawled),
            "urls_failed": list(self.urls_failed),
            "urls_skipped": list(self.urls_skipped),
            "stats": {
                "pages_discovered": self.stats.pages_discovered,
                "pages_crawled": self.stats.pages_crawled,
                "pages_failed": self.stats.pages_failed,
                "pages_skipped": self.stats.pages_skipped,
                "total_size_bytes": self.stats.total_size_bytes,
                "average_response_time": self.stats.average_response_time,
                "errors_by_type": self.stats.errors_by_type,
            },
            "created_at": self.created_at.isoformat(),
            "started_at": self.started_at.isoformat() if self.started_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "updated_at": self.updated_at.isoformat(),
            "error_message": self.error_message,
            "last_error_at": self.last_error_at.isoformat() if self.last_error_at else None,
            "tags": self.tags,
            "description": self.description,
            "created_by": self.created_by,
        }
