{"version": 3, "file": "ArtifactKey-lM7sxe3d.js", "sources": ["../../src/pages/ArtifactKey.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"artifact\">\n    <template #header>\n      <PageHeadingArtifactKey v-if=\"artifact\" :artifact=\"artifact\" />\n    </template>\n\n    <ArtifactDescription v-if=\"artifact\" :artifact=\"artifact\" />\n\n    <template v-if=\"artifact\">\n      <ArtifactTimeline v-if=\"artifact.key\" :artifact-key=\"artifact.key\" />\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    PageHeadingArtifactKey,\n    ArtifactDescription,\n    ArtifactTimeline,\n    localization,\n    useWorkspaceApi\n  } from '@prefecthq/prefect-ui-library'\n  import { useSubscription, useRouteParam } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const artifactKey = useRouteParam('artifactKey')\n\n  const artifactSubscription = useSubscription(api.artifacts.getArtifactCollection, [artifactKey])\n  const artifact = computed(() => artifactSubscription.response)\n\n  const pageTitle = computed<string>(() => {\n    if (!artifact.value) {\n      return localization.info.artifact\n    }\n\n    return `${localization.info.artifact}: ${artifact.value.key}`\n  })\n\n  usePageTitle(pageTitle)\n</script>\n"], "names": ["api", "useWorkspaceApi", "<PERSON><PERSON><PERSON>", "useRouteParam", "artifactSubscription", "useSubscription", "artifact", "computed", "pageTitle", "localization", "usePageTitle", "_createBlock", "_component_p_layout_default", "_unref", "PageHeadingArtifactKey", "ArtifactDescription", "_createElementBlock", "_Fragment", "ArtifactTimeline"], "mappings": "mPA0BE,MAAMA,EAAMC,EAAgB,EACtBC,EAAcC,EAAc,aAAa,EAEzCC,EAAuBC,EAAgBL,EAAI,UAAU,sBAAuB,CAACE,CAAW,CAAC,EACzFI,EAAWC,EAAS,IAAMH,EAAqB,QAAQ,EAEvDI,EAAYD,EAAiB,IAC5BD,EAAS,MAIP,GAAGG,EAAa,KAAK,QAAQ,KAAKH,EAAS,MAAM,GAAG,GAHlDG,EAAa,KAAK,QAI5B,EAED,OAAAC,EAAaF,CAAS,iDAvCtB,EAAAG,EAUmBC,EAAA,CAVD,MAAM,YAAU,CACrB,SACT,IAA+D,CAAjCN,EAAQ,WAAtCK,EAA+DE,EAAAC,CAAA,EAAA,OAAtB,SAAUR,EAAQ,KAAA,6CAG7D,IAA4D,CAAjCA,EAAQ,WAAnCK,EAA4DE,EAAAE,CAAA,EAAA,OAAtB,SAAUT,EAAQ,KAAA,iCAExCA,EAAQ,WAAxBU,EAEWC,EAAA,CAAA,IAAA,GAAA,CADeX,EAAA,MAAS,SAAjCK,EAAqEE,EAAAK,CAAA,EAAA,OAA9B,eAAcZ,EAAQ,MAAC,GAAA"}