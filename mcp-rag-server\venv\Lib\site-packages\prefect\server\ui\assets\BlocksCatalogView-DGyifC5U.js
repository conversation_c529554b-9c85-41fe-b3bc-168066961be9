import{d as k,e as i,W as y,g as o,bF as b,i as m,c as _,a as d,o as f,j as a,k as t,n as c,cl as T,cm as g}from"./index-ei-kaitd.js";import{u as v}from"./usePageTitle-LeBMnqrg.js";const x=k({__name:"BlocksCatalogView",setup(B){const s=i(),l=y("blockTypeSlug"),n=o(()=>l.value?[l.value]:null),u=b(s.blockTypes.getBlockTypeBySlug,n),e=o(()=>u.response),p=o(()=>e.value?`Block Type: ${e.value.name}`:null);return v(p),(C,S)=>{const r=m("p-layout-default");return e.value?(f(),_(r,{key:0,class:"blocks-catalog-view"},{header:a(()=>[t(c(g),{"block-type":e.value},null,8,["block-type"])]),default:a(()=>[t(c(T),{"block-type":e.value},null,8,["block-type"])]),_:1})):d("",!0)}}});export{x as default};
//# sourceMappingURL=BlocksCatalogView-DGyifC5U.js.map
