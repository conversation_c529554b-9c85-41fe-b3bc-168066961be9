#!/usr/bin/env python3
"""Simple test for chunk processor."""

import asyncio
from core.application.services import DocumentService, ChunkService, EmbeddingService
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.domain.entities import DocumentType
from config.settings import get_settings


# Mock chunk repository for testing
class MockChunkRepository:
    def __init__(self):
        self.chunks = {}
        self.next_id = 1
    
    async def create(self, chunk):
        chunk.id = str(self.next_id)
        self.next_id += 1
        self.chunks[chunk.id] = chunk
        return chunk
    
    async def delete_chunks_by_document(self, document_id):
        to_delete = [cid for cid, c in self.chunks.items() if c.document_id == document_id]
        for cid in to_delete:
            del self.chunks[cid]
        return len(to_delete)
    
    async def bulk_create(self, chunks):
        for chunk in chunks:
            await self.create(chunk)
        return chunks
    
    async def bulk_update_embeddings(self, chunk_embeddings):
        for chunk_id, embedding, model in chunk_embeddings:
            if chunk_id in self.chunks:
                self.chunks[chunk_id].embedding = embedding
                self.chunks[chunk_id].embedding_model = model
        return len(chunk_embeddings)
    
    async def get_chunks_by_document(self, document_id, limit=100, offset=0):
        return [c for c in self.chunks.values() if c.document_id == document_id]
    
    async def get_statistics(self):
        total = len(self.chunks)
        with_embeddings = len([c for c in self.chunks.values() if c.embedding])
        return {
            "total_chunks": total,
            "chunks_with_embeddings": with_embeddings,
        }
    
    async def get_chunks_without_embeddings(self, limit=100, embedding_model=None):
        return [c for c in self.chunks.values() if not c.embedding][:limit]
    
    async def get_random_chunks(self, limit=10):
        return list(self.chunks.values())[:limit]


async def test_simple_chunk_processor():
    print('🧩 Testing Simple Chunk Processor')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    # Use mock chunk repository
    chunk_repo = MockChunkRepository()
    chunk_service = ChunkService(chunk_repo)
    
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repo,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    chunk_processor = ChunkProcessor(
        document_service=doc_service,
        chunk_service=chunk_service,
        embedding_service=embedding_service,
        chunk_size=300,  # Small for testing
        chunk_overlap=50,
    )
    
    print('✅ ChunkProcessor created successfully')
    
    # Create a test document
    test_content = """
    This is a test document for the MCP RAG Server chunk processor.
    The document contains multiple paragraphs to test the chunking functionality.
    Each paragraph should be processed correctly and split into appropriate chunks.
    The embedding system should generate vector representations for each chunk.
    """
    
    document = await doc_service.create_document(
        url="https://test.example.com/simple-test",
        content=test_content,
        document_type=DocumentType.HTML,
        metadata={"title": "Simple Test Document"},
    )
    
    print(f'✅ Created test document: {document.id}')
    
    # Process the document
    success = await chunk_processor.process_document(document.id)
    
    if success:
        print('✅ Document processed successfully')
        
        # Check chunks
        chunks = await chunk_service.get_chunks_by_document(document.id)
        print(f'   Created {len(chunks)} chunks')
        
        for i, chunk in enumerate(chunks):
            embedding_info = f"{len(chunk.embedding)} dims" if chunk.embedding else "no embedding"
            print(f'   Chunk {i+1}: {len(chunk.content)} chars, {embedding_info}')
    else:
        print('❌ Document processing failed')
    
    # Get statistics
    stats = chunk_processor.get_statistics()
    print(f'\n📊 Processor statistics:')
    for key, value in stats.items():
        print(f'   {key}: {value}')
    
    print('\n🎉 Simple chunk processor test completed!')


if __name__ == "__main__":
    asyncio.run(test_simple_chunk_processor())
