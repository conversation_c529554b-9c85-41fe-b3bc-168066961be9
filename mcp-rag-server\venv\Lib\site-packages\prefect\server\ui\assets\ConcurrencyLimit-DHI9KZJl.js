import{d as x,e as g,W as h,u as D,g as i,aI as I,al as R,bt as S,f as T,i as u,c,o as n,j as a,k as V,n as t,a6 as W,a as r,cw as B,cx as m,cy as N,be as P}from"./index-ei-kaitd.js";import{u as j}from"./usePageTitle-LeBMnqrg.js";const Z=x({__name:"ConcurrencyLimit",setup(A){const p=g(),y=h("concurrencyLimitId"),d=D(),v=i(()=>[{label:"Details",hidden:I.xl},{label:"Active Task Runs"}]),s=R("tab","Details"),{tabs:_}=S(v,s),b={interval:3e5},L=T(p.concurrencyLimits.getConcurrencyLimit,[y.value],b),e=i(()=>L.response);function f(){d.push(P.concurrencyLimits())}const C=i(()=>e.value?`Concurrency Limit: ${e.value.tag}`:"Concurrency Limit");return j(C),(F,l)=>{const k=u("p-tabs"),w=u("p-layout-well");return n(),c(w,{class:"concurrencyLimit"},{header:a(()=>[e.value?(n(),c(t(N),{key:0,"concurrency-limit":e.value,onDelete:f},null,8,["concurrency-limit"])):r("",!0)]),well:a(()=>[e.value?(n(),c(t(m),{key:0,alternate:"","concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),default:a(()=>[V(k,{selected:t(s),"onUpdate:selected":l[0]||(l[0]=o=>W(s)?s.value=o:null),tabs:t(_)},{details:a(()=>[e.value?(n(),c(t(m),{key:0,"concurrency-limit":e.value},null,8,["concurrency-limit"])):r("",!0)]),"active-task-runs":a(()=>{var o;return[(o=e.value)!=null&&o.activeSlots?(n(),c(t(B),{key:0,"active-slots":e.value.activeSlots},null,8,["active-slots"])):r("",!0)]}),_:1},8,["selected","tabs"])]),_:1})}}});export{Z as default};
//# sourceMappingURL=ConcurrencyLimit-DHI9KZJl.js.map
