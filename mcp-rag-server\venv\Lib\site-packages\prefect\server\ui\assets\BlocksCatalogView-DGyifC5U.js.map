{"version": 3, "file": "BlocksCatalogView-DGyifC5U.js", "sources": ["../../src/pages/BlocksCatalogView.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"blockType\" class=\"blocks-catalog-view\">\n    <template #header>\n      <PageHeadingBlocksCatalogView :block-type=\"blockType\" />\n    </template>\n\n    <BlockTypeCard :block-type=\"blockType\" />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingBlocksCatalogView, BlockTypeCard, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam, useSubscriptionWithDependencies } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const blockTypeSlugParam = useRouteParam('blockTypeSlug')\n  const blockTypeSubscriptionArgs = computed<Parameters<typeof api.blockTypes.getBlockTypeBySlug> | null>(() => {\n    if (!blockTypeSlugParam.value) {\n      return null\n    }\n\n    return [blockTypeSlugParam.value]\n  })\n\n  const blockTypeSubscription = useSubscriptionWithDependencies(api.blockTypes.getBlockTypeBySlug, blockTypeSubscriptionArgs)\n  const blockType = computed(() => blockTypeSubscription.response)\n\n  const blockTypeTitle = computed<string | null>(() => {\n    if (!blockType.value) {\n      return null\n    }\n    return `Block Type: ${blockType.value.name}`\n  })\n  usePageTitle(blockTypeTitle)\n</script>"], "names": ["api", "useWorkspaceApi", "blockTypeSlugParam", "useRouteParam", "blockTypeSubscriptionArgs", "computed", "blockTypeSubscription", "useSubscriptionWithDependencies", "blockType", "blockTypeTitle", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingBlocksCatalogView", "BlockTypeCard"], "mappings": "oOAgBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAqBC,EAAc,eAAe,EAClDC,EAA4BC,EAAsE,IACjGH,EAAmB,MAIjB,CAACA,EAAmB,KAAK,EAHvB,IAIV,EAEKI,EAAwBC,EAAgCP,EAAI,WAAW,mBAAoBI,CAAyB,EACpHI,EAAYH,EAAS,IAAMC,EAAsB,QAAQ,EAEzDG,EAAiBJ,EAAwB,IACxCG,EAAU,MAGR,eAAeA,EAAU,MAAM,IAAI,GAFjC,IAGV,EACD,OAAAE,EAAaD,CAAc,+CAlCHD,EAAS,WAAjCG,EAMmBC,EAAA,OANgB,MAAM,qBAAA,GAC5B,SACT,IAAwD,CAAxDC,EAAwDC,EAAAC,CAAA,EAAA,CAAzB,aAAYP,EAAS,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA,aAGtD,IAAyC,CAAzCK,EAAyCC,EAAAE,CAAA,EAAA,CAAzB,aAAYR,EAAS,OAAA,KAAA,EAAA,CAAA,YAAA,CAAA,CAAA"}