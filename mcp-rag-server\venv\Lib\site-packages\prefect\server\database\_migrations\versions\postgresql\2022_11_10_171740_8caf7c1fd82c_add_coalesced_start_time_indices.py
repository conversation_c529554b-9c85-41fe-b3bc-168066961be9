"""Add indices for coalesced `start time` sorts

Revision ID: 8caf7c1fd82c
Revises: 54c1876c68ae
Create Date: 2022-11-10 17:17:40.018108

"""

from alembic import op

# revision identifiers, used by Alembic.
revision = "8caf7c1fd82c"
down_revision = "54c1876c68ae"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.get_context().autocommit_block():
        op.execute(
            """
            CREATE INDEX CONCURRENTLY
            ix_flow_run__coalesce_start_time_expected_start_time_asc
            ON flow_run (coalesce(start_time, expected_start_time) ASC);
            """
        )

    with op.get_context().autocommit_block():
        op.execute(
            """
            CREATE INDEX CONCURRENTLY
            ix_flow_run__coalesce_start_time_expected_start_time_desc
            ON flow_run (coalesce(start_time, expected_start_time) DESC);
            """
        )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    with op.get_context().autocommit_block():
        op.execute(
            """
            DROP INDEX CONCURRENTLY ix_flow_run__coalesce_start_time_expected_start_time_desc;
            """
        )

        op.execute(
            """
            DROP INDEX CONCURRENTLY ix_flow_run__coalesce_start_time_expected_start_time_asc;
            """
        )
    # ### end Alembic commands ###
