"""Simple PDF crawler using only pymupdf4llm."""

import asyncio
import aiohttp
import tempfile
import os
import time
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
import logging

import pymupdf4llm

from core.domain.entities import Document, DocumentType, DocumentStatus
from core.application.services import DocumentService


@dataclass
class PDFResult:
    """Result of PDF processing."""
    url: str
    content: str
    metadata: Dict[str, Any]
    chunks: List[str]
    error: Optional[str] = None
    file_size: int = 0
    page_count: int = 0
    processing_time: float = 0.0


class SimplePDFCrawler:
    """Simple PDF crawler using pymupdf4llm."""
    
    def __init__(
        self,
        document_service: DocumentService,
        max_file_size: int = 50 * 1024 * 1024,  # 50MB
        timeout: int = 60,
        user_agent: str = "MCP-RAG-Server/1.0",
    ):
        self.document_service = document_service
        self.max_file_size = max_file_size
        self.timeout = timeout
        self.user_agent = user_agent
        
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            "pdfs_processed": 0,
            "pdfs_failed": 0,
            "total_size_bytes": 0,
            "total_pages": 0,
            "average_processing_time": 0.0,
        }
    
    async def download_and_process_pdf(
        self,
        url: str,
        crawl_session_id: Optional[str] = None,
    ) -> PDFResult:
        """Download and process a PDF file."""
        start_time = time.time()
        
        try:
            # Download PDF
            pdf_data = await self._download_pdf(url)
            
            if not pdf_data:
                return PDFResult(
                    url=url,
                    content="",
                    metadata={},
                    chunks=[],
                    error="Failed to download PDF",
                )
            
            # Process PDF
            result = await self._process_pdf_data(url, pdf_data)
            result.processing_time = time.time() - start_time
            
            # Create document if successful
            if not result.error and crawl_session_id:
                await self._create_document(result, crawl_session_id)
            
            # Update statistics
            if result.error:
                self.stats["pdfs_failed"] += 1
            else:
                self.stats["pdfs_processed"] += 1
                self.stats["total_size_bytes"] += result.file_size
                self.stats["total_pages"] += result.page_count
                self._update_average_processing_time(result.processing_time)
            
            return result
            
        except Exception as e:
            self.stats["pdfs_failed"] += 1
            error_msg = str(e)
            self.logger.error(f"Error processing PDF {url}: {error_msg}")
            
            return PDFResult(
                url=url,
                content="",
                metadata={},
                chunks=[],
                error=error_msg,
                processing_time=time.time() - start_time,
            )
    
    async def _download_pdf(self, url: str) -> Optional[bytes]:
        """Download PDF file from URL."""
        try:
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "application/pdf,*/*",
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                ) as response:
                    
                    # Check file size
                    content_length = response.headers.get("content-length")
                    if content_length and int(content_length) > self.max_file_size:
                        raise Exception(f"PDF file too large: {content_length} bytes")
                    
                    # Download content
                    pdf_data = await response.read()
                    
                    if len(pdf_data) > self.max_file_size:
                        raise Exception(f"PDF file too large: {len(pdf_data)} bytes")
                    
                    return pdf_data
                    
        except Exception as e:
            self.logger.error(f"Error downloading PDF {url}: {e}")
            return None
    
    async def _process_pdf_data(self, url: str, pdf_data: bytes) -> PDFResult:
        """Process PDF data and extract content."""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                temp_file.write(pdf_data)
                temp_path = temp_file.name
            
            try:
                # Extract markdown content
                md_text = pymupdf4llm.to_markdown(temp_path)
                
                # Basic metadata
                metadata = {
                    "source": "pymupdf4llm",
                    "file_size": len(pdf_data),
                    "content_length": len(md_text),
                }
                
                # Try to get page count
                try:
                    import fitz  # PyMuPDF
                    doc = fitz.open(temp_path)
                    page_count = len(doc)
                    doc.close()
                    metadata["page_count"] = page_count
                except:
                    page_count = max(1, len(md_text) // 2000)  # Rough estimate
                    metadata["page_count"] = page_count
                
                # Simple chunking
                chunks = self._simple_chunk_text(md_text)
                metadata["chunks_count"] = len(chunks)
                
                return PDFResult(
                    url=url,
                    content=md_text,
                    metadata=metadata,
                    chunks=chunks,
                    file_size=len(pdf_data),
                    page_count=page_count,
                )
                
            finally:
                # Clean up temporary file (with retry)
                for i in range(3):
                    try:
                        os.unlink(temp_path)
                        break
                    except PermissionError:
                        if i < 2:
                            await asyncio.sleep(0.1)
                        else:
                            self.logger.warning(f"Could not delete temp file: {temp_path}")
                    
        except Exception as e:
            return PDFResult(
                url=url,
                content="",
                metadata={},
                chunks=[],
                error=str(e),
                file_size=len(pdf_data),
            )
    
    def _simple_chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Simple text chunking with overlap."""
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            chunk = text[start:end].strip()
            
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= end:
                break
        
        return chunks
    
    async def _create_document(self, result: PDFResult, crawl_session_id: str):
        """Create document from PDF processing result."""
        try:
            await self.document_service.create_document(
                url=result.url,
                content=result.content,
                document_type=DocumentType.PDF,
                metadata=result.metadata,
                crawl_session_id=crawl_session_id,
            )
        except Exception as e:
            self.logger.error(f"Error creating document for {result.url}: {e}")
    
    def _update_average_processing_time(self, processing_time: float):
        """Update average processing time."""
        current_avg = self.stats["average_processing_time"]
        pdfs_processed = self.stats["pdfs_processed"]
        
        if pdfs_processed == 1:
            self.stats["average_processing_time"] = processing_time
        else:
            self.stats["average_processing_time"] = (
                (current_avg * (pdfs_processed - 1) + processing_time) / pdfs_processed
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get crawler statistics."""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset crawler statistics."""
        self.stats = {
            "pdfs_processed": 0,
            "pdfs_failed": 0,
            "total_size_bytes": 0,
            "total_pages": 0,
            "average_processing_time": 0.0,
        }
