"""Automation event follower

Revision ID: 916718e8330f
Revises: bd6efa529f03
Create Date: 2024-04-09 12:56:58.865815

"""

import sqlalchemy as sa
from alembic import op

import prefect
import prefect.server.utilities.database
from prefect.server.events.schemas.events import ReceivedEvent

# revision identifiers, used by Alembic.
revision = "916718e8330f"
down_revision = "bd6efa529f03"
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table(
        "automation_event_follower",
        sa.Column(
            "leader_event_id", prefect.server.utilities.database.UUID(), nullable=False
        ),
        sa.Column(
            "follower_event_id",
            prefect.server.utilities.database.UUID(),
            nullable=False,
        ),
        sa.Column(
            "received",
            prefect.server.utilities.database.Timestamp(timezone=True),
            nullable=False,
        ),
        sa.Column(
            "follower",
            prefect.server.utilities.database.Pydantic(ReceivedEvent),
            nullable=False,
        ),
        sa.Column(
            "id",
            prefect.server.utilities.database.UUID(),
            server_default=sa.text("(GEN_RANDOM_UUID())"),
            nullable=False,
        ),
        sa.Column(
            "created",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.Column(
            "updated",
            prefect.server.utilities.database.Timestamp(timezone=True),
            server_default=sa.text("CURRENT_TIMESTAMP"),
            nullable=False,
        ),
        sa.PrimaryKeyConstraint("id", name=op.f("pk_automation_event_follower")),
        sa.UniqueConstraint(
            "follower_event_id",
            name=op.f("uq_automation_event_follower__follower_event_id"),
        ),
    )
    op.create_index(
        op.f("ix_automation_event_follower__leader_event_id"),
        "automation_event_follower",
        ["leader_event_id"],
        unique=False,
    )
    op.create_index(
        op.f("ix_automation_event_follower__received"),
        "automation_event_follower",
        ["received"],
        unique=False,
    )
    op.create_index(
        op.f("ix_automation_event_follower__updated"),
        "automation_event_follower",
        ["updated"],
        unique=False,
    )


def downgrade():
    op.drop_index(
        op.f("ix_automation_event_follower__updated"),
        table_name="automation_event_follower",
    )
    op.drop_index(
        op.f("ix_automation_event_follower__received"),
        table_name="automation_event_follower",
    )
    op.drop_index(
        op.f("ix_automation_event_follower__leader_event_id"),
        table_name="automation_event_follower",
    )
    op.drop_table("automation_event_follower")
    # ### end Alembic commands ###
