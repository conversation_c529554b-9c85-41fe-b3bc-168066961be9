"""PDF crawler and processor for MCP RAG Server."""

import asyncio
import aiohttp
import aiofiles
import tempfile
import os
from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from pathlib import Path
import logging

import pymupdf4llm
from unstructured.partition.pdf import partition_pdf
from unstructured.chunking.title import chunk_by_title

from core.domain.entities import Document, DocumentType, DocumentStatus
from core.application.services import DocumentService


@dataclass
class PDFResult:
    """Result of PDF processing."""
    url: str
    content: str
    metadata: Dict[str, Any]
    chunks: List[str]
    error: Optional[str] = None
    file_size: int = 0
    page_count: int = 0
    processing_time: float = 0.0


class PDFCrawler:
    """PDF crawler and processor."""
    
    def __init__(
        self,
        document_service: DocumentService,
        max_file_size: int = 50 * 1024 * 1024,  # 50MB
        timeout: int = 60,
        user_agent: str = "MCP-RAG-Server/1.0",
        use_unstructured: bool = True,
        fallback_to_pymupdf: bool = True,
    ):
        self.document_service = document_service
        self.max_file_size = max_file_size
        self.timeout = timeout
        self.user_agent = user_agent
        self.use_unstructured = use_unstructured
        self.fallback_to_pymupdf = fallback_to_pymupdf
        
        self.logger = logging.getLogger(__name__)
        
        # Statistics
        self.stats = {
            "pdfs_processed": 0,
            "pdfs_failed": 0,
            "total_size_bytes": 0,
            "total_pages": 0,
            "average_processing_time": 0.0,
        }
    
    async def download_and_process_pdf(
        self,
        url: str,
        crawl_session_id: Optional[str] = None,
    ) -> PDFResult:
        """Download and process a PDF file."""
        import time
        start_time = time.time()
        
        try:
            # Download PDF
            pdf_data = await self._download_pdf(url)
            
            if not pdf_data:
                return PDFResult(
                    url=url,
                    content="",
                    metadata={},
                    chunks=[],
                    error="Failed to download PDF",
                )
            
            # Process PDF
            result = await self._process_pdf_data(url, pdf_data)
            result.processing_time = time.time() - start_time
            
            # Create document if successful
            if not result.error and crawl_session_id:
                await self._create_document(result, crawl_session_id)
            
            # Update statistics
            if result.error:
                self.stats["pdfs_failed"] += 1
            else:
                self.stats["pdfs_processed"] += 1
                self.stats["total_size_bytes"] += result.file_size
                self.stats["total_pages"] += result.page_count
                self._update_average_processing_time(result.processing_time)
            
            return result
            
        except Exception as e:
            self.stats["pdfs_failed"] += 1
            error_msg = str(e)
            self.logger.error(f"Error processing PDF {url}: {error_msg}")
            
            return PDFResult(
                url=url,
                content="",
                metadata={},
                chunks=[],
                error=error_msg,
                processing_time=time.time() - start_time,
            )
    
    async def _download_pdf(self, url: str) -> Optional[bytes]:
        """Download PDF file from URL."""
        try:
            headers = {
                "User-Agent": self.user_agent,
                "Accept": "application/pdf,*/*",
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                ) as response:
                    
                    # Check content type
                    content_type = response.headers.get("content-type", "").lower()
                    if "pdf" not in content_type:
                        self.logger.warning(f"URL {url} does not appear to be a PDF: {content_type}")
                    
                    # Check file size
                    content_length = response.headers.get("content-length")
                    if content_length and int(content_length) > self.max_file_size:
                        raise Exception(f"PDF file too large: {content_length} bytes")
                    
                    # Download content
                    pdf_data = await response.read()
                    
                    if len(pdf_data) > self.max_file_size:
                        raise Exception(f"PDF file too large: {len(pdf_data)} bytes")
                    
                    return pdf_data
                    
        except Exception as e:
            self.logger.error(f"Error downloading PDF {url}: {e}")
            return None
    
    async def _process_pdf_data(self, url: str, pdf_data: bytes) -> PDFResult:
        """Process PDF data and extract content."""
        try:
            # Save to temporary file
            with tempfile.NamedTemporaryFile(suffix=".pdf", delete=False) as temp_file:
                temp_file.write(pdf_data)
                temp_path = temp_file.name
            
            try:
                # Try unstructured first
                if self.use_unstructured:
                    try:
                        result = await self._process_with_unstructured(url, temp_path, pdf_data)
                        if result and not result.error:
                            return result
                    except Exception as e:
                        self.logger.warning(f"Unstructured failed for {url}: {e}")
                
                # Fallback to pymupdf4llm
                if self.fallback_to_pymupdf:
                    try:
                        result = await self._process_with_pymupdf(url, temp_path, pdf_data)
                        if result:
                            return result
                    except Exception as e:
                        self.logger.warning(f"PyMuPDF failed for {url}: {e}")
                
                return PDFResult(
                    url=url,
                    content="",
                    metadata={},
                    chunks=[],
                    error="All PDF processing methods failed",
                    file_size=len(pdf_data),
                )
                
            finally:
                # Clean up temporary file
                try:
                    os.unlink(temp_path)
                except:
                    pass
                    
        except Exception as e:
            return PDFResult(
                url=url,
                content="",
                metadata={},
                chunks=[],
                error=str(e),
                file_size=len(pdf_data),
            )
    
    async def _process_with_unstructured(
        self,
        url: str,
        temp_path: str,
        pdf_data: bytes,
    ) -> Optional[PDFResult]:
        """Process PDF using unstructured library."""
        try:
            # Partition PDF into elements
            elements = partition_pdf(
                filename=temp_path,
                strategy="hi_res",  # High resolution for better accuracy
                infer_table_structure=True,
                extract_images_in_pdf=False,  # Skip images for now
                extract_image_block_types=["Image", "Table"],
            )
            
            # Extract text content
            content_parts = []
            metadata = {
                "source": "unstructured",
                "elements_count": len(elements),
                "element_types": {},
            }
            
            for element in elements:
                if hasattr(element, 'text') and element.text.strip():
                    content_parts.append(element.text.strip())
                
                # Count element types
                element_type = type(element).__name__
                metadata["element_types"][element_type] = metadata["element_types"].get(element_type, 0) + 1
            
            content = "\n\n".join(content_parts)
            
            # Chunk content
            chunks = []
            if elements:
                try:
                    chunked_elements = chunk_by_title(
                        elements,
                        max_characters=1000,
                        combine_text_under_n_chars=100,
                    )
                    
                    for chunk in chunked_elements:
                        if hasattr(chunk, 'text') and chunk.text.strip():
                            chunks.append(chunk.text.strip())
                            
                except Exception as e:
                    self.logger.warning(f"Chunking failed for {url}: {e}")
                    # Fallback to simple chunking
                    chunks = self._simple_chunk_text(content)
            
            # Get page count (estimate)
            page_count = max(1, len(content) // 2000)  # Rough estimate
            
            metadata.update({
                "file_size": len(pdf_data),
                "page_count": page_count,
                "content_length": len(content),
                "chunks_count": len(chunks),
            })
            
            return PDFResult(
                url=url,
                content=content,
                metadata=metadata,
                chunks=chunks,
                file_size=len(pdf_data),
                page_count=page_count,
            )
            
        except Exception as e:
            self.logger.error(f"Unstructured processing failed for {url}: {e}")
            return None
    
    async def _process_with_pymupdf(
        self,
        url: str,
        temp_path: str,
        pdf_data: bytes,
    ) -> Optional[PDFResult]:
        """Process PDF using pymupdf4llm library."""
        try:
            # Extract markdown content
            md_text = pymupdf4llm.to_markdown(temp_path)
            
            # Basic metadata
            metadata = {
                "source": "pymupdf4llm",
                "file_size": len(pdf_data),
                "content_length": len(md_text),
            }
            
            # Try to get page count from PyMuPDF
            try:
                import fitz  # PyMuPDF
                doc = fitz.open(temp_path)
                page_count = len(doc)
                doc.close()
                metadata["page_count"] = page_count
            except:
                page_count = max(1, len(md_text) // 2000)  # Rough estimate
                metadata["page_count"] = page_count
            
            # Simple chunking
            chunks = self._simple_chunk_text(md_text)
            metadata["chunks_count"] = len(chunks)
            
            return PDFResult(
                url=url,
                content=md_text,
                metadata=metadata,
                chunks=chunks,
                file_size=len(pdf_data),
                page_count=page_count,
            )
            
        except Exception as e:
            self.logger.error(f"PyMuPDF processing failed for {url}: {e}")
            return None
    
    def _simple_chunk_text(self, text: str, chunk_size: int = 1000, overlap: int = 200) -> List[str]:
        """Simple text chunking with overlap."""
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = min(start + chunk_size, len(text))
            chunk = text[start:end].strip()
            
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
            if start >= end:
                break
        
        return chunks
    
    async def _create_document(self, result: PDFResult, crawl_session_id: str):
        """Create document from PDF processing result."""
        try:
            await self.document_service.create_document(
                url=result.url,
                content=result.content,
                document_type=DocumentType.PDF,
                metadata=result.metadata,
                crawl_session_id=crawl_session_id,
            )
        except Exception as e:
            self.logger.error(f"Error creating document for {result.url}: {e}")
    
    def _update_average_processing_time(self, processing_time: float):
        """Update average processing time."""
        current_avg = self.stats["average_processing_time"]
        pdfs_processed = self.stats["pdfs_processed"]
        
        if pdfs_processed == 1:
            self.stats["average_processing_time"] = processing_time
        else:
            self.stats["average_processing_time"] = (
                (current_avg * (pdfs_processed - 1) + processing_time) / pdfs_processed
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get crawler statistics."""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset crawler statistics."""
        self.stats = {
            "pdfs_processed": 0,
            "pdfs_failed": 0,
            "total_size_bytes": 0,
            "total_pages": 0,
            "average_processing_time": 0.0,
        }
