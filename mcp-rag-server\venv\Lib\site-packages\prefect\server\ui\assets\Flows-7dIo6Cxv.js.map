{"version": 3, "file": "Flows-7dIo6Cxv.js", "sources": ["../../src/pages/Flows.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"flows\">\n    <template #header>\n      <PageHeadingFlows />\n    </template>\n\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <FlowsPageEmptyState />\n      </template>\n\n      <template v-else>\n        <FlowList selectable @delete=\"handleDelete\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { FlowList, FlowsPageEmptyState, PageHeadingFlows, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const subscriptionOptions = {\n    interval: 30000,\n  }\n\n  const flowsCountSubscription = useSubscription(api.flows.getFlowsCount, [{}], subscriptionOptions)\n  const flowsCount = computed(() => flowsCountSubscription.response ?? 0)\n  const empty = computed(() => flowsCountSubscription.executed && flowsCount.value === 0)\n  const loaded = computed(() => flowsCountSubscription.executed)\n\n\n  const handleDelete = (): void => {\n    flowsCountSubscription.refresh()\n  }\n\n  usePageTitle('Flows')\n</script>\n"], "names": ["api", "useWorkspaceApi", "subscriptionOptions", "flowsCountSubscription", "useSubscription", "flowsCount", "computed", "empty", "loaded", "handleDelete", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingFlows", "_createElementBlock", "_Fragment", "FlowsPageEmptyState", "FlowList"], "mappings": "sOAwBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAAyBC,EAAgBJ,EAAI,MAAM,cAAe,CAAC,CAAA,CAAE,EAAGE,CAAmB,EAC3FG,EAAaC,EAAS,IAAMH,EAAuB,UAAY,CAAC,EAChEI,EAAQD,EAAS,IAAMH,EAAuB,UAAYE,EAAW,QAAU,CAAC,EAChFG,EAASF,EAAS,IAAMH,EAAuB,QAAQ,EAGvDM,EAAe,IAAY,CAC/BN,EAAuB,QAAQ,CACjC,EAEA,OAAAO,EAAa,OAAO,iDAtCpB,EAAAC,EAcmBC,EAAA,CAdD,MAAM,SAAO,CAClB,SACT,IAAoB,CAApBC,EAAoBC,EAAAC,CAAA,CAAA,CAAA,aAGtB,IAQW,CARKP,EAAM,WAAtBQ,EAQWC,EAAA,CAAA,IAAA,GAAA,CAPOV,EAAK,WACnBI,EAAuBG,EAAAI,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,QAIvBP,EAA8CG,EAAAK,CAAA,EAAA,OAApC,WAAA,GAAY,SAAQV,CAAA"}