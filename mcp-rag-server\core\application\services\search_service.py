"""Search service for the MCP RAG Server."""

import time
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from ...domain.entities import (
    SearchQuery, SearchType, SearchScope, SearchFilters, SearchResult,
    Chunk, Document
)
from ...domain.repositories import (
    SearchQueryRepository, ChunkRepository, DocumentRepository
)
from .embedding_service import EmbeddingService


class SearchService:
    """Service for search operations."""
    
    def __init__(
        self,
        search_query_repository: SearchQueryRepository,
        chunk_repository: ChunkRepository,
        document_repository: DocumentRepository,
        embedding_service: EmbeddingService,
    ):
        self.search_query_repository = search_query_repository
        self.chunk_repository = chunk_repository
        self.document_repository = document_repository
        self.embedding_service = embedding_service
    
    async def create_search_query(
        self,
        query_text: str,
        search_type: SearchType = SearchType.SEMANTIC,
        search_scope: SearchScope = SearchScope.ALL,
        filters: Optional[SearchFilters] = None,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        context: Optional[Dict[str, Any]] = None,
    ) -> SearchQuery:
        """Create a new search query."""
        if filters is None:
            filters = SearchFilters()
        
        search_query = SearchQuery(
            query_text=query_text,
            search_type=search_type,
            search_scope=search_scope,
            filters=filters,
            user_id=user_id,
            session_id=session_id,
            context=context or {},
        )
        
        return await self.search_query_repository.create(search_query)
    
    async def execute_search(
        self,
        query: SearchQuery,
        use_cache: bool = True,
    ) -> SearchQuery:
        """Execute a search query."""
        start_time = time.time()
        
        # Check cache first
        if use_cache:
            cached_results = await self.search_query_repository.get_cached_results(
                query.query_text
            )
            if cached_results:
                query.results = [SearchResult(**r) for r in cached_results]
                query.total_results = len(query.results)
                query.cache_hits = 1
                query.executed_at = datetime.utcnow()
                return await self.search_query_repository.update(query)
        
        # Execute search based on type
        if query.search_type == SearchType.SEMANTIC:
            results = await self._semantic_search(query)
        elif query.search_type == SearchType.KEYWORD:
            results = await self._keyword_search(query)
        elif query.search_type == SearchType.HYBRID:
            results = await self._hybrid_search(query)
        elif query.search_type == SearchType.SIMILARITY:
            results = await self._similarity_search(query)
        else:
            results = []
        
        # Record execution
        execution_time = (time.time() - start_time) * 1000  # Convert to milliseconds
        query.execute_search(results, execution_time)
        
        # Cache results
        if use_cache and results:
            await self.search_query_repository.cache_query_results(
                query.query_text,
                [r.to_dict() for r in results],
                ttl_seconds=3600,
            )
        
        return await self.search_query_repository.update(query)
    
    async def _semantic_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform semantic search using embeddings."""
        # Generate embedding for query
        query_embedding = await self.embedding_service.generate_embedding(query.query_text)
        query.embedding = query_embedding
        query.embedding_model = self.embedding_service.model
        
        # Get document IDs if filtering by domain or other criteria
        document_ids = await self._get_filtered_document_ids(query.filters)
        
        # Search chunks by embedding
        chunk_results = await self.chunk_repository.search_by_embedding(
            embedding=query_embedding,
            limit=query.filters.max_results,
            similarity_threshold=query.similarity_threshold,
            document_ids=document_ids,
        )
        
        # Convert to search results
        results = []
        for chunk, score in chunk_results:
            if score >= (query.filters.min_relevance_score or 0.0):
                result = SearchResult(
                    id=chunk.id,
                    content=chunk.content,
                    score=score,
                    document_id=chunk.document_id,
                    chunk_id=chunk.id,
                    metadata=chunk.metadata.__dict__ if chunk.metadata else {},
                    highlights=self._extract_highlights(chunk.content, query.query_text),
                )
                results.append(result)
        
        return results
    
    async def _keyword_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform keyword-based search."""
        # Get document IDs if filtering
        document_ids = await self._get_filtered_document_ids(query.filters)
        
        # Search chunks by text
        chunks = await self.chunk_repository.search_by_text(
            query=query.query_text,
            limit=query.filters.max_results,
            document_ids=document_ids,
        )
        
        # Convert to search results with basic scoring
        results = []
        for chunk in chunks:
            score = self._calculate_keyword_score(chunk.content, query.query_text)
            
            if score >= (query.filters.min_relevance_score or 0.0):
                result = SearchResult(
                    id=chunk.id,
                    content=chunk.content,
                    score=score,
                    document_id=chunk.document_id,
                    chunk_id=chunk.id,
                    metadata=chunk.metadata.__dict__ if chunk.metadata else {},
                    highlights=self._extract_highlights(chunk.content, query.query_text),
                )
                results.append(result)
        
        # Sort by score
        results.sort(key=lambda r: r.score, reverse=True)
        return results[:query.filters.max_results]
    
    async def _hybrid_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform hybrid search combining semantic and keyword approaches."""
        # Execute both searches
        semantic_query = SearchQuery(
            query_text=query.query_text,
            search_type=SearchType.SEMANTIC,
            filters=query.filters,
            similarity_threshold=query.similarity_threshold,
        )
        
        keyword_query = SearchQuery(
            query_text=query.query_text,
            search_type=SearchType.KEYWORD,
            filters=query.filters,
        )
        
        semantic_results = await self._semantic_search(semantic_query)
        keyword_results = await self._keyword_search(keyword_query)
        
        # Combine and re-rank results
        combined_results = self._combine_search_results(
            semantic_results, keyword_results, 
            semantic_weight=0.7, keyword_weight=0.3
        )
        
        return combined_results[:query.filters.max_results]
    
    async def _similarity_search(self, query: SearchQuery) -> List[SearchResult]:
        """Perform similarity search based on a reference chunk."""
        # Extract chunk ID from context
        chunk_id = query.context.get("reference_chunk_id")
        if not chunk_id:
            return []
        
        # Find similar chunks
        similar_chunks = await self.chunk_repository.get_similar_chunks(
            chunk_id=chunk_id,
            limit=query.filters.max_results,
            similarity_threshold=query.similarity_threshold,
        )
        
        # Convert to search results
        results = []
        for chunk, score in similar_chunks:
            result = SearchResult(
                id=chunk.id,
                content=chunk.content,
                score=score,
                document_id=chunk.document_id,
                chunk_id=chunk.id,
                metadata=chunk.metadata.__dict__ if chunk.metadata else {},
            )
            results.append(result)
        
        return results
    
    async def _get_filtered_document_ids(self, filters: SearchFilters) -> Optional[List[str]]:
        """Get document IDs based on filters."""
        if not any([filters.document_types, filters.domains, filters.date_from, filters.date_to]):
            return None
        
        # This would need to be implemented based on your specific filtering needs
        # For now, return None to search all documents
        return None
    
    def _extract_highlights(self, content: str, query: str, max_highlights: int = 3) -> List[str]:
        """Extract highlighted snippets from content."""
        highlights = []
        query_words = query.lower().split()
        content_lower = content.lower()
        
        for word in query_words:
            if word in content_lower:
                # Find the position and extract surrounding context
                pos = content_lower.find(word)
                start = max(0, pos - 50)
                end = min(len(content), pos + len(word) + 50)
                snippet = content[start:end].strip()
                
                if snippet and snippet not in highlights:
                    highlights.append(snippet)
                    if len(highlights) >= max_highlights:
                        break
        
        return highlights
    
    def _calculate_keyword_score(self, content: str, query: str) -> float:
        """Calculate keyword-based relevance score."""
        content_lower = content.lower()
        query_words = query.lower().split()
        
        if not query_words:
            return 0.0
        
        # Count word matches
        matches = sum(1 for word in query_words if word in content_lower)
        
        # Calculate basic TF score
        score = matches / len(query_words)
        
        # Boost for exact phrase matches
        if query.lower() in content_lower:
            score += 0.3
        
        return min(1.0, score)
    
    def _combine_search_results(
        self,
        semantic_results: List[SearchResult],
        keyword_results: List[SearchResult],
        semantic_weight: float = 0.7,
        keyword_weight: float = 0.3,
    ) -> List[SearchResult]:
        """Combine and re-rank search results from different methods."""
        # Create a map of chunk_id to results
        result_map = {}
        
        # Add semantic results
        for result in semantic_results:
            result_map[result.chunk_id] = result
            result.score *= semantic_weight
        
        # Add or update with keyword results
        for result in keyword_results:
            if result.chunk_id in result_map:
                # Combine scores
                existing = result_map[result.chunk_id]
                existing.score += result.score * keyword_weight
                # Merge highlights
                existing.highlights.extend(result.highlights)
                existing.highlights = list(set(existing.highlights))  # Remove duplicates
            else:
                result.score *= keyword_weight
                result_map[result.chunk_id] = result
        
        # Sort by combined score
        combined_results = list(result_map.values())
        combined_results.sort(key=lambda r: r.score, reverse=True)
        
        return combined_results
    
    async def get_search_suggestions(
        self,
        partial_query: str,
        limit: int = 5,
    ) -> List[str]:
        """Get search suggestions based on partial query."""
        similar_queries = await self.search_query_repository.get_similar_queries(
            query_text=partial_query,
            similarity_threshold=0.6,
            limit=limit,
        )
        
        return [q.query_text for q in similar_queries]
    
    async def get_popular_queries(
        self,
        time_period: Optional[datetime] = None,
        limit: int = 10,
    ) -> List[Dict[str, Any]]:
        """Get popular search queries."""
        return await self.search_query_repository.get_popular_queries(
            time_period=time_period,
            limit=limit,
        )
    
    async def get_search_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get search analytics."""
        return await self.search_query_repository.get_query_analytics(
            start_date=start_date,
            end_date=end_date,
            user_id=user_id,
        )
