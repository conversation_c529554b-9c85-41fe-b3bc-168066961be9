{"version": 3, "file": "WorkPools-Cq5V5DP4.js", "sources": ["../../src/pages/WorkPools.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"work-pools\">\n    <template #header>\n      <PageHeadingWorkPools />\n    </template>\n\n    <template v-if=\"loaded\">\n      <template v-if=\"empty\">\n        <WorkPoolsPageEmptyState />\n      </template>\n\n      <template v-else>\n        <WorkPools @update=\"workPoolsSubscription.refresh\" />\n      </template>\n    </template>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { useWorkspaceApi, PageHeadingWorkPools, WorkPoolsPageEmptyState, WorkPools } from '@prefecthq/prefect-ui-library'\n  import { useSubscription } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  const api = useWorkspaceApi()\n  const subscriptionOptions = {\n    interval: 30000,\n  }\n\n  const workPoolsSubscription = useSubscription(api.workPools.getWorkPools, [{}], subscriptionOptions)\n  const workPools = computed(() => workPoolsSubscription.response ?? [])\n  const empty = computed(() => workPoolsSubscription.executed && workPools.value.length == 0)\n  const loaded = computed(() => workPoolsSubscription.executed)\n\n\n  usePageTitle('Work Pools')\n</script>"], "names": ["api", "useWorkspaceApi", "subscriptionOptions", "workPoolsSubscription", "useSubscription", "workPools", "computed", "empty", "loaded", "usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingWorkPools", "_createElementBlock", "_Fragment", "WorkPoolsPageEmptyState", "WorkPools"], "mappings": "qOAwBE,MAAMA,EAAMC,EAAgB,EACtBC,EAAsB,CAC1B,SAAU,GACZ,EAEMC,EAAwBC,EAAgBJ,EAAI,UAAU,aAAc,CAAC,CAAA,CAAE,EAAGE,CAAmB,EAC7FG,EAAYC,EAAS,IAAMH,EAAsB,UAAY,CAAA,CAAE,EAC/DI,EAAQD,EAAS,IAAMH,EAAsB,UAAYE,EAAU,MAAM,QAAU,CAAC,EACpFG,EAASF,EAAS,IAAMH,EAAsB,QAAQ,EAG5D,OAAAM,EAAa,YAAY,iDAlCzB,EAAAC,EAcmBC,EAAA,CAdD,MAAM,cAAY,CACvB,SACT,IAAwB,CAAxBC,EAAwBC,EAAAC,CAAA,CAAA,CAAA,aAG1B,IAQW,CARKN,EAAM,WAAtBO,EAQWC,EAAA,CAAA,IAAA,GAAA,CAPOT,EAAK,WACnBG,EAA2BG,EAAAI,CAAA,EAAA,CAAA,IAAA,CAAA,CAAA,QAI3BP,EAAqDG,EAAAK,CAAA,EAAA,OAAzC,SAAQL,EAAqBV,CAAA,EAAC"}