"""Embedding service for the MCP RAG Server."""

import asyncio
from typing import List, Optional, Dict, Any, <PERSON>ple
import openai
from tenacity import retry, stop_after_attempt, wait_exponential

from ...domain.entities import Chunk
from ...domain.repositories import ChunkRepository


class EmbeddingService:
    """Service for generating and managing embeddings."""
    
    def __init__(
        self,
        chunk_repository: ChunkRepository,
        openai_api_key: str,
        model: str = "text-embedding-3-small",
        batch_size: int = 100,
        max_concurrent: int = 5,
    ):
        self.chunk_repository = chunk_repository
        self.openai_client = openai.AsyncOpenAI(api_key=openai_api_key)
        self.model = model
        self.batch_size = batch_size
        self.max_concurrent = max_concurrent
        self._semaphore = asyncio.Semaphore(max_concurrent)
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def generate_embedding(self, text: str) -> List[float]:
        """Generate embedding for a single text."""
        async with self._semaphore:
            try:
                response = await self.openai_client.embeddings.create(
                    model=self.model,
                    input=text,
                    encoding_format="float"
                )
                return response.data[0].embedding
            except Exception as e:
                raise Exception(f"Failed to generate embedding: {str(e)}")
    
    async def generate_embeddings_batch(self, texts: List[str]) -> List[List[float]]:
        """Generate embeddings for multiple texts."""
        if not texts:
            return []
        
        # Split into batches to respect API limits
        embeddings = []
        for i in range(0, len(texts), self.batch_size):
            batch = texts[i:i + self.batch_size]
            batch_embeddings = await self._process_batch(batch)
            embeddings.extend(batch_embeddings)
        
        return embeddings
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10)
    )
    async def _process_batch(self, texts: List[str]) -> List[List[float]]:
        """Process a batch of texts."""
        async with self._semaphore:
            try:
                response = await self.openai_client.embeddings.create(
                    model=self.model,
                    input=texts,
                    encoding_format="float"
                )
                return [item.embedding for item in response.data]
            except Exception as e:
                raise Exception(f"Failed to process batch: {str(e)}")
    
    async def embed_chunk(self, chunk: Chunk) -> Chunk:
        """Generate and store embedding for a chunk."""
        if not chunk.content.strip():
            return chunk
        
        embedding = await self.generate_embedding(chunk.content)
        chunk.update_embedding(embedding, self.model)
        
        return await self.chunk_repository.update(chunk)
    
    async def embed_chunks_batch(self, chunks: List[Chunk]) -> List[Chunk]:
        """Generate and store embeddings for multiple chunks."""
        if not chunks:
            return []
        
        # Filter chunks that need embeddings
        chunks_to_embed = [
            chunk for chunk in chunks
            if chunk.content.strip() and (
                not chunk.embedding or 
                chunk.embedding_model != self.model
            )
        ]
        
        if not chunks_to_embed:
            return chunks
        
        # Extract texts
        texts = [chunk.content for chunk in chunks_to_embed]
        
        # Generate embeddings
        embeddings = await self.generate_embeddings_batch(texts)
        
        # Update chunks with embeddings
        chunk_embeddings = []
        for chunk, embedding in zip(chunks_to_embed, embeddings):
            chunk.update_embedding(embedding, self.model)
            chunk_embeddings.append((chunk.id, embedding, self.model))
        
        # Bulk update in repository
        await self.chunk_repository.bulk_update_embeddings(chunk_embeddings)
        
        return chunks
    
    async def process_pending_chunks(self, limit: int = 100) -> int:
        """Process chunks that don't have embeddings."""
        chunks = await self.chunk_repository.get_chunks_without_embeddings(
            limit=limit,
            embedding_model=self.model
        )
        
        if not chunks:
            return 0
        
        await self.embed_chunks_batch(chunks)
        return len(chunks)
    
    async def reprocess_chunks_with_new_model(
        self,
        new_model: str,
        limit: int = 100,
    ) -> int:
        """Reprocess existing chunks with a new embedding model."""
        old_model = self.model
        self.model = new_model
        
        try:
            # Get chunks with old model
            chunks = await self.chunk_repository.get_chunks_by_embedding_model(old_model)
            
            if not chunks:
                return 0
            
            # Process in batches
            processed = 0
            for i in range(0, len(chunks), limit):
                batch = chunks[i:i + limit]
                await self.embed_chunks_batch(batch)
                processed += len(batch)
            
            return processed
        finally:
            self.model = old_model
    
    async def search_similar_chunks(
        self,
        query_text: str,
        limit: int = 10,
        similarity_threshold: float = 0.7,
        document_ids: Optional[List[str]] = None,
    ) -> List[Tuple[Chunk, float]]:
        """Search for similar chunks using embedding similarity."""
        # Generate embedding for query
        query_embedding = await self.generate_embedding(query_text)
        
        # Search in repository
        return await self.chunk_repository.search_by_embedding(
            embedding=query_embedding,
            limit=limit,
            similarity_threshold=similarity_threshold,
            document_ids=document_ids,
        )
    
    async def find_similar_content(
        self,
        chunk_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.8,
    ) -> List[Tuple[Chunk, float]]:
        """Find chunks with similar content to a given chunk."""
        return await self.chunk_repository.get_similar_chunks(
            chunk_id=chunk_id,
            limit=limit,
            similarity_threshold=similarity_threshold,
        )
    
    async def calculate_chunk_similarity(
        self,
        chunk1_id: str,
        chunk2_id: str,
    ) -> Optional[float]:
        """Calculate similarity between two chunks."""
        chunk1 = await self.chunk_repository.get_by_id(chunk1_id)
        chunk2 = await self.chunk_repository.get_by_id(chunk2_id)
        
        if not chunk1 or not chunk2 or not chunk1.embedding or not chunk2.embedding:
            return None
        
        return chunk1.calculate_similarity(chunk2.embedding)
    
    async def get_embedding_statistics(self) -> Dict[str, Any]:
        """Get embedding statistics."""
        stats = await self.chunk_repository.get_statistics()
        
        total_chunks = stats.get("total_chunks", 0)
        chunks_with_embeddings = stats.get("chunks_with_embeddings", 0)
        
        return {
            "total_chunks": total_chunks,
            "chunks_with_embeddings": chunks_with_embeddings,
            "chunks_without_embeddings": total_chunks - chunks_with_embeddings,
            "embedding_coverage": (
                chunks_with_embeddings / total_chunks * 100 if total_chunks > 0 else 0
            ),
            "current_model": self.model,
            "batch_size": self.batch_size,
            "max_concurrent": self.max_concurrent,
        }
    
    async def validate_embeddings(self, sample_size: int = 100) -> Dict[str, Any]:
        """Validate embedding quality by sampling."""
        # Get random chunks with embeddings
        chunks = await self.chunk_repository.get_random_chunks(sample_size)
        chunks_with_embeddings = [c for c in chunks if c.embedding]
        
        if not chunks_with_embeddings:
            return {"error": "No chunks with embeddings found"}
        
        # Calculate statistics
        embedding_dimensions = len(chunks_with_embeddings[0].embedding)
        
        # Check for consistency
        dimension_consistency = all(
            len(chunk.embedding) == embedding_dimensions
            for chunk in chunks_with_embeddings
        )
        
        # Check for zero vectors (potential issues)
        zero_vectors = sum(
            1 for chunk in chunks_with_embeddings
            if all(abs(x) < 1e-10 for x in chunk.embedding)
        )
        
        return {
            "sample_size": len(chunks_with_embeddings),
            "embedding_dimensions": embedding_dimensions,
            "dimension_consistency": dimension_consistency,
            "zero_vectors": zero_vectors,
            "zero_vector_percentage": zero_vectors / len(chunks_with_embeddings) * 100,
            "models_used": list(set(
                chunk.embedding_model for chunk in chunks_with_embeddings
                if chunk.embedding_model
            )),
        }
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about the current embedding model."""
        return {
            "model": self.model,
            "batch_size": self.batch_size,
            "max_concurrent": self.max_concurrent,
            "provider": "OpenAI",
        }
