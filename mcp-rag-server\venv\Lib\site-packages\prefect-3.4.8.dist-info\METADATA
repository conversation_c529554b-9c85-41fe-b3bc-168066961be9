Metadata-Version: 2.4
Name: prefect
Version: 3.4.8
Summary: Workflow orchestration and management.
Project-URL: Changelog, https://github.com/PrefectHQ/prefect/releases
Project-URL: Documentation, https://docs.prefect.io
Project-URL: Source, https://github.com/PrefectHQ/prefect
Project-URL: Tracker, https://github.com/PrefectHQ/prefect/issues
Author-email: "Prefect Technologies, Inc." <<EMAIL>>
License: Apache-2.0
License-File: LICENSE
Classifier: Intended Audience :: Developers
Classifier: Intended Audience :: System Administrators
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Natural Language :: English
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Software Development :: Libraries
Requires-Python: <3.14,>=3.9
Requires-Dist: aiosqlite<1.0.0,>=0.17.0
Requires-Dist: alembic<2.0.0,>=1.7.5
Requires-Dist: anyio<5.0.0,>=4.4.0
Requires-Dist: apprise<2.0.0,>=1.1.0
Requires-Dist: asgi-lifespan<3.0,>=1.0
Requires-Dist: asyncpg<1.0.0,>=0.23
Requires-Dist: cachetools<7.0,>=5.3
Requires-Dist: click<8.2,>=8.0
Requires-Dist: cloudpickle<4.0,>=2.0
Requires-Dist: coolname<3.0.0,>=1.0.4
Requires-Dist: cryptography>=36.0.1
Requires-Dist: dateparser<2.0.0,>=1.1.1
Requires-Dist: docker<8.0,>=4.0
Requires-Dist: exceptiongroup>=1.0.0
Requires-Dist: fastapi<1.0.0,>=0.111.0
Requires-Dist: fsspec>=2022.5.0
Requires-Dist: graphviz>=0.20.1
Requires-Dist: griffe<2.0.0,>=0.49.0
Requires-Dist: httpcore<2.0.0,>=1.0.5
Requires-Dist: httpx[http2]!=0.23.2,>=0.23
Requires-Dist: humanize<5.0.0,>=4.9.0
Requires-Dist: importlib-metadata>=4.4; python_version < '3.10'
Requires-Dist: jinja2-humanize-extension>=0.4.0
Requires-Dist: jinja2<4.0.0,>=3.1.6
Requires-Dist: jsonpatch<2.0,>=1.32
Requires-Dist: jsonschema<5.0.0,>=4.18.0
Requires-Dist: opentelemetry-api<2.0.0,>=1.27.0
Requires-Dist: orjson<4.0,>=3.7
Requires-Dist: packaging<25.1,>=21.3
Requires-Dist: pathspec>=0.8.0
Requires-Dist: pendulum<4,>=3.0.0; python_version < '3.13'
Requires-Dist: prometheus-client>=0.20.0
Requires-Dist: pydantic!=2.10.0,!=2.11.0,!=2.11.1,!=2.11.2,!=2.11.3,!=2.11.4,<3.0.0,>=2.9
Requires-Dist: pydantic-core<3.0.0,>=2.12.0
Requires-Dist: pydantic-extra-types<3.0.0,>=2.8.2
Requires-Dist: pydantic-settings!=2.9.0,<3.0.0,>2.2.1
Requires-Dist: python-dateutil<3.0.0,>=2.8.2
Requires-Dist: python-slugify<9.0,>=5.0
Requires-Dist: python-socks[asyncio]<3.0,>=2.5.3
Requires-Dist: pytz<2026,>=2021.1
Requires-Dist: pyyaml<7.0.0,>=5.4.1
Requires-Dist: readchar<5.0.0,>=4.0.0
Requires-Dist: rfc3339-validator<0.2.0,>=0.1.4
Requires-Dist: rich<15.0,>=11.0
Requires-Dist: ruamel-yaml>=0.17.0
Requires-Dist: semver>=3.0.4
Requires-Dist: sniffio<2.0.0,>=1.3.0
Requires-Dist: sqlalchemy[asyncio]<3.0.0,>=2.0
Requires-Dist: toml>=0.10.0
Requires-Dist: typer!=0.12.2,<0.17.0,>=0.12.0
Requires-Dist: typing-extensions<5.0.0,>=4.10.0
Requires-Dist: uv>=0.6.0
Requires-Dist: uvicorn!=0.29.0,>=0.14.0
Requires-Dist: websockets<16.0,>=13.0
Requires-Dist: whenever<0.9.0,>=0.7.3; python_version >= '3.13'
Provides-Extra: aws
Requires-Dist: prefect-aws>=0.5.8; extra == 'aws'
Provides-Extra: azure
Requires-Dist: prefect-azure>=0.4.0; extra == 'azure'
Provides-Extra: bitbucket
Requires-Dist: prefect-bitbucket>=0.3.0; extra == 'bitbucket'
Provides-Extra: dask
Requires-Dist: prefect-dask>=0.3.0; extra == 'dask'
Provides-Extra: databricks
Requires-Dist: prefect-databricks>=0.3.0; extra == 'databricks'
Provides-Extra: dbt
Requires-Dist: prefect-dbt>=0.6.0; extra == 'dbt'
Provides-Extra: docker
Requires-Dist: prefect-docker>=0.6.0; extra == 'docker'
Provides-Extra: email
Requires-Dist: prefect-email>=0.4.0; extra == 'email'
Provides-Extra: gcp
Requires-Dist: prefect-gcp>=0.6.0; extra == 'gcp'
Provides-Extra: github
Requires-Dist: prefect-github>=0.3.0; extra == 'github'
Provides-Extra: gitlab
Requires-Dist: prefect-gitlab>=0.3.0; extra == 'gitlab'
Provides-Extra: kubernetes
Requires-Dist: prefect-kubernetes>=0.4.0; extra == 'kubernetes'
Provides-Extra: otel
Requires-Dist: opentelemetry-distro<1.0.0,>=0.48b0; extra == 'otel'
Requires-Dist: opentelemetry-exporter-otlp<2.0.0,>=1.27.0; extra == 'otel'
Requires-Dist: opentelemetry-instrumentation-logging<1.0.0,>=0.48b0; extra == 'otel'
Requires-Dist: opentelemetry-instrumentation<1.0.0,>=0.48b0; extra == 'otel'
Requires-Dist: opentelemetry-test-utils<1.0.0,>=0.48b0; extra == 'otel'
Provides-Extra: ray
Requires-Dist: prefect-ray>=0.4.0; extra == 'ray'
Provides-Extra: redis
Requires-Dist: prefect-redis>=0.2.0; extra == 'redis'
Provides-Extra: shell
Requires-Dist: prefect-shell>=0.3.0; extra == 'shell'
Provides-Extra: slack
Requires-Dist: prefect-slack>=0.3.0; extra == 'slack'
Provides-Extra: snowflake
Requires-Dist: prefect-snowflake>=0.28.0; extra == 'snowflake'
Provides-Extra: sqlalchemy
Requires-Dist: prefect-sqlalchemy>=0.5.0; extra == 'sqlalchemy'
Description-Content-Type: text/markdown

<p align="center"><img src="https://github.com/PrefectHQ/prefect/assets/3407835/c654cbc6-63e8-4ada-a92a-efd2f8f24b85" width=1000></p>

<p align="center">
    <a href="https://pypi.org/project/prefect/" alt="PyPI version">
        <img alt="PyPI" src="https://img.shields.io/pypi/v/prefect?color=0052FF&labelColor=090422" />
    </a>
    <a href="https://pypi.org/project/prefect/" alt="PyPI downloads/month">
        <img alt="Downloads" src="https://img.shields.io/pypi/dm/prefect?color=0052FF&labelColor=090422" />
    </a>
    <a href="https://github.com/prefecthq/prefect/" alt="Stars">
        <img src="https://img.shields.io/github/stars/prefecthq/prefect?color=0052FF&labelColor=090422" />
    </a>
    <a href="https://github.com/prefecthq/prefect/pulse" alt="Activity">
        <img src="https://img.shields.io/github/commit-activity/m/prefecthq/prefect?color=0052FF&labelColor=090422" />
    </a>
    <br>
    <a href="https://prefect.io/slack" alt="Slack">
        <img src="https://img.shields.io/badge/slack-join_community-red.svg?color=0052FF&labelColor=090422&logo=slack" />
    </a>
    <a href="https://www.youtube.com/c/PrefectIO/" alt="YouTube">
        <img src="https://img.shields.io/badge/youtube-watch_videos-red.svg?color=0052FF&labelColor=090422&logo=youtube" />
    </a>
</p>


<p align="center">
    <a href="https://docs.prefect.io/v3/get-started/index?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none">
        Installation
    </a>
    ·
    <a href="https://docs.prefect.io/v3/get-started/quickstart?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none">
        Quickstart
    </a>
    ·
    <a href="https://docs.prefect.io/v3/how-to-guides/workflows/write-and-run?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none">
        Build workflows
    </a>
    ·
    <a href="https://docs.prefect.io/v3/concepts/deployments?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none">
        Deploy workflows
    </a>
    ·
    <a href="https://app.prefect.cloud/?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none">
        Prefect Cloud
    </a>
</p>

# Prefect

Prefect is a workflow orchestration framework for building data pipelines in Python.
It's the simplest way to elevate a script into a production workflow.
With Prefect, you can build resilient, dynamic data pipelines that react to the world around them and recover from unexpected changes.

With just a few lines of code, data teams can confidently automate any data process with features such as scheduling, caching, retries, and event-based automations.

Workflow activity is tracked and can be monitored with a self-hosted [Prefect server](https://docs.prefect.io/latest/manage/self-host/?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none) instance or managed [Prefect Cloud](https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none) dashboard.

> [!TIP]
> Prefect flows can handle retries, dependencies, and even complex branching logic
> 
> [Check our docs](https://docs.prefect.io/v3/get-started/index?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none) or see the example below to learn more!

## Getting started

Prefect requires Python 3.9+. To [install the latest version of Prefect](https://docs.prefect.io/v3/get-started/install), run one of the following commands:

```bash
pip install -U prefect
```

```bash
uv add prefect
```

Then create and run a Python file that uses Prefect `flow` and `task` decorators to orchestrate and observe your workflow - in this case, a simple script that fetches the number of GitHub stars from a repository:

```python
from prefect import flow, task
import httpx


@task(log_prints=True)
def get_stars(repo: str):
    url = f"https://api.github.com/repos/{repo}"
    count = httpx.get(url).json()["stargazers_count"]
    print(f"{repo} has {count} stars!")


@flow(name="GitHub Stars")
def github_stars(repos: list[str]):
    for repo in repos:
        get_stars(repo)


# run the flow!
if __name__ == "__main__":
    github_stars(["PrefectHQ/Prefect"])
```

Fire up a Prefect server and open the UI at http://localhost:4200 to see what happened:

```bash
prefect server start
```

To run your workflow on a schedule, turn it into a deployment and schedule it to run every minute by changing the last line of your script to the following:

```python
if __name__ == "__main__":
    github_stars.serve(
        name="first-deployment",
        cron="* * * * *",
        parameters={"repos": ["PrefectHQ/prefect"]}
    )
```

You now have a process running locally that is looking for scheduled deployments!
Additionally you can run your workflow manually from the UI or CLI. You can even run deployments in response to [events](https://docs.prefect.io/latest/automate/?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none).

> [!TIP]
> Where to go next - check out our [documentation](https://docs.prefect.io/v3/get-started/index?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none) to learn more about:
> - [Deploying flows to production environments](https://docs.prefect.io/v3/deploy?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none)
> - [Adding error handling and retries](https://docs.prefect.io/v3/develop/write-tasks#retries?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none)
> - [Integrating with your existing tools](https://docs.prefect.io/integrations/integrations?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none)
> - [Setting up team collaboration features](https://docs.prefect.io/v3/manage/cloud/manage-users/manage-teams#manage-teams?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none)


## Prefect Cloud

Prefect Cloud provides workflow orchestration for the modern data enterprise. By automating over 200 million data tasks monthly, Prefect empowers diverse organizations — from Fortune 50 leaders such as Progressive Insurance to innovative disruptors such as Cash App — to increase engineering productivity, reduce pipeline errors, and cut data workflow compute costs.

Read more about Prefect Cloud [here](https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none) or sign up to [try it for yourself](https://app.prefect.cloud?utm_source=oss&utm_medium=oss&utm_campaign=oss_gh_repo&utm_term=none&utm_content=none).

## prefect-client

If your use case is geared towards communicating with Prefect Cloud or a remote Prefect server, check out our
[prefect-client](https://pypi.org/project/prefect-client/). It is a lighter-weight option for accessing client-side functionality in the Prefect SDK and is ideal for use in ephemeral execution environments.

## Connect & Contribute
Join a thriving community of over 25,000 practitioners who solve data challenges with Prefect. Prefect's community is built on collaboration, technical innovation, and continuous improvement.

### Community Resources
🌐 **[Explore the Documentation](https://docs.prefect.io)** - Comprehensive guides and API references  
💬 **[Join the Slack Community](https://prefect.io/slack)** - Connect with thousands of practitioners  
🤝 **[Contribute to Prefect](https://docs.prefect.io/contribute/)** - Help shape the future of the project  
 🔌 **[Support or create a new Prefect integration](https://docs.prefect.io/contribute/contribute-integrations)** - Extend Prefect's capabilities

### Stay Informed
📥 **[Subscribe to our Newsletter](https://prefect.io/newsletter)** - Get the latest Prefect news and updates  
📣 **[Twitter/X](https://x.com/PrefectIO)** - Latest updates and announcements  
📺 **[YouTube](https://www.youtube.com/@PrefectIO)** - Video tutorials and webinars  
📱 **[LinkedIn](https://www.linkedin.com/company/prefect)** - Professional networking and company news  

Your contributions, questions, and ideas make Prefect better every day. Whether you're reporting bugs, suggesting features, or improving documentation, your input is invaluable to the Prefect community.

