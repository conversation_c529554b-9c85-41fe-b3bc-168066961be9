import{d as v,aR as y,aS as V,e as C,f as F,g as i,aT as S,i as r,c as _,o as n,j as l,q as m,a as x,k as a,F as R,n as e,aU as B,l as d,aV as U,aW as D,b as N,aX as T,aY as j,B as W,z as q,G as A,aZ as E,ay as O}from"./index-ei-kaitd.js";import{s as z}from"./index-B4HswuBc.js";const G={class:"workspace-dashboard__header-actions"},H={class:"workspace-dashboard__subflows-toggle"},I={key:1,class:"workspace-dashboard__grid"},J={class:"workspace-dashboard__side"},Y=v({__name:"Dashboard",setup(K){y(V,{interval:z(30)});const f=C(),c=F(f.flowRuns.getFlowRunsCount,[{}]),u=i(()=>c.executed),p=i(()=>c.response===0),b=[{text:"Dashboard"}],s=S({range:{type:"span",seconds:-86400},tags:[]}),g=()=>N.map("WorkspaceDashboardFilter",s,"TaskRunsFilter");return(M,o)=>{const k=r("p-toggle"),w=r("p-button"),h=r("p-layout-default");return n(),_(h,{class:"workspace-dashboard"},{header:l(()=>[a(e(q),{crumbs:b,class:"workspace-dashboard__page-heading"},A({_:2},[u.value&&!p.value?{name:"actions",fn:l(()=>[d("div",G,[d("div",H,[a(k,{modelValue:e(s).hideSubflows,"onUpdate:modelValue":o[0]||(o[0]=t=>e(s).hideSubflows=t),append:"Hide subflows"},null,8,["modelValue"])]),a(e(E),{selected:e(s).tags,"onUpdate:selected":o[1]||(o[1]=t=>e(s).tags=t),"empty-message":"All tags"},null,8,["selected"]),a(e(O),{modelValue:e(s).range,"onUpdate:modelValue":o[2]||(o[2]=t=>e(s).range=t),class:"workspace-dashboard__date-select"},null,8,["modelValue"])])]),key:"0"}:void 0]),1024)]),default:l(()=>[u.value?(n(),m(R,{key:0},[p.value?(n(),_(e(B),{key:0})):(n(),m("div",I,[a(e(U),{filter:e(s)},null,8,["filter"]),d("div",J,[a(e(D),{filter:g}),a(e(T),{class:"workspace-dashboard__work-pools",filter:e(s)},null,8,["filter"])])]))],64)):x("",!0),a(e(j),{title:"Ready to scale?",subtitle:"Webhooks, role and object-level security, and serverless push work pools on Prefect Cloud"},{actions:l(()=>[a(w,{to:"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none",target:"_blank",primary:""},{default:l(()=>o[3]||(o[3]=[W(" Upgrade to Cloud ")])),_:1,__:[3]})]),_:1})]),_:1})}}});export{Y as default};
//# sourceMappingURL=Dashboard-ZEHiiZMn.js.map
