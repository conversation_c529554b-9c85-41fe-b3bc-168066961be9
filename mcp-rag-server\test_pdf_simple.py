#!/usr/bin/env python3
"""Simple test for PDF crawler."""

import asyncio
from crawlers.pdf.simple_pdf_crawler import SimplePDFCrawler
from core.application.services import DocumentService
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from config.settings import get_settings


async def test_simple_pdf():
    print('📄 Testing Simple PDF Crawler')
    
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    crawler = SimplePDFCrawler(doc_service)
    
    # Test with a small PDF
    test_url = 'https://www.w3.org/WAI/ER/tests/xhtml/testfiles/resources/pdf/dummy.pdf'
    
    result = await crawler.download_and_process_pdf(test_url)
    
    print(f'✅ Processed: {result.url}')
    print(f'   Content length: {len(result.content)} chars')
    print(f'   Chunks: {len(result.chunks)}')
    print(f'   File size: {result.file_size} bytes')
    print(f'   Page count: {result.page_count}')
    print(f'   Processing time: {result.processing_time:.2f}s')
    
    if result.error:
        print(f'   Error: {result.error}')
    else:
        print(f'   Content preview: {result.content[:200]}...')
        if result.chunks:
            print(f'   First chunk: {result.chunks[0][:100]}...')
    
    # Show statistics
    stats = crawler.get_statistics()
    print('📊 Statistics:')
    for key, value in stats.items():
        print(f'   {key}: {value}')


if __name__ == "__main__":
    asyncio.run(test_simple_pdf())
