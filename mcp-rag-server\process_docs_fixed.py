#!/usr/bin/env python3
"""Process existing documents - FIXED VERSION."""

import asyncio
import time
from config.settings import get_settings


async def process_eufunds_documents():
    """Process eufunds documents into chunks."""
    print('🧩 PROCESSING EUFUNDS DOCUMENTS')
    print('=' * 50)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Get eufunds documents
        print('📄 Getting eufunds documents...')
        docs_result = client.table('documents').select('*').ilike('url', '%eufunds%').execute()
        documents = docs_result.data
        
        print(f'🇪🇺 Found {len(documents)} eufunds documents')
        
        if not documents:
            print('❌ No eufunds documents found!')
            return
        
        # Show documents
        for i, doc in enumerate(documents, 1):
            url = doc.get('url', 'No URL')
            content_length = len(doc.get('content', '')) if doc.get('content') else 0
            print(f'   {i}. {url} ({content_length} chars)')
        
        # Process each document into chunks
        print(f'\n🔄 Creating chunks...')
        
        total_chunks = 0
        processed_docs = 0
        
        for doc in documents:
            doc_id = doc['id']
            content = doc.get('content', '')
            url = doc.get('url', '')
            
            if not content or not content.strip():
                print(f'   ⚠️  Skipping {url} - no content')
                continue
            
            print(f'   📄 Processing: {url}')
            
            # Simple chunking - split by sentences/paragraphs
            chunks = []
            chunk_size = 800
            overlap = 200
            
            # Clean content
            clean_content = ' '.join(content.split())
            
            # Create chunks
            start = 0
            chunk_index = 0
            
            while start < len(clean_content):
                end = start + chunk_size
                
                # Find good break point
                if end < len(clean_content):
                    # Look for sentence end
                    for i in range(end, max(start + chunk_size//2, end - 100), -1):
                        if clean_content[i] in '.!?':
                            end = i + 1
                            break
                
                chunk_content = clean_content[start:end].strip()
                
                if chunk_content:
                    chunk_data = {
                        'document_id': doc_id,
                        'content': chunk_content,
                        'content_length': len(chunk_content),
                        'chunk_index': chunk_index,
                        'start_position': start,
                        'end_position': end,
                        'embedding_model': 'text-embedding-3-small',
                        'metadata': {'source_url': url}
                    }
                    chunks.append(chunk_data)
                    chunk_index += 1
                
                # Move start position with overlap
                start = max(start + chunk_size - overlap, end)
                
                if start >= len(clean_content):
                    break
            
            # Insert chunks into database
            if chunks:
                try:
                    result = client.table('chunks').insert(chunks).execute()
                    doc_chunks = len(result.data) if result.data else len(chunks)
                    total_chunks += doc_chunks
                    processed_docs += 1
                    print(f'     ✅ Created {doc_chunks} chunks')
                except Exception as e:
                    print(f'     ❌ Error inserting chunks: {e}')
            else:
                print(f'     ⚠️  No chunks created')
        
        print(f'\n📊 CHUNKING RESULTS')
        print('=' * 20)
        print(f'✅ Documents processed: {processed_docs}/{len(documents)}')
        print(f'🧩 Total chunks created: {total_chunks}')
        
        if processed_docs > 0:
            print(f'📈 Average chunks per document: {total_chunks / processed_docs:.1f}')
        
        return total_chunks
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return 0


async def generate_embeddings():
    """Generate embeddings for chunks."""
    print(f'\n🤖 GENERATING EMBEDDINGS')
    print('=' * 30)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        import openai
        
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        openai.api_key = settings.openai_api_key
        
        # Get chunks without embeddings
        print('📦 Getting chunks without embeddings...')
        chunks_result = client.table('chunks').select('id, content').is_('embedding', 'null').limit(20).execute()
        chunks = chunks_result.data
        
        print(f'🧩 Found {len(chunks)} chunks to process')
        
        if not chunks:
            print('✅ All chunks already have embeddings!')
            return 0
        
        # Generate embeddings
        embeddings_created = 0
        
        for i, chunk in enumerate(chunks, 1):
            chunk_id = chunk['id']
            content = chunk['content']
            
            print(f'   🤖 Processing chunk {i}/{len(chunks)}...')
            
            try:
                # Generate embedding using OpenAI
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=content
                )
                
                embedding = response.data[0].embedding
                
                # Convert to JSON string for storage
                import json
                embedding_json = json.dumps(embedding)
                
                # Update chunk with embedding
                update_result = client.table('chunks').update({
                    'embedding': embedding_json,
                    'embedding_model': 'text-embedding-3-small'
                }).eq('id', chunk_id).execute()
                
                if update_result.data:
                    embeddings_created += 1
                    print(f'     ✅ Embedding created ({len(embedding)} dims)')
                else:
                    print(f'     ❌ Failed to update chunk')
                
                # Small delay to avoid rate limits
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f'     ❌ Error generating embedding: {e}')
        
        print(f'\n📊 EMBEDDING RESULTS')
        print('=' * 20)
        print(f'✅ Embeddings created: {embeddings_created}/{len(chunks)}')
        
        return embeddings_created
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return 0


async def test_rag_search():
    """Test RAG search."""
    print(f'\n🔍 TESTING RAG SEARCH')
    print('=' * 30)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        import openai
        import json
        import numpy as np
        
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        openai.api_key = settings.openai_api_key
        
        # Test questions
        test_questions = [
            "Какви са условията за кандидатстване за европейски фондове?",
            "Кои са основните програми за финансиране в България?",
            "Как мога да се свържа с отговорните лица?",
        ]
        
        successful_searches = 0
        
        for i, question in enumerate(test_questions, 1):
            print(f'\n❓ Question {i}: {question}')
            
            try:
                # Generate embedding for question
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=question
                )
                question_embedding = response.data[0].embedding
                
                # Get all chunks with embeddings
                chunks_result = client.table('chunks').select('id, content, embedding').not_.is_('embedding', 'null').execute()
                chunks = chunks_result.data
                
                if not chunks:
                    print('   ❌ No chunks with embeddings found!')
                    continue
                
                # Calculate similarities
                similarities = []
                
                for chunk in chunks:
                    try:
                        chunk_embedding = json.loads(chunk['embedding'])
                        
                        # Calculate cosine similarity
                        dot_product = np.dot(question_embedding, chunk_embedding)
                        norm_a = np.linalg.norm(question_embedding)
                        norm_b = np.linalg.norm(chunk_embedding)
                        similarity = dot_product / (norm_a * norm_b)
                        
                        similarities.append((chunk, similarity))
                        
                    except Exception as e:
                        print(f'     ⚠️  Error calculating similarity: {e}')
                
                # Sort by similarity
                similarities.sort(key=lambda x: x[1], reverse=True)
                
                # Show top results
                top_results = similarities[:3]
                
                if top_results and top_results[0][1] > 0.7:
                    successful_searches += 1
                    print(f'   ✅ Found {len(top_results)} relevant chunks')
                    
                    for j, (chunk, similarity) in enumerate(top_results, 1):
                        content = chunk['content']
                        preview = content[:100] + "..." if len(content) > 100 else content
                        print(f'     {j}. Similarity: {similarity:.3f} - "{preview}"')
                else:
                    print(f'   ❌ No relevant chunks found (best similarity: {top_results[0][1]:.3f} if top_results else "none"})')
                
            except Exception as e:
                print(f'   ❌ Search error: {e}')
        
        # Results
        success_rate = (successful_searches / len(test_questions)) * 100
        print(f'\n📊 SEARCH RESULTS')
        print('=' * 20)
        print(f'Successful searches: {successful_searches}/{len(test_questions)}')
        print(f'Success rate: {success_rate:.1f}%')
        
        if success_rate >= 80:
            print('🟢 EXCELLENT - RAG search working great!')
        elif success_rate >= 60:
            print('🟡 GOOD - RAG search working well!')
        else:
            print('🔴 NEEDS IMPROVEMENT - RAG search not optimal!')
        
        return success_rate
        
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()
        return 0


async def main():
    """Main function."""
    print('🚀 RAG SYSTEM COMPLETE ACTIVATION')
    print('=' * 60)
    
    start_time = time.time()
    
    try:
        # Step 1: Create chunks
        total_chunks = await process_eufunds_documents()
        
        if total_chunks > 0:
            # Step 2: Generate embeddings
            embeddings_created = await generate_embeddings()
            
            if embeddings_created > 0:
                # Step 3: Test RAG search
                search_success_rate = await test_rag_search()
                
                # Final assessment
                total_time = time.time() - start_time
                
                print(f'\n🎯 FINAL RESULTS')
                print('=' * 20)
                print(f'⏱️  Total time: {total_time:.2f}s')
                print(f'🧩 Chunks created: {total_chunks}')
                print(f'🤖 Embeddings generated: {embeddings_created}')
                print(f'🔍 Search success rate: {search_success_rate:.1f}%')
                
                if search_success_rate >= 60:
                    print('\n🟢 SUCCESS! RAG SYSTEM IS FULLY OPERATIONAL!')
                    print('🚀 Ready for production use!')
                else:
                    print('\n🟡 PARTIAL SUCCESS - System working but needs optimization')
            else:
                print('\n❌ FAILED - Could not generate embeddings')
        else:
            print('\n❌ FAILED - Could not create chunks')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
