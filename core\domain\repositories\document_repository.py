"""Document repository interface for the MCP RAG Server."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..entities import Document, DocumentStatus, DocumentType


class DocumentRepository(ABC):
    """Abstract document repository interface."""
    
    @abstractmethod
    async def create(self, document: Document) -> Document:
        """Create a new document."""
        pass
    
    @abstractmethod
    async def get_by_id(self, document_id: str) -> Optional[Document]:
        """Get document by ID."""
        pass
    
    @abstractmethod
    async def get_by_url(self, url: str) -> Optional[Document]:
        """Get document by URL."""
        pass
    
    @abstractmethod
    async def get_by_content_hash(self, content_hash: str) -> Optional[Document]:
        """Get document by content hash."""
        pass
    
    @abstractmethod
    async def update(self, document: Document) -> Document:
        """Update an existing document."""
        pass
    
    @abstractmethod
    async def delete(self, document_id: str) -> bool:
        """Delete a document."""
        pass
    
    @abstractmethod
    async def list_documents(
        self,
        limit: int = 100,
        offset: int = 0,
        status: Optional[DocumentStatus] = None,
        document_type: Optional[DocumentType] = None,
        crawl_session_id: Optional[str] = None,
    ) -> List[Document]:
        """List documents with optional filters."""
        pass
    
    @abstractmethod
    async def count_documents(
        self,
        status: Optional[DocumentStatus] = None,
        document_type: Optional[DocumentType] = None,
        crawl_session_id: Optional[str] = None,
    ) -> int:
        """Count documents with optional filters."""
        pass
    
    @abstractmethod
    async def get_documents_by_status(self, status: DocumentStatus) -> List[Document]:
        """Get all documents with specific status."""
        pass
    
    @abstractmethod
    async def get_documents_by_crawl_session(self, crawl_session_id: str) -> List[Document]:
        """Get all documents from a specific crawl session."""
        pass
    
    @abstractmethod
    async def get_documents_by_domain(self, domain: str) -> List[Document]:
        """Get all documents from a specific domain."""
        pass
    
    @abstractmethod
    async def get_documents_created_after(self, date: datetime) -> List[Document]:
        """Get documents created after a specific date."""
        pass
    
    @abstractmethod
    async def get_documents_updated_after(self, date: datetime) -> List[Document]:
        """Get documents updated after a specific date."""
        pass
    
    @abstractmethod
    async def search_documents(
        self,
        query: str,
        limit: int = 100,
        offset: int = 0,
        filters: Optional[Dict[str, Any]] = None,
    ) -> List[Document]:
        """Search documents by content or metadata."""
        pass
    
    @abstractmethod
    async def get_documents_needing_processing(self, limit: int = 100) -> List[Document]:
        """Get documents that need processing (pending or failed with retry count < 3)."""
        pass
    
    @abstractmethod
    async def get_documents_by_embedding_model(self, model: str) -> List[Document]:
        """Get documents processed with a specific embedding model."""
        pass
    
    @abstractmethod
    async def update_document_status(
        self,
        document_id: str,
        status: DocumentStatus,
        error_message: Optional[str] = None,
    ) -> bool:
        """Update document status."""
        pass
    
    @abstractmethod
    async def increment_retry_count(self, document_id: str) -> bool:
        """Increment document retry count."""
        pass
    
    @abstractmethod
    async def bulk_create(self, documents: List[Document]) -> List[Document]:
        """Create multiple documents in bulk."""
        pass
    
    @abstractmethod
    async def bulk_update_status(
        self,
        document_ids: List[str],
        status: DocumentStatus,
    ) -> int:
        """Update status for multiple documents."""
        pass
    
    @abstractmethod
    async def get_statistics(self) -> Dict[str, Any]:
        """Get document repository statistics."""
        pass
    
    @abstractmethod
    async def cleanup_old_documents(
        self,
        older_than: datetime,
        status: Optional[DocumentStatus] = None,
    ) -> int:
        """Clean up old documents."""
        pass
