"""Search API router for MCP RAG Server."""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, HTTPException, Depends, Query
from pydantic import BaseModel, Field

from core.application.services import SearchService, EmbeddingService
from core.domain.entities import SearchType, SearchScope, SearchFilters
from config.settings import get_settings


class SearchRequest(BaseModel):
    """Search request model."""
    query: str = Field(..., description="Search query text")
    search_type: SearchType = Field(default=SearchType.SEMANTIC, description="Type of search")
    search_scope: SearchScope = Field(default=SearchScope.ALL, description="Search scope")
    max_results: int = Field(default=10, ge=1, le=100, description="Maximum number of results")
    similarity_threshold: float = Field(default=0.7, ge=0.0, le=1.0, description="Similarity threshold")
    min_relevance_score: Optional[float] = Field(default=None, ge=0.0, le=1.0, description="Minimum relevance score")
    document_types: Optional[List[str]] = Field(default=None, description="Filter by document types")
    domains: Optional[List[str]] = Field(default=None, description="Filter by domains")
    user_id: Optional[str] = Field(default=None, description="User ID for tracking")
    session_id: Optional[str] = Field(default=None, description="Session ID for tracking")


class SearchResult(BaseModel):
    """Search result model."""
    id: str
    content: str
    score: float
    document_id: str
    chunk_id: str
    metadata: Dict[str, Any]
    highlights: List[str]


class SearchResponse(BaseModel):
    """Search response model."""
    query: str
    search_type: str
    results: List[SearchResult]
    total_results: int
    execution_time_ms: float
    query_id: str


class SuggestionsResponse(BaseModel):
    """Search suggestions response model."""
    suggestions: List[str]


router = APIRouter()


# Dependency to get search service
async def get_search_service() -> SearchService:
    """Get search service instance."""
    # This would be injected via dependency injection in a real app
    # For now, we'll create it here
    settings = get_settings()
    
    # Import here to avoid circular imports
    from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
    from core.infrastructure.database.supabase_chunk_repository import SupabaseChunkRepository
    from core.application.services import DocumentService, ChunkService
    from core.domain.repositories import SearchQueryRepository
    
    # Mock search query repository for now
    class MockSearchQueryRepository(SearchQueryRepository):
        async def create(self, query): return query
        async def update(self, query): return query
        async def get_cached_results(self, query_text): return None
        async def cache_query_results(self, query_text, results, ttl_seconds): return True
        async def get_similar_queries(self, query_text, similarity_threshold, limit): return []
        async def get_popular_queries(self, time_period, limit): return []
        async def get_query_analytics(self, start_date, end_date, user_id): return {}
        # Add other required methods as needed
        async def get_by_id(self, query_id): return None
        async def delete(self, query_id): return False
        async def list_queries(self, limit, offset, user_id, session_id, search_type): return []
        async def count_queries(self, user_id, session_id, search_type): return 0
        async def get_queries_by_user(self, user_id, limit, offset): return []
        async def get_queries_by_session(self, session_id, limit, offset): return []
        async def get_queries_by_text(self, query_text, exact_match, limit): return []
        async def get_queries_created_after(self, date): return []
        async def get_queries_executed_after(self, date): return []
        async def get_slow_queries(self, min_execution_time, limit): return []
        async def get_failed_queries(self, limit, offset): return []
        async def search_queries(self, search_text, limit, offset): return []
        async def get_performance_metrics(self, start_date, end_date): return {}
        async def invalidate_cache(self, query_text): return 0
        async def bulk_create(self, queries): return queries
        async def cleanup_old_queries(self, older_than, keep_successful): return 0
        async def export_queries(self, start_date, end_date, format): return ""
    
    # Create repositories
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    chunk_repository = SupabaseChunkRepository(settings.supabase_url, settings.supabase_service_key)
    search_query_repository = MockSearchQueryRepository()
    
    # Create services
    doc_service = DocumentService(doc_repository)
    chunk_service = ChunkService(chunk_repository)
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repository,
        openai_api_key=settings.openai_api_key,
        model=settings.openai_model,
    )
    
    # Create search service
    search_service = SearchService(
        search_query_repository=search_query_repository,
        chunk_repository=chunk_repository,
        document_repository=doc_repository,
        embedding_service=embedding_service,
    )
    
    return search_service


@router.post("/search", response_model=SearchResponse)
async def search(
    request: SearchRequest,
    search_service: SearchService = Depends(get_search_service),
):
    """Perform a search query."""
    try:
        # Create search filters
        filters = SearchFilters(
            max_results=request.max_results,
            min_relevance_score=request.min_relevance_score,
            document_types=request.document_types,
            domains=request.domains,
        )
        
        # Create search query
        search_query = await search_service.create_search_query(
            query_text=request.query,
            search_type=request.search_type,
            search_scope=request.search_scope,
            filters=filters,
            user_id=request.user_id,
            session_id=request.session_id,
        )
        
        # Set similarity threshold
        search_query.similarity_threshold = request.similarity_threshold
        
        # Execute search
        executed_query = await search_service.execute_search(search_query)
        
        # Convert results to response format
        results = []
        for result in executed_query.results:
            results.append(SearchResult(
                id=result.id,
                content=result.content,
                score=result.score,
                document_id=result.document_id,
                chunk_id=result.chunk_id,
                metadata=result.metadata,
                highlights=result.highlights,
            ))
        
        return SearchResponse(
            query=executed_query.query_text,
            search_type=executed_query.search_type.value,
            results=results,
            total_results=executed_query.total_results,
            execution_time_ms=executed_query.execution_time_ms or 0.0,
            query_id=executed_query.id,
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/search", response_model=SearchResponse)
async def search_get(
    q: str = Query(..., description="Search query"),
    search_type: SearchType = Query(default=SearchType.SEMANTIC, description="Search type"),
    max_results: int = Query(default=10, ge=1, le=100, description="Maximum results"),
    similarity_threshold: float = Query(default=0.7, ge=0.0, le=1.0, description="Similarity threshold"),
    search_service: SearchService = Depends(get_search_service),
):
    """Perform a search query via GET request."""
    request = SearchRequest(
        query=q,
        search_type=search_type,
        max_results=max_results,
        similarity_threshold=similarity_threshold,
    )
    
    return await search(request, search_service)


@router.get("/suggestions", response_model=SuggestionsResponse)
async def get_search_suggestions(
    q: str = Query(..., description="Partial query for suggestions"),
    limit: int = Query(default=5, ge=1, le=20, description="Maximum suggestions"),
    search_service: SearchService = Depends(get_search_service),
):
    """Get search suggestions based on partial query."""
    try:
        suggestions = await search_service.get_search_suggestions(q, limit)
        
        return SuggestionsResponse(suggestions=suggestions)
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")


@router.get("/popular", response_model=List[Dict[str, Any]])
async def get_popular_queries(
    limit: int = Query(default=10, ge=1, le=50, description="Maximum queries"),
    search_service: SearchService = Depends(get_search_service),
):
    """Get popular search queries."""
    try:
        popular_queries = await search_service.get_popular_queries(limit=limit)
        
        return popular_queries
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get popular queries: {str(e)}")


@router.get("/analytics", response_model=Dict[str, Any])
async def get_search_analytics(
    user_id: Optional[str] = Query(default=None, description="Filter by user ID"),
    search_service: SearchService = Depends(get_search_service),
):
    """Get search analytics."""
    try:
        analytics = await search_service.get_search_analytics(user_id=user_id)
        
        return analytics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")


@router.post("/similar/{chunk_id}", response_model=SearchResponse)
async def find_similar_chunks(
    chunk_id: str,
    limit: int = Query(default=10, ge=1, le=50, description="Maximum results"),
    similarity_threshold: float = Query(default=0.8, ge=0.0, le=1.0, description="Similarity threshold"),
    search_service: SearchService = Depends(get_search_service),
):
    """Find chunks similar to a specific chunk."""
    try:
        # Create similarity search query
        search_query = await search_service.create_search_query(
            query_text=f"Similar to chunk {chunk_id}",
            search_type=SearchType.SIMILARITY,
            context={"reference_chunk_id": chunk_id},
        )
        
        search_query.similarity_threshold = similarity_threshold
        search_query.filters.max_results = limit
        
        # Execute search
        executed_query = await search_service.execute_search(search_query)
        
        # Convert results
        results = []
        for result in executed_query.results:
            results.append(SearchResult(
                id=result.id,
                content=result.content,
                score=result.score,
                document_id=result.document_id,
                chunk_id=result.chunk_id,
                metadata=result.metadata,
                highlights=result.highlights,
            ))
        
        return SearchResponse(
            query=executed_query.query_text,
            search_type=executed_query.search_type.value,
            results=results,
            total_results=executed_query.total_results,
            execution_time_ms=executed_query.execution_time_ms or 0.0,
            query_id=executed_query.id,
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Similarity search failed: {str(e)}")
