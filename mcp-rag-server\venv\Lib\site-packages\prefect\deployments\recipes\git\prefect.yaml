prefect-version: null
name: null
description: "Store code within git repository"

build: null
push: null
pull:
  - prefect.deployments.steps.git_clone:
      repository: "{{ repository }}"
      branch: "{{ branch }}"
      access_token: null

deployments:
  - name: null
    version: null
    tags: []
    description: null
    schedule: {}
    flow_name: null
    entrypoint: null
    parameters: {}
    work_pool:
      name: null
      work_queue_name: null
      job_variables: {}