import{d as k,e as f,ae as y,g as o,ci as m,f as _,i as d,c as C,o as g,j as s,k as t,n as l,cj as v,ck as B}from"./index-ei-kaitd.js";import{u as T}from"./usePageTitle-LeBMnqrg.js";const S=k({__name:"BlocksCatalog",setup(h){const c=f(),a=y(null),p=o(()=>a.value?[a.value]:[]),{filter:n}=m({blockSchemas:{blockCapabilities:p}}),i=_(c.blockTypes.getBlockTypes,[n]),u=o(()=>i.response??[]);return T("Blocks Catalog"),(x,e)=>{const r=d("p-layout-default");return g(),C(r,{class:"blocks-catalog"},{header:s(()=>[t(l(B))]),default:s(()=>[t(l(v),{capability:a.value,"onUpdate:capability":e[0]||(e[0]=b=>a.value=b),"block-types":u.value},null,8,["capability","block-types"])]),_:1})}}});export{S as default};
//# sourceMappingURL=BlocksCatalog-Kkf9NBQY.js.map
