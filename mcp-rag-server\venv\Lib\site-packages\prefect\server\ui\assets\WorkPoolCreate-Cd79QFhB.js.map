{"version": 3, "file": "WorkPoolCreate-Cd79QFhB.js", "sources": ["../../src/pages/WorkPoolCreate.vue"], "sourcesContent": ["<template>\n  <p-layout-default>\n    <template #header>\n      <PageHeadingWorkPoolCreate />\n    </template>\n    <WorkPoolCreateWizard />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeadingWorkPoolCreate, WorkPoolCreateWizard } from '@prefecthq/prefect-ui-library'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  usePageTitle('Create Work Pool')\n</script>"], "names": ["usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingWorkPoolCreate", "WorkPoolCreateWizard"], "mappings": "6LAaE,OAAAA,EAAa,kBAAkB,mDAZ/BC,EAKmBC,EAAA,KAAA,CAJN,SACT,IAA6B,CAA7BC,EAA6BC,EAAAC,CAAA,CAAA,CAAA,aAE/B,IAAwB,CAAxBF,EAAwBC,EAAAE,CAAA,CAAA,CAAA"}