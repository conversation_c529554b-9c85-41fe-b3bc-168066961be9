"""Chunk entity for the MCP RAG Server."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
import uuid


@dataclass
class ChunkMetadata:
    """Chunk metadata container."""
    section_title: Optional[str] = None
    page_number: Optional[int] = None
    paragraph_index: Optional[int] = None
    heading_level: Optional[int] = None
    table_data: Optional[Dict[str, Any]] = None
    image_references: List[str] = field(default_factory=list)
    link_references: List[str] = field(default_factory=list)
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Chunk:
    """Document chunk entity for vector storage."""
    
    # Primary identifiers
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    document_id: str = ""
    
    # Content
    content: str = ""
    content_length: int = 0
    
    # Position and structure
    chunk_index: int = 0
    start_position: int = 0
    end_position: int = 0
    
    # Vector data
    embedding: Optional[List[float]] = None
    embedding_model: Optional[str] = None
    
    # Metadata
    metadata: ChunkMetadata = field(default_factory=ChunkMetadata)
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    
    # Quality metrics
    relevance_score: Optional[float] = None
    readability_score: Optional[float] = None
    
    def __post_init__(self):
        """Post-initialization processing."""
        if not self.content_length and self.content:
            self.content_length = len(self.content)
    
    def update_embedding(self, embedding: List[float], model: str):
        """Update chunk embedding."""
        self.embedding = embedding
        self.embedding_model = model
        self.updated_at = datetime.utcnow()
    
    def calculate_similarity(self, other_embedding: List[float]) -> float:
        """Calculate cosine similarity with another embedding."""
        if not self.embedding or not other_embedding:
            return 0.0
        
        # Simple dot product for cosine similarity (assuming normalized vectors)
        return sum(a * b for a, b in zip(self.embedding, other_embedding))
    
    def is_valid(self) -> bool:
        """Check if chunk is valid for processing."""
        return (
            bool(self.content.strip()) and
            self.content_length > 10 and  # Minimum content length
            bool(self.document_id)
        )
    
    def get_preview(self, max_length: int = 100) -> str:
        """Get a preview of the chunk content."""
        if len(self.content) <= max_length:
            return self.content
        return self.content[:max_length] + "..."
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "document_id": self.document_id,
            "content": self.content,
            "content_length": self.content_length,
            "chunk_index": self.chunk_index,
            "start_position": self.start_position,
            "end_position": self.end_position,
            "embedding": self.embedding,
            "embedding_model": self.embedding_model,
            "metadata": {
                "section_title": self.metadata.section_title,
                "page_number": self.metadata.page_number,
                "paragraph_index": self.metadata.paragraph_index,
                "heading_level": self.metadata.heading_level,
                "table_data": self.metadata.table_data,
                "image_references": self.metadata.image_references,
                "link_references": self.metadata.link_references,
                "custom_fields": self.metadata.custom_fields,
            },
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "relevance_score": self.relevance_score,
            "readability_score": self.readability_score,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Chunk":
        """Create chunk from dictionary."""
        metadata = ChunkMetadata(
            section_title=data.get("metadata", {}).get("section_title"),
            page_number=data.get("metadata", {}).get("page_number"),
            paragraph_index=data.get("metadata", {}).get("paragraph_index"),
            heading_level=data.get("metadata", {}).get("heading_level"),
            table_data=data.get("metadata", {}).get("table_data"),
            image_references=data.get("metadata", {}).get("image_references", []),
            link_references=data.get("metadata", {}).get("link_references", []),
            custom_fields=data.get("metadata", {}).get("custom_fields", {}),
        )
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            document_id=data.get("document_id", ""),
            content=data.get("content", ""),
            content_length=data.get("content_length", 0),
            chunk_index=data.get("chunk_index", 0),
            start_position=data.get("start_position", 0),
            end_position=data.get("end_position", 0),
            embedding=data.get("embedding"),
            embedding_model=data.get("embedding_model"),
            metadata=metadata,
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.utcnow(),
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.utcnow(),
            relevance_score=data.get("relevance_score"),
            readability_score=data.get("readability_score"),
        )
