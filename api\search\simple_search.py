"""Simple search API for testing."""

from typing import List, Dict, Any
from fastapi import APIRouter, HTTPException, Query
from pydantic import BaseModel


class SimpleSearchRequest(BaseModel):
    """Simple search request."""
    query: str
    max_results: int = 10


class SimpleSearchResult(BaseModel):
    """Simple search result."""
    id: str
    content: str
    score: float


class SimpleSearchResponse(BaseModel):
    """Simple search response."""
    query: str
    results: List[SimpleSearchResult]
    total_results: int


router = APIRouter()


@router.get("/simple", response_model=SimpleSearchResponse)
async def simple_search(
    q: str = Query(..., description="Search query"),
    max_results: int = Query(default=10, ge=1, le=100, description="Maximum results"),
):
    """Simple search endpoint for testing."""
    try:
        # Mock search results for testing
        mock_results = [
            SimpleSearchResult(
                id="1",
                content=f"This is a mock search result for query: {q}",
                score=0.95,
            ),
            SimpleSearchResult(
                id="2", 
                content=f"Another mock result related to: {q}",
                score=0.87,
            ),
            SimpleSearchResult(
                id="3",
                content=f"Third mock result about: {q}",
                score=0.76,
            ),
        ]
        
        # Limit results
        results = mock_results[:max_results]
        
        return SimpleSearchResponse(
            query=q,
            results=results,
            total_results=len(results),
        )
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/test")
async def test_endpoint():
    """Test endpoint to verify API is working."""
    return {"message": "Search API is working!", "status": "ok"}
