#!/usr/bin/env python3
"""Process existing documents into chunks with embeddings."""

import asyncio
import time
from typing import List
from core.application.services import (
    DocumentService, ChunkService, EmbeddingService
)
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.infrastructure.database.supabase_chunk_repository import SupabaseChunkRepository
from config.settings import get_settings


async def process_all_eufunds_documents():
    """Process all eufunds documents into chunks with embeddings."""
    print('🧩 PROCESSING EXISTING EUFUNDS DOCUMENTS')
    print('=' * 60)
    
    # Setup services
    settings = get_settings()
    
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    chunk_repository = SupabaseChunkRepository(settings.supabase_url, settings.supabase_service_key)
    
    doc_service = DocumentService(doc_repository)
    chunk_service = ChunkService(chunk_repository)
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repository,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    chunk_processor = ChunkProcessor(
        document_service=doc_service,
        chunk_service=chunk_service,
        embedding_service=embedding_service,
        chunk_size=800,  # Good size for RAG
        chunk_overlap=200,
    )
    
    print('✅ Services initialized')
    
    # Get all documents
    print('\n📄 Getting all documents...')
    documents = await doc_service.get_recent_documents(limit=50)
    
    # Filter eufunds documents
    eufunds_docs = [doc for doc in documents if 'eufunds' in doc.url.lower()]
    
    print(f'📊 Found {len(documents)} total documents')
    print(f'🇪🇺 Found {len(eufunds_docs)} eufunds documents')
    
    if not eufunds_docs:
        print('❌ No eufunds documents to process!')
        return
    
    # Show documents to process
    print(f'\n📝 Documents to process:')
    for i, doc in enumerate(eufunds_docs, 1):
        content_length = len(doc.content) if doc.content else 0
        print(f'   {i}. {doc.url} ({content_length} chars)')
    
    # Process each document
    print(f'\n🔄 Processing documents...')
    
    start_time = time.time()
    processed = 0
    failed = 0
    total_chunks = 0
    
    for i, doc in enumerate(eufunds_docs, 1):
        print(f'\n📄 Processing {i}/{len(eufunds_docs)}: {doc.url}')
        
        try:
            # Process document
            success = await chunk_processor.process_document(doc.id)
            
            if success:
                processed += 1
                
                # Get chunks for this document
                chunks = await chunk_service.get_chunks_by_document(doc.id)
                doc_chunks = len(chunks)
                total_chunks += doc_chunks
                
                print(f'   ✅ Success: {doc_chunks} chunks created')
                
                # Show sample chunk
                if chunks:
                    sample_chunk = chunks[0]
                    embedding_info = f"{len(sample_chunk.embedding)} dims" if sample_chunk.embedding else "no embedding"
                    print(f'   📦 Sample chunk: {len(sample_chunk.content)} chars, {embedding_info}')
                    
            else:
                failed += 1
                print(f'   ❌ Failed to process document')
                
        except Exception as e:
            failed += 1
            print(f'   ❌ Error: {e}')
    
    processing_time = time.time() - start_time
    
    # Final statistics
    print(f'\n📊 PROCESSING RESULTS')
    print('=' * 30)
    print(f'✅ Documents processed: {processed}/{len(eufunds_docs)}')
    print(f'❌ Documents failed: {failed}')
    print(f'🧩 Total chunks created: {total_chunks}')
    print(f'⏱️  Processing time: {processing_time:.2f}s')
    
    if processed > 0:
        print(f'📈 Average chunks per document: {total_chunks / processed:.1f}')
        print(f'📈 Average processing time: {processing_time / processed:.2f}s per doc')
    
    # Get final chunk statistics
    chunk_stats = await chunk_service.get_statistics()
    print(f'\n📊 CHUNK STATISTICS')
    print('=' * 20)
    print(f'Total chunks in database: {chunk_stats["total_chunks"]}')
    print(f'Chunks with embeddings: {chunk_stats["chunks_with_embeddings"]}')
    
    if chunk_stats["total_chunks"] > 0:
        embedding_coverage = (chunk_stats["chunks_with_embeddings"] / chunk_stats["total_chunks"]) * 100
        print(f'Embedding coverage: {embedding_coverage:.1f}%')
    
    return {
        "processed": processed,
        "failed": failed,
        "total_chunks": total_chunks,
        "processing_time": processing_time,
        "chunk_statistics": chunk_stats
    }


async def test_rag_search():
    """Test RAG search functionality."""
    print(f'\n🔍 TESTING RAG SEARCH')
    print('=' * 30)
    
    # Setup services
    settings = get_settings()
    chunk_repository = SupabaseChunkRepository(settings.supabase_url, settings.supabase_service_key)
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repository,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    # Test questions
    test_questions = [
        "Какви са условията за кандидатстване за европейски фондове?",
        "Кои са основните програми за финансиране в България?",
        "Какви документи са необходими за подаване на проект?",
        "Как мога да се свържа с отговорните лица?",
        "Какви са отключващите условия?"
    ]
    
    successful_searches = 0
    total_searches = len(test_questions)
    
    for i, question in enumerate(test_questions, 1):
        print(f'\n❓ Question {i}: {question}')
        
        try:
            # Generate embedding for question
            question_embedding = await embedding_service.generate_embedding(question)
            
            # Search for relevant chunks
            similar_chunks = await chunk_repository.search_by_embedding(
                embedding=question_embedding,
                limit=3,
                similarity_threshold=0.7
            )
            
            if similar_chunks:
                successful_searches += 1
                avg_similarity = sum(score for _, score in similar_chunks) / len(similar_chunks)
                
                print(f'   ✅ Found {len(similar_chunks)} relevant chunks')
                print(f'   📊 Avg similarity: {avg_similarity:.3f}')
                
                # Show top result
                top_chunk, top_score = similar_chunks[0]
                preview = top_chunk.content[:150] + "..." if len(top_chunk.content) > 150 else top_chunk.content
                print(f'   🎯 Top result ({top_score:.3f}): "{preview}"')
            else:
                print(f'   ❌ No relevant chunks found')
                
        except Exception as e:
            print(f'   ❌ Search error: {e}')
    
    # Search results
    success_rate = (successful_searches / total_searches) * 100
    print(f'\n📊 SEARCH RESULTS')
    print('=' * 20)
    print(f'Successful searches: {successful_searches}/{total_searches}')
    print(f'Success rate: {success_rate:.1f}%')
    
    if success_rate >= 80:
        print('🟢 EXCELLENT - RAG search working great!')
    elif success_rate >= 60:
        print('🟡 GOOD - RAG search working well!')
    elif success_rate >= 40:
        print('🟠 FAIR - RAG search needs improvement!')
    else:
        print('🔴 POOR - RAG search not working!')
    
    return success_rate


async def main():
    """Main function."""
    print('🚀 COMPLETE RAG SYSTEM ACTIVATION')
    print('=' * 70)
    
    try:
        # Step 1: Process existing documents
        processing_results = await process_all_eufunds_documents()
        
        if processing_results and processing_results["processed"] > 0:
            # Step 2: Test RAG search
            search_success_rate = await test_rag_search()
            
            # Final assessment
            print(f'\n🎯 FINAL ASSESSMENT')
            print('=' * 20)
            
            if processing_results["total_chunks"] > 0 and search_success_rate >= 60:
                print('🟢 SUCCESS! RAG SYSTEM IS FULLY OPERATIONAL!')
                print(f'✅ {processing_results["total_chunks"]} chunks processed')
                print(f'✅ {search_success_rate:.1f}% search success rate')
                print('🚀 Ready for production use!')
            else:
                print('🟡 PARTIAL SUCCESS - System working but needs optimization')
        else:
            print('❌ FAILED - Could not process documents')
            
    except Exception as e:
        print(f'❌ Error: {e}')
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
