import{d as u,s,c,a as l,w as m,o as p,r as i,m as f,h,b as v,D as d}from"./index-ei-kaitd.js";const r=a=>{const{value:e,isCustom:t}=f();if(h(a.query)&&t.value)try{const o=v.map("SavedSearchFilter",e.value,"LocationQuery");return{...a,query:o}}catch(o){console.error(o)}return!0},D=u({beforeRouteEnter:r,beforeRouteUpdate:r,__name:"RunsPageWithDefaultFilter",props:{component:{type:Function}},setup(a){const e=a,t=s(null);function o(n){return d(n)}return m(e.component,()=>{o(e.component)?e.component().then(n=>{t.value=n.default}):t.value=e.component},{immediate:!0}),(n,y)=>t.value!==null?(p(),c(i(t.value),{key:0})):l("",!0)}});export{D as default};
//# sourceMappingURL=RunsPageWithDefaultFilter-BpHg3lPU-B2ixWUwH.js.map
