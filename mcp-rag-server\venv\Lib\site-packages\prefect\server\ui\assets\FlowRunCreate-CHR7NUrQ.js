import{c9 as b,d as h,e as k,W as F,u as N,al as S,ca as x,ae as D,g as I,i as J,c as B,a as O,n as e,o as T,j as d,k as p,cb as V,cc as $,cd as P,ce as U,be as i,L as f,$ as W}from"./index-ei-kaitd.js";import{u as j}from"./usePageTitle-LeBMnqrg.js";class E extends b{parse(t){return JSON.parse(decodeURIComponent(t??""))}format(t){return encodeURIComponent(JSON.stringify(t))}}const Q=h({__name:"FlowRunCreate",setup(y){const t=k(),l=F("deploymentId"),c=N(),w=S("parameters",E,void 0),{deployment:a}=x(l),o=D(!1),R=async u=>{var m,s;if(!o.value)try{o.value=!0;const n=await t.deployments.createDeploymentFlowRun(l.value,u),r=((s=(m=u.state)==null?void 0:m.stateDetails)==null?void 0:s.scheduledTime)??void 0,C=!r,g=P(U,{flowRun:n,flowRunRoute:i.flowRun,router:c,immediate:C,startTime:r});f(g,"success"),c.push(i.deployment(l.value))}catch(n){const r=W(n,"Something went wrong trying to create a flow run");f(r,"error"),console.error(n)}finally{o.value=!1}},_=()=>{c.back()},v=I(()=>a.value?`Create Flow Run for Deployment: ${a.value.name}`:"Create Flow Run for Deployment");return j(v),(u,m)=>{const s=J("p-layout-default");return e(a)?(T(),B(s,{key:0},{header:d(()=>[p(e($),{deployment:e(a)},null,8,["deployment"])]),default:d(()=>[p(e(V),{deployment:e(a),parameters:e(w),disabled:o.value,onSubmit:R,onCancel:_},null,8,["deployment","parameters","disabled"])]),_:1})):O("",!0)}}});export{Q as default};
//# sourceMappingURL=FlowRunCreate-CHR7NUrQ.js.map
