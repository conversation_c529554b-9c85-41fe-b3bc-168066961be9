"""Search query repository interface for the MCP RAG Server."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..entities import SearchQuery, SearchType, SearchScope


class SearchQueryRepository(ABC):
    """Abstract search query repository interface."""
    
    @abstractmethod
    async def create(self, query: SearchQuery) -> SearchQuery:
        """Create a new search query."""
        pass
    
    @abstractmethod
    async def get_by_id(self, query_id: str) -> Optional[SearchQuery]:
        """Get search query by ID."""
        pass
    
    @abstractmethod
    async def update(self, query: SearchQuery) -> SearchQuery:
        """Update an existing search query."""
        pass
    
    @abstractmethod
    async def delete(self, query_id: str) -> bool:
        """Delete a search query."""
        pass
    
    @abstractmethod
    async def list_queries(
        self,
        limit: int = 100,
        offset: int = 0,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        search_type: Optional[SearchType] = None,
    ) -> List[SearchQuery]:
        """List search queries with optional filters."""
        pass
    
    @abstractmethod
    async def count_queries(
        self,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None,
        search_type: Optional[SearchType] = None,
    ) -> int:
        """Count search queries with optional filters."""
        pass
    
    @abstractmethod
    async def get_queries_by_user(
        self,
        user_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[SearchQuery]:
        """Get all queries by a specific user."""
        pass
    
    @abstractmethod
    async def get_queries_by_session(
        self,
        session_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[SearchQuery]:
        """Get all queries in a specific session."""
        pass
    
    @abstractmethod
    async def get_queries_by_text(
        self,
        query_text: str,
        exact_match: bool = False,
        limit: int = 100,
    ) -> List[SearchQuery]:
        """Get queries by text content."""
        pass
    
    @abstractmethod
    async def get_similar_queries(
        self,
        query_text: str,
        similarity_threshold: float = 0.8,
        limit: int = 10,
    ) -> List[SearchQuery]:
        """Get similar queries based on text similarity."""
        pass
    
    @abstractmethod
    async def get_popular_queries(
        self,
        time_period: Optional[datetime] = None,
        limit: int = 100,
    ) -> List[Dict[str, Any]]:
        """Get popular queries with frequency counts."""
        pass
    
    @abstractmethod
    async def get_queries_created_after(self, date: datetime) -> List[SearchQuery]:
        """Get queries created after a specific date."""
        pass
    
    @abstractmethod
    async def get_queries_executed_after(self, date: datetime) -> List[SearchQuery]:
        """Get queries executed after a specific date."""
        pass
    
    @abstractmethod
    async def get_slow_queries(
        self,
        min_execution_time: float = 1000.0,  # milliseconds
        limit: int = 100,
    ) -> List[SearchQuery]:
        """Get queries with execution time above threshold."""
        pass
    
    @abstractmethod
    async def get_failed_queries(
        self,
        limit: int = 100,
        offset: int = 0,
    ) -> List[SearchQuery]:
        """Get queries that failed to execute."""
        pass
    
    @abstractmethod
    async def search_queries(
        self,
        search_text: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[SearchQuery]:
        """Search queries by content."""
        pass
    
    @abstractmethod
    async def get_query_analytics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        user_id: Optional[str] = None,
    ) -> Dict[str, Any]:
        """Get query analytics and statistics."""
        pass
    
    @abstractmethod
    async def get_performance_metrics(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
    ) -> Dict[str, Any]:
        """Get query performance metrics."""
        pass
    
    @abstractmethod
    async def cache_query_results(
        self,
        query_text: str,
        results: List[Dict[str, Any]],
        ttl_seconds: int = 3600,
    ) -> bool:
        """Cache query results."""
        pass
    
    @abstractmethod
    async def get_cached_results(
        self,
        query_text: str,
    ) -> Optional[List[Dict[str, Any]]]:
        """Get cached query results."""
        pass
    
    @abstractmethod
    async def invalidate_cache(
        self,
        query_text: Optional[str] = None,
    ) -> int:
        """Invalidate query cache."""
        pass
    
    @abstractmethod
    async def bulk_create(self, queries: List[SearchQuery]) -> List[SearchQuery]:
        """Create multiple queries in bulk."""
        pass
    
    @abstractmethod
    async def cleanup_old_queries(
        self,
        older_than: datetime,
        keep_successful: bool = True,
    ) -> int:
        """Clean up old queries."""
        pass
    
    @abstractmethod
    async def export_queries(
        self,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        format: str = "json",
    ) -> str:
        """Export queries to file."""
        pass
