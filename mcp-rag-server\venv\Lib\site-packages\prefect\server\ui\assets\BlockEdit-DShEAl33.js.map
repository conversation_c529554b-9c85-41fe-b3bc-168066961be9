{"version": 3, "file": "BlockEdit-DShEAl33.js", "sources": ["../../src/pages/BlockEdit.vue"], "sourcesContent": ["<template>\n  <p-layout-default v-if=\"blockDocument\" class=\"block-edit\">\n    <template #header>\n      <PageHeadingBlockEdit :block-document=\"blockDocument\" />\n    </template>\n\n    <BlockTypeCardLayout :block-type=\"blockType\">\n      <BlockSchemaEditForm v-model:data=\"data\" v-bind=\"{ name, blockSchema }\" v-on=\"{ submit, cancel }\" />\n    </BlockTypeCardLayout>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { showToast } from '@prefecthq/prefect-design'\n  import { BlockTypeCardLayout, BlockSchemaEditForm, PageHeadingBlockEdit, BlockDocumentUpdate, useWorkspaceApi } from '@prefecthq/prefect-ui-library'\n  import { useRouteParam } from '@prefecthq/vue-compositions'\n  import { ref } from 'vue'\n  import { useRouter } from 'vue-router'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { routes } from '@/router/routes'\n\n  const api = useWorkspaceApi()\n  const router = useRouter()\n  const blockDocumentId = useRouteParam('blockDocumentId')\n  const blockDocument = await api.blockDocuments.getBlockDocument(blockDocumentId.value)\n  const { blockType, blockSchema } = blockDocument\n  const data = ref(blockDocument.data)\n  const name = ref(blockDocument.name)\n\n  function submit(request: BlockDocumentUpdate): void {\n    api.blockDocuments\n      .updateBlockDocument(blockDocument.id, request)\n      .then(() => {\n        showToast('Block updated successfully', 'success')\n        router.push(routes.block(blockDocumentId.value))\n      })\n      .catch(err => {\n        showToast('Failed to update block', 'error')\n        console.error(err)\n      })\n  }\n\n  function cancel(): void {\n    router.back()\n  }\n\n  usePageTitle(`Edit Block: ${name.value}`)\n</script>"], "names": ["api", "useWorkspaceApi", "router", "useRouter", "blockDocumentId", "useRouteParam", "blockDocument", "__temp", "__restore", "_withAsyncContext", "blockType", "blockSchema", "data", "ref", "name", "submit", "request", "showToast", "routes", "err", "cancel", "usePageTitle", "_unref", "_createBlock", "_component_p_layout_default", "_createVNode", "PageHeadingBlockEdit", "BlockTypeCardLayout", "_mergeProps", "$event", "_toHandlers"], "mappings": "uRAqBE,MAAMA,EAAMC,EAAgB,EACtBC,EAASC,EAAU,EACnBC,EAAkBC,EAAc,iBAAiB,EACjDC,GAAsB,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAAT,EAAI,eAAe,iBAAiBI,EAAgB,KAAK,CAAA,mBAC/E,CAAE,UAAAM,EAAW,YAAAC,CAAA,EAAgBL,EAC7BM,EAAOC,EAAIP,EAAc,IAAI,EAC7BQ,EAAOD,EAAIP,EAAc,IAAI,EAEnC,SAASS,EAAOC,EAAoC,CAClDhB,EAAI,eACD,oBAAoBM,EAAc,GAAIU,CAAO,EAC7C,KAAK,IAAM,CACVC,EAAU,6BAA8B,SAAS,EACjDf,EAAO,KAAKgB,EAAO,MAAMd,EAAgB,KAAK,CAAC,CAAA,CAChD,EACA,MAAae,GAAA,CACZF,EAAU,yBAA0B,OAAO,EAC3C,QAAQ,MAAME,CAAG,CAAA,CAClB,CAAA,CAGL,SAASC,GAAe,CACtBlB,EAAO,KAAK,CAAA,CAGD,OAAAmB,EAAA,eAAeP,EAAK,KAAK,EAAE,+CA7ChBQ,EAAahB,CAAA,OAArCiB,EAQmBC,EAAA,OARoB,MAAM,YAAA,GAChC,SACT,IAAwD,CAAxDC,EAAwDH,EAAAI,CAAA,EAAA,CAAjC,iBAAgBJ,EAAahB,CAAA,GAAA,KAAA,EAAA,CAAA,gBAAA,CAAA,CAAA,aAGtD,IAEsB,CAFtBmB,EAEsBH,EAAAK,CAAA,EAAA,CAFA,aAAYL,EAASZ,CAAA,GAAA,WACzC,IAAoG,CAApGe,EAAoGH,KAApGM,EAAoG,CAAvE,KAAMhB,EAAI,qCAAJA,EAAI,MAAAiB,EAAA,EAAY,CAAA,KAAAf,EAAA,kBAAMQ,EAAWX,CAAA,CAAA,EAAImB,EAAyB,CAAjB,OAAAf,WAAc,CAAA,EAAA,KAAA,GAAA,CAAA,MAAA,CAAA,CAAA"}