prefect-version: null
name: null
description: "Store code within an S3 bucket"

required_inputs:
  bucket: "The bucket to store and retrieve code from"

build: null

push: 
  - prefect_aws.deployments.steps.push_to_s3:
      id: "push_code"
      requires: "prefect-aws>=0.3.4"
      bucket: "{{ bucket }}"
      folder: "{{ name }}"

pull:
  - prefect_aws.deployments.steps.pull_from_s3:
      id: "pull_code"
      requires: "prefect-aws>=0.3.4"
      bucket: "{{ push_code.bucket }}"
      folder: "{{ push_code.folder }}"

deployments:
  - name: null
    version: null
    tags: []
    description: null
    schedule: {}
    flow_name: null
    entrypoint: null
    parameters: {}
    work_pool:
      name: null
      work_queue_name: null
      job_variables: {}