# PowerShell script to create __init__.py files
Write-Host "Creating __init__.py files for Python packages..."

# Core packages
New-Item -ItemType File -Path "core\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\domain\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\domain\entities\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\domain\repositories\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\application\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\application\services\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\application\use_cases\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\infrastructure\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\infrastructure\database\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "core\infrastructure\external\__init__.py" -Force | Out-Null

# Crawlers
New-Item -ItemType File -Path "crawlers\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "crawlers\html\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "crawlers\pdf\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "crawlers\hybrid\__init__.py" -Force | Out-Null

# API
New-Item -ItemType File -Path "api\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "api\mcp\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "api\health\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "api\middleware\__init__.py" -Force | Out-Null

# Workflows
New-Item -ItemType File -Path "workflows\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "workflows\prefect\__init__.py" -Force | Out-Null

# Config
New-Item -ItemType File -Path "config\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "config\settings\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "config\database\__init__.py" -Force | Out-Null

# Tests
New-Item -ItemType File -Path "tests\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "tests\unit\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "tests\unit\core\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "tests\integration\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "tests\e2e\__init__.py" -Force | Out-Null

# Monitoring
New-Item -ItemType File -Path "monitoring\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "monitoring\prometheus\__init__.py" -Force | Out-Null
New-Item -ItemType File -Path "monitoring\logging\__init__.py" -Force | Out-Null

Write-Host "✅ All __init__.py files created successfully!"
