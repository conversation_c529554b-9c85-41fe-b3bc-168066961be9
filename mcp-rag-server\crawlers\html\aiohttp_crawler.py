"""Asynchronous HTML crawler using aiohttp."""

import asyncio
import aiohttp
import time
import random
from typing import List, Optional, Dict, Any, Set
from urllib.parse import urljoin, urlparse
from dataclasses import dataclass
from datetime import datetime
import logging

from bs4 import BeautifulSoup
from tenacity import retry, stop_after_attempt, wait_exponential

from core.domain.entities import Document, DocumentType, DocumentStatus, CrawlSession
from core.application.services import DocumentService


@dataclass
class CrawlResult:
    """Result of a crawl operation."""
    url: str
    content: str
    status_code: int
    headers: Dict[str, str]
    links: List[str]
    error: Optional[str] = None
    response_time: float = 0.0


class AiohttpCrawler:
    """Asynchronous HTML crawler using aiohttp."""
    
    def __init__(
        self,
        document_service: DocumentService,
        max_concurrent: int = 10,
        delay_range: tuple = (1.0, 3.0),
        timeout: int = 30,
        user_agent: str = "MCP-RAG-Server/1.0",
        respect_robots_txt: bool = True,
    ):
        self.document_service = document_service
        self.max_concurrent = max_concurrent
        self.delay_range = delay_range
        self.timeout = timeout
        self.user_agent = user_agent
        self.respect_robots_txt = respect_robots_txt
        
        # Rate limiting
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._last_request_time = {}
        
        # Statistics
        self.stats = {
            "pages_crawled": 0,
            "pages_failed": 0,
            "total_size_bytes": 0,
            "average_response_time": 0.0,
        }
        
        self.logger = logging.getLogger(__name__)
    
    async def crawl_url(
        self,
        url: str,
        session: aiohttp.ClientSession,
        crawl_session_id: Optional[str] = None,
    ) -> CrawlResult:
        """Crawl a single URL."""
        async with self._semaphore:
            # Rate limiting
            await self._apply_rate_limit(url)
            
            start_time = time.time()
            
            try:
                headers = {
                    "User-Agent": self.user_agent,
                    "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8",
                    "Accept-Language": "en-US,en;q=0.5",
                    "Accept-Encoding": "gzip, deflate",
                    "Connection": "keep-alive",
                    "Upgrade-Insecure-Requests": "1",
                }
                
                async with session.get(
                    url,
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=self.timeout),
                    allow_redirects=True,
                ) as response:
                    response_time = time.time() - start_time
                    
                    # Check content type
                    content_type = response.headers.get("content-type", "").lower()
                    if not any(ct in content_type for ct in ["text/html", "application/xhtml"]):
                        return CrawlResult(
                            url=url,
                            content="",
                            status_code=response.status,
                            headers=dict(response.headers),
                            links=[],
                            error=f"Unsupported content type: {content_type}",
                            response_time=response_time,
                        )
                    
                    # Read content
                    content = await response.text()
                    
                    # Extract links
                    links = self._extract_links(content, url)
                    
                    # Update statistics
                    self.stats["pages_crawled"] += 1
                    self.stats["total_size_bytes"] += len(content.encode('utf-8'))
                    self._update_average_response_time(response_time)
                    
                    # Create document
                    if crawl_session_id:
                        await self._create_document(
                            url=url,
                            content=content,
                            crawl_session_id=crawl_session_id,
                            response_headers=dict(response.headers),
                        )
                    
                    return CrawlResult(
                        url=url,
                        content=content,
                        status_code=response.status,
                        headers=dict(response.headers),
                        links=links,
                        response_time=response_time,
                    )
                    
            except asyncio.TimeoutError:
                self.stats["pages_failed"] += 1
                error_msg = f"Timeout after {self.timeout} seconds"
                self.logger.warning(f"Timeout crawling {url}: {error_msg}")
                
                return CrawlResult(
                    url=url,
                    content="",
                    status_code=0,
                    headers={},
                    links=[],
                    error=error_msg,
                    response_time=time.time() - start_time,
                )
                
            except Exception as e:
                self.stats["pages_failed"] += 1
                error_msg = str(e)
                self.logger.error(f"Error crawling {url}: {error_msg}")
                
                return CrawlResult(
                    url=url,
                    content="",
                    status_code=0,
                    headers={},
                    links=[],
                    error=error_msg,
                    response_time=time.time() - start_time,
                )
    
    async def crawl_urls(
        self,
        urls: List[str],
        crawl_session_id: Optional[str] = None,
    ) -> List[CrawlResult]:
        """Crawl multiple URLs concurrently."""
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        async with aiohttp.ClientSession(connector=connector) as session:
            tasks = [
                self.crawl_url(url, session, crawl_session_id)
                for url in urls
            ]
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # Handle exceptions
            crawl_results = []
            for i, result in enumerate(results):
                if isinstance(result, Exception):
                    self.logger.error(f"Exception crawling {urls[i]}: {result}")
                    crawl_results.append(CrawlResult(
                        url=urls[i],
                        content="",
                        status_code=0,
                        headers={},
                        links=[],
                        error=str(result),
                    ))
                else:
                    crawl_results.append(result)
            
            return crawl_results
    
    async def crawl_website(
        self,
        start_urls: List[str],
        max_pages: int = 100,
        max_depth: int = 3,
        allowed_domains: Optional[List[str]] = None,
        crawl_session_id: Optional[str] = None,
    ) -> List[CrawlResult]:
        """Crawl an entire website starting from given URLs."""
        if allowed_domains is None:
            allowed_domains = [urlparse(url).netloc for url in start_urls]
        
        visited_urls: Set[str] = set()
        urls_to_crawl: Set[str] = set(start_urls)
        all_results: List[CrawlResult] = []
        
        current_depth = 0
        
        while urls_to_crawl and len(all_results) < max_pages and current_depth < max_depth:
            self.logger.info(f"Crawling depth {current_depth}, {len(urls_to_crawl)} URLs to crawl")
            
            # Get URLs for current batch
            batch_urls = list(urls_to_crawl)[:min(self.max_concurrent, max_pages - len(all_results))]
            
            # Remove from queue
            urls_to_crawl -= set(batch_urls)
            visited_urls.update(batch_urls)
            
            # Crawl batch
            batch_results = await self.crawl_urls(batch_urls, crawl_session_id)
            all_results.extend(batch_results)
            
            # Extract new URLs from successful crawls
            new_urls = set()
            for result in batch_results:
                if result.error is None and result.status_code == 200:
                    for link in result.links:
                        if self._should_crawl_url(link, allowed_domains, visited_urls, urls_to_crawl):
                            new_urls.add(link)
            
            urls_to_crawl.update(new_urls)
            current_depth += 1
            
            # Add delay between batches
            if urls_to_crawl:
                delay = random.uniform(*self.delay_range)
                await asyncio.sleep(delay)
        
        self.logger.info(f"Crawling completed. Total pages: {len(all_results)}")
        return all_results
    
    def _extract_links(self, html_content: str, base_url: str) -> List[str]:
        """Extract all links from HTML content."""
        try:
            soup = BeautifulSoup(html_content, 'html.parser')
            links = []
            
            for tag in soup.find_all(['a', 'link']):
                href = tag.get('href')
                if href:
                    # Convert relative URLs to absolute
                    absolute_url = urljoin(base_url, href)
                    
                    # Basic URL validation
                    parsed = urlparse(absolute_url)
                    if parsed.scheme in ['http', 'https'] and parsed.netloc:
                        links.append(absolute_url)
            
            return list(set(links))  # Remove duplicates
            
        except Exception as e:
            self.logger.warning(f"Error extracting links from {base_url}: {e}")
            return []
    
    def _should_crawl_url(
        self,
        url: str,
        allowed_domains: List[str],
        visited_urls: Set[str],
        queued_urls: Set[str],
    ) -> bool:
        """Check if URL should be crawled."""
        if url in visited_urls or url in queued_urls:
            return False
        
        parsed = urlparse(url)
        
        # Check domain
        if parsed.netloc not in allowed_domains:
            return False
        
        # Skip certain file types
        skip_extensions = {'.pdf', '.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx',
                          '.zip', '.rar', '.tar', '.gz', '.jpg', '.jpeg', '.png',
                          '.gif', '.bmp', '.svg', '.mp3', '.mp4', '.avi', '.mov'}
        
        if any(url.lower().endswith(ext) for ext in skip_extensions):
            return False
        
        # Skip fragments and query parameters for now
        if '#' in url:
            url = url.split('#')[0]
        
        return True
    
    async def _apply_rate_limit(self, url: str):
        """Apply rate limiting per domain."""
        domain = urlparse(url).netloc
        
        if domain in self._last_request_time:
            elapsed = time.time() - self._last_request_time[domain]
            min_delay = self.delay_range[0]
            
            if elapsed < min_delay:
                sleep_time = min_delay - elapsed
                await asyncio.sleep(sleep_time)
        
        self._last_request_time[domain] = time.time()
    
    async def _create_document(
        self,
        url: str,
        content: str,
        crawl_session_id: str,
        response_headers: Dict[str, str],
    ):
        """Create document from crawled content."""
        try:
            # Extract metadata from HTML
            soup = BeautifulSoup(content, 'html.parser')
            
            title = None
            if soup.title:
                title = soup.title.string.strip() if soup.title.string else None
            
            description = None
            meta_desc = soup.find('meta', attrs={'name': 'description'})
            if meta_desc:
                description = meta_desc.get('content')
            
            # Clean content (remove scripts, styles, etc.)
            for script in soup(["script", "style", "nav", "footer", "header"]):
                script.decompose()
            
            clean_content = soup.get_text()
            
            # Create document
            metadata = {
                "title": title,
                "description": description,
                "content_type": response_headers.get("content-type"),
                "content_length": len(content),
            }
            
            await self.document_service.create_document(
                url=url,
                content=clean_content,
                document_type=DocumentType.HTML,
                metadata=metadata,
                crawl_session_id=crawl_session_id,
            )
            
        except Exception as e:
            self.logger.error(f"Error creating document for {url}: {e}")
    
    def _update_average_response_time(self, response_time: float):
        """Update average response time."""
        current_avg = self.stats["average_response_time"]
        pages_crawled = self.stats["pages_crawled"]
        
        if pages_crawled == 1:
            self.stats["average_response_time"] = response_time
        else:
            self.stats["average_response_time"] = (
                (current_avg * (pages_crawled - 1) + response_time) / pages_crawled
            )
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get crawler statistics."""
        return self.stats.copy()
    
    def reset_statistics(self):
        """Reset crawler statistics."""
        self.stats = {
            "pages_crawled": 0,
            "pages_failed": 0,
            "total_size_bytes": 0,
            "average_response_time": 0.0,
        }
