import{d as T,e as F,u as x,al as N,W as P,g as o,bF as m,i as A,c as r,a as u,o as n,j as p,n as k,cn as D,co as V,U as W,cp as X,k as j,cq as q,L as y,cr as w,be as z}from"./index-ei-kaitd.js";import{u as E}from"./usePageTitle-LeBMnqrg.js";const R=T({__name:"BlocksCatalogCreate",setup(H){const s=F(),t=x(),i=N("redirect"),b=P("blockTypeSlug"),d=o(()=>b.value?[b.value]:null),f=m(s.blockTypes.getBlockTypeBySlug,d),e=o(()=>f.response),v=o(()=>e.value?[e.value.id]:null),h=m(s.blockSchemas.getBlockSchemaForBlockType,v),l=o(()=>h.response);function _(a){s.blockDocuments.createBlockDocument(a).then(({id:c})=>g(c)).catch(c=>{y("Failed to create block","error"),console.error(c)})}function S(){t.back()}function g(a){if(y("Block created successfully","success"),i.value){const c=t.resolve(w(i.value));t.push(c);return}t.push(z.block(a))}const B=o(()=>e.value?`Create ${e.value.name} Block`:"Create Block");return E(B),(a,c)=>{const C=A("p-layout-default");return e.value?(n(),r(C,{key:0,class:"blocks-catalog-create"},{header:p(()=>[j(k(q),{"block-type":e.value},null,8,["block-type"])]),default:p(()=>[e.value?(n(),r(k(D),{key:0,"block-type":e.value},{default:p(()=>[l.value?(n(),r(k(V),W({key:l.value.id,"block-schema":l.value},X({submit:_,cancel:S})),null,16,["block-schema"])):u("",!0)]),_:1},8,["block-type"])):u("",!0)]),_:1})):u("",!0)}}});export{R as default};
//# sourceMappingURL=BlocksCatalogCreate-DdwWU2r-.js.map
