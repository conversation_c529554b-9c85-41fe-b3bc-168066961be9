# =============================================================================
# MCP RAG Server Configuration
# =============================================================================

# Database Configuration
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_KEY=your-service-key-here
SUPABASE_ANON_KEY=your-anon-key-here

# Database Connection Pool
DATABASE_POOL_SIZE=10
DATABASE_MAX_OVERFLOW=20
DATABASE_POOL_TIMEOUT=30

# OpenAI Configuration
OPENAI_API_KEY=your-openai-api-key-here
OPENAI_MODEL=text-embedding-3-small
OPENAI_MAX_TOKENS=8192

# Redis Configuration
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=
REDIS_DB=0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=4
API_RELOAD=false

# Security
SECRET_KEY=your-secret-key-here-change-in-production
ENCRYPTION_KEY=your-encryption-key-here-32-chars

# Crawling Configuration
CRAWL_DELAY_MIN=1
CRAWL_DELAY_MAX=3
CRAWL_CONCURRENT_REQUESTS=8
CRAWL_USER_AGENT=MCP-RAG-Server/1.0

# Monitoring
PROMETHEUS_PORT=9090
LOG_LEVEL=INFO
STRUCTURED_LOGGING=true

# Environment
ENVIRONMENT=development
DEBUG=true

# Rate Limiting
RATE_LIMIT_REQUESTS=100
RATE_LIMIT_WINDOW=60

# Prefect Configuration
PREFECT_API_URL=http://localhost:4200/api
PREFECT_LOGGING_LEVEL=INFO
