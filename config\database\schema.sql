-- MCP RAG Server Database Schema
-- This script creates all necessary tables and indexes for the MCP RAG Server

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "vector";
CREATE EXTENSION IF NOT EXISTS "pg_cron";

-- Documents table
CREATE TABLE IF NOT EXISTS documents (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    url TEXT NOT NULL UNIQUE,
    content TEXT,
    raw_content TEXT,
    document_type VARCHAR(20) NOT NULL DEFAULT 'html',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    content_hash VARCHAR(64),
    embedding_model VARCHAR(100),
    chunk_count INTEGER DEFAULT 0,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    processed_at TIMESTAMPTZ,
    crawl_depth INTEGER DEFAULT 0,
    parent_url TEXT,
    crawl_session_id UUID,
    error_message TEXT,
    retry_count INTEGER DEFAULT 0
);

-- Chunks table with vector embeddings
CREATE TABLE IF NOT EXISTS chunks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    document_id UUID NOT NULL REFERENCES documents(id) ON DELETE CASCADE,
    content TEXT NOT NULL,
    content_length INTEGER NOT NULL DEFAULT 0,
    chunk_index INTEGER NOT NULL DEFAULT 0,
    start_position INTEGER NOT NULL DEFAULT 0,
    end_position INTEGER NOT NULL DEFAULT 0,
    embedding vector(1536), -- OpenAI text-embedding-3-small dimension
    embedding_model VARCHAR(100),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    relevance_score FLOAT,
    readability_score FLOAT
);

-- Crawl sessions table
CREATE TABLE IF NOT EXISTS crawl_sessions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255),
    start_urls TEXT[] NOT NULL,
    config JSONB DEFAULT '{}',
    strategy VARCHAR(20) NOT NULL DEFAULT 'breadth_first',
    status VARCHAR(20) NOT NULL DEFAULT 'pending',
    current_depth INTEGER DEFAULT 0,
    urls_to_crawl TEXT[],
    urls_crawled TEXT[],
    urls_failed TEXT[],
    urls_skipped TEXT[],
    stats JSONB DEFAULT '{}',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    started_at TIMESTAMPTZ,
    completed_at TIMESTAMPTZ,
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    error_message TEXT,
    last_error_at TIMESTAMPTZ,
    tags TEXT[],
    description TEXT,
    created_by VARCHAR(255)
);

-- Search queries table
CREATE TABLE IF NOT EXISTS search_queries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    query_text TEXT NOT NULL,
    search_type VARCHAR(20) NOT NULL DEFAULT 'semantic',
    search_scope VARCHAR(20) NOT NULL DEFAULT 'all',
    filters JSONB DEFAULT '{}',
    embedding vector(1536),
    embedding_model VARCHAR(100),
    similarity_threshold FLOAT DEFAULT 0.7,
    results JSONB DEFAULT '[]',
    total_results INTEGER DEFAULT 0,
    execution_time_ms FLOAT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    executed_at TIMESTAMPTZ,
    user_id VARCHAR(255),
    session_id VARCHAR(255),
    context JSONB DEFAULT '{}',
    index_hits INTEGER DEFAULT 0,
    cache_hits INTEGER DEFAULT 0
);

-- Create indexes for better performance

-- Documents indexes
CREATE INDEX IF NOT EXISTS idx_documents_url ON documents(url);
CREATE INDEX IF NOT EXISTS idx_documents_status ON documents(status);
CREATE INDEX IF NOT EXISTS idx_documents_type ON documents(document_type);
CREATE INDEX IF NOT EXISTS idx_documents_created_at ON documents(created_at);
CREATE INDEX IF NOT EXISTS idx_documents_updated_at ON documents(updated_at);
CREATE INDEX IF NOT EXISTS idx_documents_crawl_session ON documents(crawl_session_id);
CREATE INDEX IF NOT EXISTS idx_documents_content_hash ON documents(content_hash);
CREATE INDEX IF NOT EXISTS idx_documents_embedding_model ON documents(embedding_model);

-- Chunks indexes
CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);
CREATE INDEX IF NOT EXISTS idx_chunks_chunk_index ON chunks(chunk_index);
CREATE INDEX IF NOT EXISTS idx_chunks_created_at ON chunks(created_at);
CREATE INDEX IF NOT EXISTS idx_chunks_embedding_model ON chunks(embedding_model);
CREATE INDEX IF NOT EXISTS idx_chunks_content_length ON chunks(content_length);

-- Vector similarity index using HNSW (better than IVFFLAT for most cases)
CREATE INDEX IF NOT EXISTS idx_chunks_embedding_hnsw ON chunks 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Crawl sessions indexes
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_status ON crawl_sessions(status);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_created_at ON crawl_sessions(created_at);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_created_by ON crawl_sessions(created_by);
CREATE INDEX IF NOT EXISTS idx_crawl_sessions_strategy ON crawl_sessions(strategy);

-- Search queries indexes
CREATE INDEX IF NOT EXISTS idx_search_queries_query_text ON search_queries(query_text);
CREATE INDEX IF NOT EXISTS idx_search_queries_search_type ON search_queries(search_type);
CREATE INDEX IF NOT EXISTS idx_search_queries_created_at ON search_queries(created_at);
CREATE INDEX IF NOT EXISTS idx_search_queries_user_id ON search_queries(user_id);
CREATE INDEX IF NOT EXISTS idx_search_queries_session_id ON search_queries(session_id);

-- Vector similarity index for search queries
CREATE INDEX IF NOT EXISTS idx_search_queries_embedding_hnsw ON search_queries 
USING hnsw (embedding vector_cosine_ops) 
WITH (m = 16, ef_construction = 64);

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_documents_updated_at BEFORE UPDATE ON documents
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_chunks_updated_at BEFORE UPDATE ON chunks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_crawl_sessions_updated_at BEFORE UPDATE ON crawl_sessions
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Create partitioning function for chunks (by month)
CREATE OR REPLACE FUNCTION create_monthly_partition(table_name text, start_date date)
RETURNS void AS $$
DECLARE
    partition_name text;
    start_month text;
    end_date date;
BEGIN
    start_month := to_char(start_date, 'YYYY_MM');
    partition_name := table_name || '_' || start_month;
    end_date := start_date + interval '1 month';
    
    EXECUTE format('CREATE TABLE IF NOT EXISTS %I PARTITION OF %I 
                    FOR VALUES FROM (%L) TO (%L)',
                   partition_name, table_name, start_date, end_date);
END;
$$ LANGUAGE plpgsql;

-- Create initial partition for current month
SELECT create_monthly_partition('chunks', date_trunc('month', CURRENT_DATE));

-- Schedule monthly partition creation using pg_cron
-- This will run on the first day of each month at 00:01
SELECT cron.schedule('create-monthly-partitions', '1 0 1 * *', 
    'SELECT create_monthly_partition(''chunks'', date_trunc(''month'', CURRENT_DATE + interval ''1 month''));');

-- Create function for similarity search
CREATE OR REPLACE FUNCTION search_similar_chunks(
    query_embedding vector(1536),
    similarity_threshold float DEFAULT 0.7,
    max_results int DEFAULT 10,
    document_ids uuid[] DEFAULT NULL
)
RETURNS TABLE (
    chunk_id uuid,
    document_id uuid,
    content text,
    similarity float,
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as chunk_id,
        c.document_id,
        c.content,
        1 - (c.embedding <=> query_embedding) as similarity,
        c.metadata
    FROM chunks c
    WHERE 
        c.embedding IS NOT NULL
        AND (document_ids IS NULL OR c.document_id = ANY(document_ids))
        AND (1 - (c.embedding <=> query_embedding)) >= similarity_threshold
    ORDER BY c.embedding <=> query_embedding
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Create function for full-text search
CREATE OR REPLACE FUNCTION search_chunks_fulltext(
    search_query text,
    max_results int DEFAULT 10,
    document_ids uuid[] DEFAULT NULL
)
RETURNS TABLE (
    chunk_id uuid,
    document_id uuid,
    content text,
    rank float,
    metadata jsonb
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.id as chunk_id,
        c.document_id,
        c.content,
        ts_rank(to_tsvector('english', c.content), plainto_tsquery('english', search_query)) as rank,
        c.metadata
    FROM chunks c
    WHERE 
        to_tsvector('english', c.content) @@ plainto_tsquery('english', search_query)
        AND (document_ids IS NULL OR c.document_id = ANY(document_ids))
    ORDER BY rank DESC
    LIMIT max_results;
END;
$$ LANGUAGE plpgsql;

-- Create full-text search index
CREATE INDEX IF NOT EXISTS idx_chunks_content_fts ON chunks 
USING gin(to_tsvector('english', content));

-- Create RLS policies (Row Level Security)
ALTER TABLE documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE chunks ENABLE ROW LEVEL SECURITY;
ALTER TABLE crawl_sessions ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_queries ENABLE ROW LEVEL SECURITY;

-- Allow service role to access all data
CREATE POLICY "Service role can access all documents" ON documents
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access all chunks" ON chunks
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access all crawl sessions" ON crawl_sessions
    FOR ALL USING (auth.role() = 'service_role');

CREATE POLICY "Service role can access all search queries" ON search_queries
    FOR ALL USING (auth.role() = 'service_role');

-- Create views for analytics
CREATE OR REPLACE VIEW document_stats AS
SELECT 
    document_type,
    status,
    COUNT(*) as count,
    AVG(chunk_count) as avg_chunks,
    AVG(EXTRACT(EPOCH FROM (updated_at - created_at))) as avg_processing_time_seconds
FROM documents
GROUP BY document_type, status;

CREATE OR REPLACE VIEW crawl_session_stats AS
SELECT 
    status,
    strategy,
    COUNT(*) as session_count,
    AVG(COALESCE(array_length(urls_crawled, 1), 0)) as avg_pages_crawled,
    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_duration_seconds
FROM crawl_sessions
GROUP BY status, strategy;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL FUNCTIONS IN SCHEMA public TO anon, authenticated;
