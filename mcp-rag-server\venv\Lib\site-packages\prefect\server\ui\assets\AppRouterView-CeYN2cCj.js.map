{"version": 3, "file": "AppRouterView-CeYN2cCj.js", "sources": ["../../src/components/JoinTheCommunityModal.vue", "../../src/components/ContextSidebar.vue", "../../src/compositions/useApiConfig.ts", "../../src/compositions/useCreateCan.ts", "../../src/compositions/useMobileMenuOpen.ts", "../../src/pages/AppRouterView.vue"], "sourcesContent": ["<template>\n  <p-modal v-model:show-modal=\"showModal\" title=\"Join the Prefect Community\">\n    <template #header>\n      <h2>Join the Community</h2>\n    </template>\n\n    <template #default>\n      <p>\n        Connect with 25k+ engineers scaling Python with Prefect. Show us your work and be the first to know about new Prefect features.\n      </p>\n\n      <div class=\"flex gap-x-2 items-center\">\n        <p-button primary icon=\"Slack\" :to=\"joinSlackUrl\" target=\"_blank\" @click=\"showJoinSlackThankYouMessage = true\">\n          Join us on Slack\n        </p-button>\n\n        <span v-if=\"showJoinSlackThankYouMessage\" class=\"text-sm italic\">\n          Thanks for joining our community!\n        </span>\n      </div>\n\n      <p-divider class=\"-my-3\" />\n\n      <p-form :id=\"formId\" @submit=\"signUpForEmailUpdates\">\n        <p-label v-slot=\"{ id }\" label=\"Notify me about Prefect updates\" :state :message=\"state.error\">\n          <p-text-input\n            :id\n            v-model=\"email\"\n            placeholder=\"<EMAIL>\"\n            :state\n          />\n        </p-label>\n      </p-form>\n\n      <p-message v-if=\"error\" error>\n        {{ error }}\n      </p-message>\n    </template>\n\n    <template #cancel=\"scope\">\n      <p-button class=\"sm:order-first\" @click=\"scope.close\">\n        Skip\n      </p-button>\n    </template>\n\n    <template #actions>\n      <p-button primary type=\"submit\" :form=\"formId\" :loading>\n        Sign up\n      </p-button>\n    </template>\n  </p-modal>\n</template>\n\n<script setup lang=\"ts\">\n  import { showToast } from '@prefecthq/prefect-design'\n  import { isEmail, isRequired } from '@prefecthq/prefect-ui-library'\n  import { useValidation } from '@prefecthq/vue-compositions'\n  import { ref } from 'vue'\n\n  const showModal = defineModel<boolean>('showModal')\n\n  const joinSlackUrl = 'http://prefect.io/slack?utm_source=oss&utm_medium=oss&utm_campaign=oss_popup&utm_term=none&utm_content=none'\n  const showJoinSlackThankYouMessage = ref(false)\n\n  const formId = 'join-the-community-modal'\n  const email = ref<string>()\n  const { validate, state } = useValidation(email, [isRequired('Email'), isEmail('Email')])\n\n  const loading = ref(false)\n  const error = ref('')\n\n  const formEndpoint = 'https://getform.io/f/eapderva'\n  async function signUpForEmailUpdates(): Promise<void> {\n    if (!await validate()) {\n      return\n    }\n\n    error.value = ''\n    loading.value = true\n    try {\n      await fetch(formEndpoint, {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({ email: email.value }),\n        // getform redirects to a thank-you page so this cancels that additional request\n        redirect: 'manual',\n      })\n\n      showModal.value = false\n      showToast('Successfully subscribed', 'success')\n    } catch (err) {\n      error.value = 'An error occurred. Please try again.'\n      console.error(err)\n    } finally {\n      loading.value = false\n    }\n  }\n</script>", "<template>\n  <p-context-sidebar class=\"context-sidebar\">\n    <template #header>\n      <router-link :to=\"routes.root()\" class=\"context-sidebar__logo-link\">\n        <p-icon icon=\"Prefect\" class=\"context-sidebar__logo-icon\" />\n      </router-link>\n    </template>\n    <p-context-nav-item title=\"Dashboard\" :to=\"routes.dashboard()\" />\n    <p-context-nav-item title=\"Runs\" :to=\"routes.runs()\" />\n    <p-context-nav-item title=\"Flows\" :to=\"routes.flows()\" />\n    <p-context-nav-item title=\"Deployments\" :to=\"routes.deployments()\" />\n    <p-context-nav-item v-if=\"canSeeWorkPools\" title=\"Work Pools\" :to=\"routes.workPools()\" />\n    <p-context-nav-item v-if=\"!canSeeWorkPools\" title=\"Work Queues\" :to=\"routes.workQueues()\" />\n    <p-context-nav-item title=\"Blocks\" :to=\"routes.blocks()\" />\n    <p-context-nav-item :title=\"localization.info.variables\" :to=\"routes.variables()\" />\n    <p-context-nav-item title=\"Automations\" :to=\"routes.automations()\" />\n    <p-context-nav-item title=\"Event Feed\" :to=\"routes.events()\" />\n    <p-context-nav-item title=\"Concurrency\" :to=\"routes.concurrencyLimits()\" />\n\n    <template #footer>\n      <a href=\"https://www.prefect.io/cloud-vs-oss?utm_source=oss&utm_medium=oss&utm_campaign=oss&utm_term=none&utm_content=none\" target=\"_blank\">\n        <p-context-nav-item>\n          <div>\n            Ready to scale?\n          </div>\n          <p-button primary small class=\"context-sidebar__upgade-button\">\n            Upgrade\n          </p-button>\n        </p-context-nav-item>\n      </a>\n\n      <p-context-nav-item @click=\"openJoinCommunityModal\">\n        Join the Community\n        <JoinTheCommunityModal :show-modal=\"showJoinCommunityModal || !joinTheCommunityModalDismissed\" @update:show-modal=\"updateShowModal\" />\n      </p-context-nav-item>\n\n      <p-context-nav-item title=\"Settings\" :to=\"routes.settings()\" />\n    </template>\n  </p-context-sidebar>\n</template>\n\n<script lang=\"ts\" setup>\n  import JoinTheCommunityModal from '@/components/JoinTheCommunityModal.vue'\n  import { useCan } from '@/compositions/useCan'\n  import { routes } from '@/router'\n  import { PContextNavItem, PContextSidebar } from '@prefecthq/prefect-design'\n  import { localization, useShowModal } from '@prefecthq/prefect-ui-library'\n  import { useStorage } from '@prefecthq/vue-compositions'\n  import { computed } from 'vue'\n\n  const can = useCan()\n  const canSeeWorkPools = computed(() => can.read.work_pool)\n\n  const { showModal: showJoinCommunityModal, open: openJoinCommunityModal } = useShowModal()\n  const { value: joinTheCommunityModalDismissed } = useStorage('local', 'join-the-community-modal-dismissed', false)\n  function updateShowModal(updatedShowModal: boolean): void {\n    showJoinCommunityModal.value = updatedShowModal\n    if (!updatedShowModal) {\n      joinTheCommunityModalDismissed.value = true\n    }\n  }\n</script>\n\n<style>\n.context-sidebar__logo-link { @apply\n  outline-none\n  rounded-md\n  focus:ring-spacing-focus-ring\n  focus:ring-focus-ring\n}\n\n.context-sidebar__logo-link:focus:not(:focus-visible) { @apply\n  ring-transparent\n}\n\n.context-sidebar__logo-icon { @apply\n  !w-[42px]\n  !h-[42px]\n}\n\n.context-sidebar__upgade-button { @apply\n  ml-auto\n}\n</style>", "import { PrefectConfig } from '@prefecthq/prefect-ui-library'\nimport { UiSettings } from '@/services/uiSettings'\nimport { MODE } from '@/utilities/meta'\n\nexport type UseWorkspaceApiConfig = {\n  config: PrefectConfig,\n}\nexport async function useApiConfig(): Promise<UseWorkspaceApiConfig> {\n  const baseUrl = await UiSettings.get('apiUrl')\n  const config: PrefectConfig = { baseUrl }\n\n  if (baseUrl.startsWith('/') && MODE() === 'development') {\n    config.baseUrl = `http://127.0.0.1:4200${baseUrl}`\n  }\n\n  return { config }\n}", "import { Can, createCan, workspacePermissions } from '@prefecthq/prefect-ui-library'\nimport { useSubscription } from '@prefecthq/vue-compositions'\nimport { computed, Ref } from 'vue'\nimport { uiSettings } from '@/services/uiSettings'\nimport { Permission } from '@/utilities/permissions'\n\ntype UseCreateCan = {\n  can: Can<Permission>,\n  pending: Ref<boolean>,\n}\n\nexport function useCreateCan(): UseCreateCan {\n  const flagsSubscription = useSubscription(uiSettings.getFeatureFlags, [])\n\n  const permissions = computed<Permission[]>(() => [\n    ...workspacePermissions,\n    ...flagsSubscription.response ?? [],\n  ])\n\n  const can = createCan(permissions)\n  const pending = computed(() => flagsSubscription.loading)\n\n  return {\n    can,\n    pending,\n  }\n}", "import { Ref, ref } from 'vue'\n\nexport type UseMobileMenuOpen = {\n  mobileMenuOpen: Ref<boolean>,\n  open: () => void,\n  close: () => void,\n  toggle: () => void,\n}\n\nexport function useMobileMenuOpen(): UseMobileMenuOpen {\n  const mobileMenuOpen = ref(false)\n\n  function toggle(): void {\n    mobileMenuOpen.value = !mobileMenuOpen.value\n  }\n\n  function open(): void {\n    mobileMenuOpen.value = true\n  }\n\n  function close(): void {\n    mobileMenuOpen.value = false\n  }\n\n  return {\n    mobileMenuOpen,\n    open,\n    close,\n    toggle,\n  }\n}", "<template>\n  <div class=\"app-router-view\">\n    <template v-if=\"!media.lg && !$route.meta.public\">\n      <PGlobalSidebar class=\"app-router-view__mobile-menu\">\n        <template #upper-links>\n          <router-link :to=\"appRoutes.root()\">\n            <p-icon icon=\"Prefect\" class=\"app-router-view__prefect-icon\" />\n          </router-link>\n        </template>\n        <template #bottom-links>\n          <p-button small icon=\"Bars3Icon\" class=\"app-router-view__menu-icon\" @click=\"toggle\" />\n        </template>\n      </PGlobalSidebar>\n    </template>\n    <ContextSidebar v-if=\"showMenu && !$route.meta.public\" class=\"app-router-view__sidebar\" @click=\"close\" />\n    <router-view :class=\"['app-router-view__view', { 'app-router-view__view--public': $route.meta.public }]\">\n      <template #default=\"{ Component }\">\n        <transition name=\"app-router-view-fade\" mode=\"out-in\">\n          <component :is=\"Component\" />\n        </transition>\n      </template>\n    </router-view>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PGlobalSidebar, PIcon, media, showToast } from '@prefecthq/prefect-design'\n  import { workspaceApiKey, canKey as designCanKey, createWorkspaceRoutes, workspaceRoutesKey } from '@prefecthq/prefect-ui-library'\n  import { computed, provide, watchEffect } from 'vue'\n  import { RouterView } from 'vue-router'\n  import ContextSidebar from '@/components/ContextSidebar.vue'\n  import { useApiConfig } from '@/compositions/useApiConfig'\n  import { useCreateCan } from '@/compositions/useCreateCan'\n  import { useMobileMenuOpen } from '@/compositions/useMobileMenuOpen'\n  import router, { routes as appRoutes } from '@/router'\n  import { createPrefectApi, prefectApiKey } from '@/utilities/api'\n  import { canKey } from '@/utilities/permissions'\n  import { UiSettings } from '@/services/uiSettings'\n\n  const { can } = useCreateCan()\n  const { config } = await useApiConfig()\n  const api = createPrefectApi(config)\n  const routes = createWorkspaceRoutes()\n\n  provide(canKey, can)\n  provide(designCanKey, can)\n  provide(prefectApiKey, api)\n  provide(workspaceApiKey, api)\n  provide(workspaceRoutesKey, routes)\n\n\n  api.admin.authCheck().then(status_code => {\n    if (status_code == 401) {\n      if (router.currentRoute.value.name !== 'login') {\n        showToast('Authentication failed.', 'error', { timeout: false })\n        router.push({\n          name: 'login', \n          query: { redirect: router.currentRoute.value.fullPath }\n        })\n      }\n    } else {\n      api.health.isHealthy().then(healthy => {\n        if (!healthy) {\n          showToast(`Can't connect to Server API at ${config.baseUrl}. Check that it's accessible from your machine.`, 'error', { timeout: false })\n        }\n      })\n    }\n  })\n\n  const { mobileMenuOpen, toggle, close } = useMobileMenuOpen()\n  const showMenu = computed(() => media.lg || mobileMenuOpen.value)\n\n  watchEffect(() => document.body.classList.toggle('body-scrolling-disabled', showMenu.value && !media.lg))\n</script>\n\n<style>\n.body-scrolling-disabled { @apply\n  overflow-hidden\n}\n\n.app-router-view { @apply\n  flex\n  flex-col\n  bg-no-repeat\n  overflow-auto;\n  --prefect-scroll-margin: theme('spacing.20');\n  height: 100vh;\n  background-image: url('/decorative_iso-pixel-grid_light.svg');\n  background-attachment: fixed;\n  background-position: bottom -140px left -140px;\n}\n\n.dark .app-router-view {\n  background-image: url('/decorative_iso-pixel-grid_dark.svg');\n}\n\n.app-router-view__prefect-icon { @apply\n  w-7\n  h-7\n}\n\n.app-router-view__mobile-menu { @apply\n  h-auto\n  py-3\n}\n\n.app-router-view__sidebar { @apply\n  bg-floating\n  top-[54px]\n  lg:bg-transparent\n  lg:top-0\n}\n\n.app-router-view__sidebar .p-context-sidebar__header { @apply\n  hidden\n  lg:block\n}\n\n.app-router-view__view {\n  /* The 1px flex-basis is important because it allows us to use height: 100% without additional flexing */\n  flex: 1 0 1px;\n  height: 100%;\n}\n\n.app-router-view__view--public { @apply\n  flex\n  items-center\n  justify-center;\n  grid-column: 1 / -1;\n}\n\n@screen lg {\n  .app-router-view {\n    --prefect-scroll-margin: theme('spacing.2');\n    display: grid;\n    grid-template-columns: max-content minmax(0, 1fr);\n  }\n}\n\n.app-router-view-fade-enter-active,\n.app-router-view-fade-leave-active {\n  transition: opacity 0.25s ease;\n}\n\n.app-router-view-fade-enter-from,\n.app-router-view-fade-leave-to {\n  opacity: 0;\n}\n</style>"], "names": ["joinSlackUrl", "formId", "formEndpoint", "showModal", "_useModel", "__props", "showJoinSlackThankYouMessage", "ref", "email", "validate", "state", "useValidation", "isRequired", "isEmail", "loading", "error", "signUpForEmailUpdates", "showToast", "err", "_createBlock", "_component_p_modal", "$event", "_cache", "_createElementVNode", "_hoisted_1", "_createVNode", "_component_p_button", "_createElementBlock", "_hoisted_2", "_component_p_divider", "_component_p_form", "_component_p_label", "_unref", "_withCtx", "id", "_component_p_text_input", "_component_p_message", "scope", "can", "useCan", "canSeeWorkPools", "computed", "showJoinCommunityModal", "openJoinCommunityModal", "useShowModal", "joinTheCommunityModalDismissed", "useStorage", "updateShowModal", "updatedShowModal", "PContextSidebar", "_component_router_link", "routes", "_component_p_icon", "PContextNavItem", "JoinTheCommunityModal", "localization", "useApiConfig", "baseUrl", "UiSettings", "config", "MODE", "useCreateCan", "flagsSubscription", "useSubscription", "uiSettings", "permissions", "workspacePermissions", "createCan", "pending", "useMobileMenuOpen", "mobileMenuOpen", "toggle", "open", "close", "__temp", "__restore", "_withAsyncContext", "api", "createPrefectApi", "createWorkspaceRoutes", "provide", "can<PERSON>ey", "designCanKey", "prefectApiKey", "workspaceApiKey", "workspaceRoutesKey", "status_code", "router", "healthy", "showMenu", "media", "watchEffect", "_openBlock", "$route", "PGlobalSidebar", "appRoutes", "PIcon", "ContextSidebar", "RouterView", "Component", "_Transition", "_resolveDynamicComponent"], "mappings": "qlBA6DQA,GAAe,8GAGfC,EAAS,2BAOTC,GAAe,gKAZf,MAAAC,EAAYC,EAAqBC,EAAA,WAAW,EAG5CC,EAA+BC,EAAI,EAAK,EAGxCC,EAAQD,EAAY,EACpB,CAAE,SAAAE,EAAU,MAAAC,CAAM,EAAIC,EAAcH,EAAO,CAACI,EAAW,OAAO,EAAGC,EAAQ,OAAO,CAAC,CAAC,EAElFC,EAAUP,EAAI,EAAK,EACnBQ,EAAQR,EAAI,EAAE,EAGpB,eAAeS,GAAuC,CAChD,GAAC,MAAMP,IAIX,CAAAM,EAAM,MAAQ,GACdD,EAAQ,MAAQ,GACZ,GAAA,CACF,MAAM,MAAMZ,GAAc,CACxB,OAAQ,OACR,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,UAAU,CAAE,MAAOM,EAAM,MAAO,EAE3C,SAAU,QAAA,CACX,EAEDL,EAAU,MAAQ,GAClBc,EAAU,0BAA2B,SAAS,QACvCC,EAAK,CACZH,EAAM,MAAQ,uCACd,QAAQ,MAAMG,CAAG,CAAA,QACjB,CACAJ,EAAQ,MAAQ,EAAA,EAClB,kJAhGFK,EAiDUC,EAAA,CAjDO,aAAYjB,EAAS,0CAATA,EAAS,MAAAkB,GAAE,MAAM,4BAAA,GACjC,SACT,IAA2BC,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,CAA3BC,EAA2B,UAAvB,qBAAkB,EAAA,CAAA,IAGb,UACT,IAEI,CAFJD,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,EAEI,SAFD,oIAEH,EAAA,GAEAA,EAQM,MARNC,GAQM,CAPJC,EAEWC,EAAA,CAFD,QAAA,GAAQ,KAAK,QAAS,GAAI1B,GAAc,OAAO,SAAU,uBAAOM,EAA4B,MAAA,GAAA,aAAS,IAE/GgB,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAF+G,oBAE/G,CAAA,iBAEYhB,EAA4B,SAAxC,EAAAqB,EAEO,OAFPC,GAAiE,qCAEjE,cAGFH,EAA2BI,EAAA,CAAhB,MAAM,QAAO,EAExBJ,EASSK,EAAA,CATA,GAAI7B,EAAS,SAAQe,CAAA,aAC5B,IAOU,CAPVS,EAOUM,EAAA,CAPe,MAAM,kCAAmC,MAAAC,EAAKtB,CAAA,EAAE,QAASsB,EAAKtB,CAAA,EAAC,KAAA,GACtF,QAAAuB,EAAA,CAKE,CANe,GAAAC,KAAE,CACnBT,EAKEU,EAAA,CAJC,GAAAD,aACQ1B,EAAK,2CAALA,EAAK,MAAAa,GACd,YAAY,mBACX,MAAAW,EAAKtB,CAAA,6EAKKK,EAAK,WAAtBI,EAEYiB,EAAA,OAFY,MAAA,EAAA,aACtB,IAAW,KAARrB,EAAK,KAAA,EAAA,CAAA,CAAA,qBAID,OAAMkB,EAGJI,GAHW,CACtBZ,EAEWC,EAAA,CAFD,MAAM,iBAAkB,QAAOW,EAAM,KAAA,aAAO,IAEtDf,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFsD,QAEtD,CAAA,oCAGS,UACT,IAEW,CAFXG,EAEWC,EAAA,CAFD,QAAA,GAAQ,KAAK,SAAU,KAAMzB,EAAS,QAAAa,EAAO,KAAA,aAAC,IAExDQ,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAFwD,WAExD,CAAA,iPCEJ,MAAMgB,EAAMC,GAAO,EACbC,EAAkBC,EAAS,IAAMH,EAAI,KAAK,SAAS,EAEnD,CAAE,UAAWI,EAAwB,KAAMC,CAAA,EAA2BC,EAAa,EACnF,CAAE,MAAOC,GAAmCC,EAAW,QAAS,qCAAsC,EAAK,EACjH,SAASC,EAAgBC,EAAiC,CACxDN,EAAuB,MAAQM,EAC1BA,IACHH,EAA+B,MAAQ,GACzC,iFA1DF1B,EAqCoBa,EAAAiB,CAAA,EAAA,CArCD,MAAM,mBAAiB,CAC7B,SACT,IAEc,CAFdxB,EAEcyB,EAAA,CAFA,GAAIlB,EAAMmB,CAAA,EAAC,KAAI,EAAI,MAAM,4BAAA,aACrC,IAA4D,CAA5D1B,EAA4D2B,EAAA,CAApD,KAAK,UAAU,MAAM,kDAetB,SACT,IASI,CATJ7B,EASI,IATJC,GASI,CARFC,EAOqBO,EAAAqB,CAAA,EAAA,KAAA,WANnB,IAEM,CAFN/B,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAAC,EAEM,WAFD,oBAEL,EAAA,GACAE,EAEWC,EAAA,CAFD,QAAA,GAAQ,MAAA,GAAM,MAAM,gCAAA,aAAiC,IAE/DJ,EAAA,CAAA,IAAAA,EAAA,CAAA,EAAA,GAF+D,WAE/D,CAAA,kCAIJG,EAGqBO,EAAAqB,CAAA,EAAA,CAHA,QAAOrB,EAAsBW,CAAA,GAAA,WAAE,IAElD,eAFkD,sBAElD,GAAAlB,EAAsI6B,GAAA,CAA9G,aAAYtB,EAAsBU,CAAA,GAAA,CAAKV,EAA8Ba,CAAA,EAAG,qBAAmBE,CAAA,sDAGrHtB,EAA+DO,EAAAqB,CAAA,EAAA,CAA3C,MAAM,WAAY,GAAIrB,EAAMmB,CAAA,EAAC,SAAQ,CAAA,6BA7B3D,IAAiE,CAAjE1B,EAAiEO,EAAAqB,CAAA,EAAA,CAA7C,MAAM,YAAa,GAAIrB,EAAMmB,CAAA,EAAC,UAAS,kBAC3D1B,EAAuDO,EAAAqB,CAAA,EAAA,CAAnC,MAAM,OAAQ,GAAIrB,EAAMmB,CAAA,EAAC,KAAI,kBACjD1B,EAAyDO,EAAAqB,CAAA,EAAA,CAArC,MAAM,QAAS,GAAIrB,EAAMmB,CAAA,EAAC,MAAK,kBACnD1B,EAAqEO,EAAAqB,CAAA,EAAA,CAAjD,MAAM,cAAe,GAAIrB,EAAMmB,CAAA,EAAC,YAAW,kBACrCX,EAAe,WAAzCrB,EAAyFa,EAAAqB,CAAA,EAAA,OAA9C,MAAM,aAAc,GAAIrB,EAAMmB,CAAA,EAAC,UAAS,CAAA,2BACxDX,EAAe,oBAA1CrB,EAA4Fa,EAAAqB,CAAA,EAAA,OAAhD,MAAM,cAAe,GAAIrB,EAAMmB,CAAA,EAAC,WAAU,CAAA,kBACtF1B,EAA2DO,EAAAqB,CAAA,EAAA,CAAvC,MAAM,SAAU,GAAIrB,EAAMmB,CAAA,EAAC,OAAM,kBACrD1B,EAAoFO,EAAAqB,CAAA,EAAA,CAA/D,MAAOrB,EAAAuB,CAAA,EAAa,KAAK,UAAY,GAAIvB,EAAMmB,CAAA,EAAC,UAAS,0BAC9E1B,EAAqEO,EAAAqB,CAAA,EAAA,CAAjD,MAAM,cAAe,GAAIrB,EAAMmB,CAAA,EAAC,YAAW,kBAC/D1B,EAA+DO,EAAAqB,CAAA,EAAA,CAA3C,MAAM,aAAc,GAAIrB,EAAMmB,CAAA,EAAC,OAAM,kBACzD1B,EAA2EO,EAAAqB,CAAA,EAAA,CAAvD,MAAM,cAAe,GAAIrB,EAAMmB,CAAA,EAAC,kBAAiB,CAAA,6BCVzE,eAAsBK,IAA+C,CACnE,MAAMC,EAAU,MAAMC,GAAW,IAAI,QAAQ,EACvCC,EAAwB,CAAE,QAAAF,CAAQ,EAExC,OAAIA,EAAQ,WAAW,GAAG,GAAKG,EAAA,EAIxB,CAAE,OAAAD,CAAO,CAClB,CCLO,SAASE,IAA6B,CAC3C,MAAMC,EAAoBC,EAAgBC,GAAW,gBAAiB,CAAA,CAAE,EAElEC,EAAcxB,EAAuB,IAAM,CAC/C,GAAGyB,EACH,GAAGJ,EAAkB,UAAY,CAAA,CAAC,CACnC,EAEKxB,EAAM6B,EAAUF,CAAW,EAC3BG,EAAU3B,EAAS,IAAMqB,EAAkB,OAAO,EAEjD,MAAA,CACL,IAAAxB,EACA,QAAA8B,CACF,CACF,CCjBO,SAASC,IAAuC,CAC/C,MAAAC,EAAiB/D,EAAI,EAAK,EAEhC,SAASgE,GAAe,CACPD,EAAA,MAAQ,CAACA,EAAe,KAAA,CAGzC,SAASE,GAAa,CACpBF,EAAe,MAAQ,EAAA,CAGzB,SAASG,GAAc,CACrBH,EAAe,MAAQ,EAAA,CAGlB,MAAA,CACL,eAAAA,EACA,KAAAE,EACA,MAAAC,EACA,OAAAF,CACF,CACF,wFCSQ,KAAA,CAAE,IAAAjC,CAAI,EAAIuB,GAAa,EACvB,CAAE,OAAAF,CAAO,GAAI,CAAAe,EAAAC,CAAA,EAAAC,EAAA,IAAMpB,GAAa,CAAA,mBAChCqB,EAAMC,GAAiBnB,CAAM,EAC7BR,EAAS4B,GAAsB,EAErCC,EAAQC,GAAQ3C,CAAG,EACnB0C,EAAQE,GAAc5C,CAAG,EACzB0C,EAAQG,GAAeN,CAAG,EAC1BG,EAAQI,GAAiBP,CAAG,EAC5BG,EAAQK,GAAoBlC,CAAM,EAGlC0B,EAAI,MAAM,UAAY,EAAA,KAAoBS,GAAA,CACpCA,GAAe,IACbC,EAAO,aAAa,MAAM,OAAS,UACrCtE,EAAU,yBAA0B,QAAS,CAAE,QAAS,GAAO,EAC/DsE,EAAO,KAAK,CACV,KAAM,QACN,MAAO,CAAE,SAAUA,EAAO,aAAa,MAAM,QAAS,CAAA,CACvD,GAGHV,EAAI,OAAO,UAAY,EAAA,KAAgBW,GAAA,CAChCA,GACOvE,EAAA,kCAAkC0C,EAAO,OAAO,kDAAmD,QAAS,CAAE,QAAS,GAAO,CAC1I,CACD,CACH,CACD,EAED,KAAM,CAAE,eAAAW,EAAgB,OAAAC,EAAQ,MAAAE,CAAA,EAAUJ,GAAkB,EACtDoB,EAAWhD,EAAS,IAAMiD,EAAM,IAAMpB,EAAe,KAAK,EAEpD,OAAAqB,GAAA,IAAM,SAAS,KAAK,UAAU,OAAO,0BAA2BF,EAAS,OAAS,CAACC,EAAM,EAAE,CAAC,mDAvExG,OAAAE,EAAA,EAAAjE,EAqBM,MArBNH,GAqBM,CApBa,CAAAQ,EAAA0D,CAAA,EAAM,IAAE,CAAKG,SAAO,KAAK,YACxC1E,EASiBa,EAAA8D,EAAA,EAAA,OATD,MAAM,8BAAA,GACT,gBACT,IAEc,CAFdrE,EAEcyB,EAAA,CAFA,GAAIlB,EAAS+D,CAAA,EAAC,KAAI,CAAA,aAC9B,IAA+D,CAA/DtE,EAA+DO,EAAAgE,EAAA,EAAA,CAAvD,KAAK,UAAU,MAAM,qDAGtB,iBACT,IAAsF,CAAtFvE,EAAsFC,EAAA,CAA5E,MAAA,GAAM,KAAK,YAAY,MAAM,6BAA8B,QAAOM,EAAMuC,CAAA,CAAA,wCAIlEkB,EAAA,OAAaI,CAAAA,EAAAA,OAAO,KAAK,YAA/C1E,EAAyG8E,GAAA,OAAlD,MAAM,2BAA4B,QAAOjE,EAAKyC,CAAA,CAAA,gCACrGhD,EAMcO,EAAAkE,EAAA,EAAA,CANA,MAAoEL,GAAAA,CAAAA,wBAAAA,CAAAA,gCAAAA,EAAAA,OAAO,KAAK,OAAM,CAAA,CAAA,GACvF,QAAO5D,EAChB,CAEa,CAHO,UAAAkE,KAAS,CAC7B1E,EAEa2E,GAAA,CAFD,KAAK,uBAAuB,KAAK,QAAA,aAC3C,IAA6B,EAA7BR,IAAAzE,EAA6BkF,GAAbF,CAAS,CAAA,EAAA"}