import{d as v,g as h,q as u,o as i,k as e,j as t,F as C,v as x,t as f,n,du as w,K as B,i as l,c as P,dv as V,a6 as j,dw as p,z as q}from"./index-ei-kaitd.js";import{u as A}from"./usePageTitle-LeBMnqrg.js";import{u as F}from"./usePrefectApi-qsKG6mzx.js";import"./api-DGOAIix_.js";import"./mapper-BuxGYc8V.js";const M={class:"settings-block"},T=v({__name:"SettingsCodeBlock",props:{engineSettings:{}},setup(d){const s=d,o=h(()=>Object.entries(s.engineSettings));return(g,c)=>(i(),u("div",M,[e(n(w),{multiline:""},{default:t(()=>[(i(!0),u(C,null,x(o.value,(a,r)=>(i(),u("div",{key:r,class:"settings-block--code-line"},f(a[0])+": "+f(a[1]),1))),128))]),_:1})]))}}),N=v({__name:"Settings",async setup(d){let s,o;const g=[{text:"Settings"}],c=F(),[a,r]=([s,o]=B(()=>Promise.all([c.admin.getSettings(),c.admin.getVersion()])),s=await s,o(),s);return A("Settings"),(U,m)=>{const S=l("p-key-value"),b=l("p-theme-toggle"),_=l("p-label"),k=l("p-layout-default");return i(),P(k,{class:"settings"},{header:t(()=>[e(n(q),{crumbs:g},{actions:t(()=>[e(S,{class:"settings__version",label:"Version",value:n(r),alternate:""},null,8,["value"])]),_:1})]),default:t(()=>[e(_,{label:"Theme"},{default:t(()=>[e(b)]),_:1}),e(_,{label:"Color Mode",class:"settings__color-mode"},{default:t(()=>[e(n(V),{selected:n(p),"onUpdate:selected":m[0]||(m[0]=y=>j(p)?p.value=y:null)},null,8,["selected"])]),_:1}),e(_,{label:"Server Settings"},{default:t(()=>[e(T,{class:"settings__code-block","engine-settings":n(a)},null,8,["engine-settings"])]),_:1})]),_:1})}}});export{N as default};
//# sourceMappingURL=Settings-D_1Fo85R.js.map
