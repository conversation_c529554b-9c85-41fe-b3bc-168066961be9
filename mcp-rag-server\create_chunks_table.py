#!/usr/bin/env python3
"""Create chunks table in Supabase."""

import asyncio
from config.settings import get_settings


async def create_chunks_table():
    """Create chunks table in Supabase."""
    print('🔧 Creating chunks table in Supabase')
    print('=' * 40)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Create chunks table
        create_table_sql = """
        CREATE TABLE IF NOT EXISTS chunks (
            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
            document_id UUID NOT NULL,
            content TEXT NOT NULL,
            content_length INTEGER NOT NULL,
            chunk_index INTEGER NOT NULL,
            start_position INTEGER,
            end_position INTEGER,
            embedding TEXT,
            embedding_model TEXT,
            metadata JSONB DEFAULT '{}',
            created_at TIMESTAMPTZ DEFAULT NOW(),
            updated_at TIMESTAMPTZ DEFAULT NOW(),
            relevance_score FLOAT,
            readability_score FLOAT
        );
        """
        
        print('📋 Creating chunks table...')
        result = client.rpc('exec_sql', {'sql': create_table_sql}).execute()
        print('✅ Chunks table created successfully!')
        
        # Create indexes for performance
        indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_chunks_document_id ON chunks(document_id);",
            "CREATE INDEX IF NOT EXISTS idx_chunks_embedding_model ON chunks(embedding_model);",
            "CREATE INDEX IF NOT EXISTS idx_chunks_created_at ON chunks(created_at);",
        ]
        
        print('📋 Creating indexes...')
        for idx_sql in indexes_sql:
            try:
                client.rpc('exec_sql', {'sql': idx_sql}).execute()
                print(f'   ✅ Index created')
            except Exception as e:
                print(f'   ⚠️  Index creation warning: {e}')
        
        # Test table
        print('📋 Testing chunks table...')
        test_result = client.table('chunks').select('id').limit(1).execute()
        print('✅ Chunks table is accessible!')
        
        return True
        
    except Exception as e:
        print(f'❌ Error creating chunks table: {e}')
        
        # Try alternative approach - direct SQL execution
        print('\n🔧 Trying alternative approach...')
        try:
            # Simple table creation
            simple_sql = """
            CREATE TABLE chunks (
                id TEXT PRIMARY KEY,
                document_id TEXT NOT NULL,
                content TEXT NOT NULL,
                content_length INTEGER NOT NULL,
                chunk_index INTEGER NOT NULL,
                start_position INTEGER,
                end_position INTEGER,
                embedding TEXT,
                embedding_model TEXT,
                metadata TEXT DEFAULT '{}',
                created_at TEXT,
                updated_at TEXT,
                relevance_score REAL,
                readability_score REAL
            );
            """
            
            # This is a fallback - we'll create a simple version
            print('📋 Creating simple chunks table...')
            
            # We'll use the client's table creation if possible
            return False
            
        except Exception as e2:
            print(f'❌ Alternative approach failed: {e2}')
            return False


async def test_chunks_table():
    """Test if chunks table works."""
    print('\n🧪 Testing chunks table functionality')
    print('=' * 40)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Test insert
        print('📋 Testing chunk insertion...')
        test_chunk = {
            'id': 'test-chunk-1',
            'document_id': 'test-doc-1',
            'content': 'This is a test chunk for testing the chunks table functionality.',
            'content_length': 64,
            'chunk_index': 0,
            'start_position': 0,
            'end_position': 64,
            'embedding_model': 'test-model',
            'metadata': '{"test": true}',
            'created_at': '2025-07-11T20:00:00Z',
            'updated_at': '2025-07-11T20:00:00Z'
        }
        
        insert_result = client.table('chunks').insert(test_chunk).execute()
        print('✅ Test chunk inserted successfully!')
        
        # Test select
        print('📋 Testing chunk selection...')
        select_result = client.table('chunks').select('*').eq('id', 'test-chunk-1').execute()
        
        if select_result.data:
            print('✅ Test chunk retrieved successfully!')
            print(f'   Content: {select_result.data[0]["content"][:50]}...')
        else:
            print('❌ Test chunk not found!')
        
        # Clean up test data
        print('📋 Cleaning up test data...')
        client.table('chunks').delete().eq('id', 'test-chunk-1').execute()
        print('✅ Test data cleaned up!')
        
        return True
        
    except Exception as e:
        print(f'❌ Error testing chunks table: {e}')
        return False


async def main():
    """Main function."""
    print('🔧 CHUNKS TABLE CREATION')
    print('=' * 50)
    
    # Create table
    created = await create_chunks_table()
    
    if created:
        # Test table
        tested = await test_chunks_table()
        
        if tested:
            print('\n🎉 SUCCESS!')
            print('✅ Chunks table created and tested successfully!')
            print('🚀 Ready to process documents into chunks!')
        else:
            print('\n⚠️  Table created but testing failed')
    else:
        print('\n❌ Failed to create chunks table')
        print('💡 Manual intervention may be required')


if __name__ == "__main__":
    asyncio.run(main())
