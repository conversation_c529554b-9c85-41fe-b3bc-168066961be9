import{d as l,W as n,i as s,c as u,o as p,j as a,k as t,n as o,d8 as m,d9 as c}from"./index-ei-kaitd.js";import{u as _}from"./usePageTitle-LeBMnqrg.js";const C=l({__name:"WorkPoolQueueCreate",setup(k){const e=n("workPoolName");return _("Create Work Pool Queue"),(d,f)=>{const r=s("p-layout-default");return p(),u(r,null,{header:a(()=>[t(o(c),{"work-pool-name":o(e)},null,8,["work-pool-name"])]),default:a(()=>[t(o(m),{"work-pool-name":o(e)},null,8,["work-pool-name"])]),_:1})}}});export{C as default};
//# sourceMappingURL=WorkPoolQueueCreate-DeAXjZ1m.js.map
