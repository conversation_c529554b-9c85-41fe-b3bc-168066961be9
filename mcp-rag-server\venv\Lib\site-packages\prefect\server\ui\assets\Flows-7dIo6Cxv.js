import{d as m,e as i,f as _,g as o,i as w,c as s,o as t,j as n,q as b,a as k,F as C,n as a,bM as y,bN as h,k as v,bO as x}from"./index-ei-kaitd.js";import{u as F}from"./usePageTitle-LeBMnqrg.js";const V=m({__name:"Flows",setup(g){const c=i(),l={interval:3e4},e=_(c.flows.getFlowsCount,[{}],l),r=o(()=>e.response??0),u=o(()=>e.executed&&r.value===0),p=o(()=>e.executed),d=()=>{e.refresh()};return F("Flows"),(N,B)=>{const f=w("p-layout-default");return t(),s(f,{class:"flows"},{header:n(()=>[v(a(x))]),default:n(()=>[p.value?(t(),b(C,{key:0},[u.value?(t(),s(a(y),{key:0})):(t(),s(a(h),{key:1,selectable:"",onDelete:d}))],64)):k("",!0)]),_:1})}}});export{V as default};
//# sourceMappingURL=Flows-7dIo6Cxv.js.map
