import{d as v,e as b,W as C,f as g,g as r,i as h,c as D,a as k,o as E,j as p,k as c,n as u,c5 as w,c8 as x,L as m,c7 as d,be as V,$}from"./index-ei-kaitd.js";import{u as B}from"./usePageTitle-LeBMnqrg.js";const W=v({__name:"DeploymentEdit",setup(I){const a=b(),t=C("deploymentId"),i={interval:3e5},s=g(a.deployments.getDeployment,[t.value],i),e=r(()=>s.response);async function y(l){try{await a.deployments.updateDeploymentV2(t.value,l),m("Deployment updated","success"),s.refresh(),d.push(V.deployment(t.value))}catch(o){const n=$(o,"Error updating deployment");m(n,"error"),console.warn(o)}}function f(){d.back()}const _=r(()=>e.value?`Edit Deployment: ${e.value.name}`:"Edit Deployment");return B(_),(l,o)=>{const n=h("p-layout-default");return e.value?(E(),D(n,{key:0,class:"deployment-edit"},{header:p(()=>[c(u(x),{deployment:e.value},null,8,["deployment"])]),default:p(()=>[c(u(w),{deployment:e.value,onCancel:f,onSubmit:y},null,8,["deployment"])]),_:1})):k("",!0)}}});export{W as default};
//# sourceMappingURL=DeploymentEdit-CICik4BU.js.map
