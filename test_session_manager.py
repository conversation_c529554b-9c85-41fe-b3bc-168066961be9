#!/usr/bin/env python3
"""Test script for crawl session manager."""

import asyncio
from crawlers.session_manager import CrawlSessionManager
from core.application.services import DocumentService
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.domain.entities import CrawlStrategy
from config.settings import get_settings


async def test_session_manager():
    print('🕷️  Testing Crawl Session Manager')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    # Create session manager
    session_manager = CrawlSessionManager(
        document_service=doc_service,
        max_concurrent_sessions=2,
        max_pages_per_session=5,  # Limited for testing
        max_depth=2,
    )
    
    # Test 1: Create session
    print('\n📝 Test 1: Creating crawl session')
    session = await session_manager.create_session(
        name="Test Session",
        start_urls=["https://example.com"],
        strategy=CrawlStrategy.BREADTH_FIRST,
        description="Test crawl session for development",
        created_by="test_user",
    )
    
    print(f'✅ Created session: {session.id}')
    print(f'   Name: {session.name}')
    print(f'   Status: {session.status.value}')
    print(f'   Start URLs: {session.start_urls}')
    print(f'   Strategy: {session.strategy.value}')
    
    # Test 2: Start session
    print('\n🚀 Test 2: Starting crawl session')
    started = await session_manager.start_session(session.id)
    
    if started:
        print(f'✅ Session started successfully')
        
        # Wait a bit and check progress
        await asyncio.sleep(5)
        
        stats = session_manager.get_session_statistics(session.id)
        if stats:
            print(f'📊 Session progress:')
            print(f'   Status: {stats["status"]}')
            print(f'   URLs crawled: {stats["urls_crawled"]}')
            print(f'   URLs failed: {stats["urls_failed"]}')
            print(f'   URLs to crawl: {stats["urls_to_crawl"]}')
            print(f'   Current depth: {stats["current_depth"]}')
        
        # Wait for completion
        print('\n⏳ Waiting for session to complete...')
        for i in range(30):  # Wait up to 30 seconds
            await asyncio.sleep(1)
            current_session = session_manager.get_session(session.id)
            if current_session and current_session.status.value in ['completed', 'failed', 'cancelled']:
                break
        
        # Final statistics
        final_stats = session_manager.get_session_statistics(session.id)
        if final_stats:
            print(f'\n📊 Final session statistics:')
            print(f'   Status: {final_stats["status"]}')
            print(f'   URLs crawled: {final_stats["urls_crawled"]}')
            print(f'   URLs failed: {final_stats["urls_failed"]}')
            print(f'   URLs skipped: {final_stats["urls_skipped"]}')
            if final_stats.get("stats"):
                print(f'   Success rate: {final_stats["stats"].get("success_rate", 0):.1f}%')
    else:
        print('❌ Failed to start session')
    
    # Test 3: Global statistics
    print('\n🌍 Test 3: Global statistics')
    global_stats = session_manager.get_global_statistics()
    print(f'📊 Global statistics:')
    print(f'   Total sessions: {global_stats["total_sessions"]}')
    print(f'   Running sessions: {global_stats["running_sessions"]}')
    print(f'   Completed sessions: {global_stats["completed_sessions"]}')
    print(f'   Total pages crawled: {global_stats["total_pages_crawled"]}')
    print(f'   Success rate: {global_stats["success_rate"]:.1f}%')
    
    # Test 4: List sessions
    print('\n📋 Test 4: List all sessions')
    sessions = session_manager.list_sessions()
    print(f'📝 Found {len(sessions)} sessions:')
    for s in sessions:
        print(f'   - {s.name} ({s.id[:8]}...) - {s.status.value}')
    
    print('\n🎉 Session manager testing completed!')


async def test_eufunds_session():
    """Test session manager with eufunds.bg."""
    print('\n🇪🇺 Testing with eufunds.bg')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    # Create session manager with conservative settings
    session_manager = CrawlSessionManager(
        document_service=doc_service,
        max_concurrent_sessions=1,
        max_pages_per_session=10,  # Limited for testing
        max_depth=2,
    )
    
    # Create session for eufunds.bg
    session = await session_manager.create_session(
        name="EUFunds.bg Crawl",
        start_urls=["https://eufunds.bg"],
        strategy=CrawlStrategy.BREADTH_FIRST,
        allowed_domains=["eufunds.bg"],
        description="Crawl eufunds.bg for EU funding information",
        created_by="mcp_rag_server",
    )
    
    print(f'✅ Created EUFunds session: {session.id}')
    
    # Start session
    started = await session_manager.start_session(session.id)
    
    if started:
        print(f'🚀 EUFunds session started')
        
        # Monitor progress
        for i in range(60):  # Wait up to 60 seconds
            await asyncio.sleep(1)
            
            if i % 10 == 0:  # Print progress every 10 seconds
                stats = session_manager.get_session_statistics(session.id)
                if stats:
                    print(f'📊 Progress: {stats["urls_crawled"]} crawled, {stats["urls_to_crawl"]} remaining')
            
            current_session = session_manager.get_session(session.id)
            if current_session and current_session.status.value in ['completed', 'failed', 'cancelled']:
                break
        
        # Final results
        final_stats = session_manager.get_session_statistics(session.id)
        if final_stats:
            print(f'\n🎯 EUFunds crawl results:')
            print(f'   Status: {final_stats["status"]}')
            print(f'   Pages crawled: {final_stats["urls_crawled"]}')
            print(f'   Pages failed: {final_stats["urls_failed"]}')
            if final_stats.get("stats"):
                print(f'   Success rate: {final_stats["stats"].get("success_rate", 0):.1f}%')
    
    print('\n✅ EUFunds testing completed!')


async def main():
    """Main function."""
    print('🚀 MCP RAG Server - Session Manager Testing')
    print('=' * 60)
    
    # Basic session manager test
    await test_session_manager()
    
    # Ask user if they want to test with eufunds.bg
    import sys
    if len(sys.argv) > 1 and sys.argv[1] == "--eufunds":
        await test_eufunds_session()
    else:
        print('\n💡 Tip: Run with --eufunds to test with eufunds.bg')
    
    print('\n🎉 All session manager tests completed!')


if __name__ == "__main__":
    asyncio.run(main())
