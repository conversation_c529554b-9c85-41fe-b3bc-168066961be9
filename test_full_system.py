#!/usr/bin/env python3
"""Comprehensive test script for the entire MCP RAG Server system."""

import asyncio
import aiohttp
import json
from datetime import datetime

from crawlers.session_manager import CrawlSessionManager
from core.application.services import (
    DocumentService, ChunkService, EmbeddingService
)
from core.application.services.chunk_processor import ChunkProcessor
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from core.domain.entities import CrawlStrategy, DocumentType
from config.settings import get_settings


# Mock chunk repository for testing
class MockChunkRepository:
    def __init__(self):
        self.chunks = {}
        self.next_id = 1
    
    async def create(self, chunk):
        chunk.id = str(self.next_id)
        self.next_id += 1
        self.chunks[chunk.id] = chunk
        return chunk
    
    async def delete_chunks_by_document(self, document_id):
        to_delete = [cid for cid, c in self.chunks.items() if c.document_id == document_id]
        for cid in to_delete:
            del self.chunks[cid]
        return len(to_delete)
    
    async def bulk_create(self, chunks):
        for chunk in chunks:
            await self.create(chunk)
        return chunks
    
    async def bulk_update_embeddings(self, chunk_embeddings):
        for chunk_id, embedding, model in chunk_embeddings:
            if chunk_id in self.chunks:
                self.chunks[chunk_id].embedding = embedding
                self.chunks[chunk_id].embedding_model = model
        return len(chunk_embeddings)
    
    async def get_chunks_by_document(self, document_id, limit=100, offset=0):
        return [c for c in self.chunks.values() if c.document_id == document_id]
    
    async def get_statistics(self):
        total = len(self.chunks)
        with_embeddings = len([c for c in self.chunks.values() if c.embedding])
        return {
            "total_chunks": total,
            "chunks_with_embeddings": with_embeddings,
        }
    
    async def get_chunks_without_embeddings(self, limit=100, embedding_model=None):
        return [c for c in self.chunks.values() if not c.embedding][:limit]
    
    async def get_random_chunks(self, limit=10):
        return list(self.chunks.values())[:limit]


async def test_api_endpoints():
    """Test API endpoints."""
    print('🌐 Testing API Endpoints')
    print('=' * 50)
    
    base_url = "http://localhost:8000"
    
    async with aiohttp.ClientSession() as session:
        # Test health endpoint
        print('\n🏥 Testing health endpoint')
        async with session.get(f"{base_url}/health/") as response:
            if response.status == 200:
                data = await response.json()
                print(f'✅ Health check: {data["status"]}')
                print(f'   Version: {data["version"]}')
                print(f'   Environment: {data["environment"]}')
            else:
                print(f'❌ Health check failed: {response.status}')
        
        # Test search test endpoint
        print('\n🔍 Testing search test endpoint')
        async with session.get(f"{base_url}/api/search/test") as response:
            if response.status == 200:
                data = await response.json()
                print(f'✅ Search test: {data["message"]}')
            else:
                print(f'❌ Search test failed: {response.status}')
        
        # Test simple search
        print('\n🔎 Testing simple search')
        search_query = "artificial intelligence machine learning"
        async with session.get(f"{base_url}/api/search/simple?q={search_query}&max_results=3") as response:
            if response.status == 200:
                data = await response.json()
                print(f'✅ Search successful: {data["query"]}')
                print(f'   Results: {data["total_results"]}')
                for i, result in enumerate(data["results"], 1):
                    print(f'   {i}. Score: {result["score"]:.2f} - {result["content"][:60]}...')
            else:
                print(f'❌ Search failed: {response.status}')


async def test_crawling_system():
    """Test the crawling system."""
    print('\n🕷️  Testing Crawling System')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    # Create session manager
    session_manager = CrawlSessionManager(
        document_service=doc_service,
        max_concurrent_sessions=1,
        max_pages_per_session=3,  # Limited for testing
        max_depth=1,
    )
    
    # Create crawl session
    session = await session_manager.create_session(
        name="Full System Test",
        start_urls=["https://example.com"],
        strategy=CrawlStrategy.BREADTH_FIRST,
        description="Full system integration test",
        created_by="test_system",
    )
    
    print(f'✅ Created crawl session: {session.id}')
    
    # Start session
    started = await session_manager.start_session(session.id)
    
    if started:
        print('🚀 Crawl session started')
        
        # Wait for completion
        for i in range(20):  # Wait up to 20 seconds
            await asyncio.sleep(1)
            current_session = session_manager.get_session(session.id)
            if current_session and current_session.status.value in ['completed', 'failed']:
                break
        
        # Get final statistics
        stats = session_manager.get_session_statistics(session.id)
        if stats:
            print(f'📊 Crawl results:')
            print(f'   Status: {stats["status"]}')
            print(f'   Pages crawled: {stats["urls_crawled"]}')
            print(f'   Pages failed: {stats["urls_failed"]}')
    
    return session_manager


async def test_embedding_system():
    """Test the embedding system."""
    print('\n🤖 Testing Embedding System')
    print('=' * 50)
    
    # Setup
    settings = get_settings()
    doc_repository = SupabaseDocumentRepository(settings.supabase_url, settings.supabase_service_key)
    doc_service = DocumentService(doc_repository)
    
    chunk_repo = MockChunkRepository()
    chunk_service = ChunkService(chunk_repo)
    
    embedding_service = EmbeddingService(
        chunk_repository=chunk_repo,
        openai_api_key=settings.openai_api_key,
        model='text-embedding-3-small'
    )
    
    chunk_processor = ChunkProcessor(
        document_service=doc_service,
        chunk_service=chunk_service,
        embedding_service=embedding_service,
        chunk_size=400,
        chunk_overlap=80,
    )
    
    # Create test document
    test_content = """
    The MCP RAG Server is a comprehensive system for crawling, processing, and searching documents.
    It uses advanced natural language processing techniques to create semantic embeddings.
    The system supports multiple document types including HTML, PDF, and plain text.
    Vector search capabilities enable powerful semantic search and retrieval augmented generation.
    The architecture follows clean code principles with domain-driven design patterns.
    """
    
    document = await doc_service.create_document(
        url="https://test.example.com/full-system-test",
        content=test_content,
        document_type=DocumentType.HTML,
        metadata={"title": "Full System Test Document"},
    )
    
    print(f'✅ Created test document: {document.id}')
    
    # Process document
    success = await chunk_processor.process_document(document.id)
    
    if success:
        print('✅ Document processed successfully')
        
        # Get chunks
        chunks = await chunk_service.get_chunks_by_document(document.id)
        print(f'   Created {len(chunks)} chunks')
        
        for i, chunk in enumerate(chunks):
            embedding_info = f"{len(chunk.embedding)} dims" if chunk.embedding else "no embedding"
            print(f'   Chunk {i+1}: {len(chunk.content)} chars, {embedding_info}')
        
        # Get processing statistics
        stats = await chunk_processor.get_processing_statistics()
        print(f'\n📊 Processing statistics:')
        print(f'   Documents processed: {stats["documents_processed"]}')
        print(f'   Chunks created: {stats["chunks_created"]}')
        print(f'   Embeddings generated: {stats["embeddings_generated"]}')
        print(f'   Average processing time: {stats["average_processing_time"]:.2f}s')
    
    return chunk_processor


async def test_full_integration():
    """Test full system integration."""
    print('\n🎯 Testing Full System Integration')
    print('=' * 50)
    
    # Test API endpoints
    await test_api_endpoints()
    
    # Test crawling system
    session_manager = await test_crawling_system()
    
    # Test embedding system
    chunk_processor = await test_embedding_system()
    
    # Global statistics
    print('\n📈 Global System Statistics')
    print('=' * 30)
    
    global_stats = session_manager.get_global_statistics()
    print(f'🕷️  Crawling Statistics:')
    print(f'   Total sessions: {global_stats["total_sessions"]}')
    print(f'   Pages crawled: {global_stats["total_pages_crawled"]}')
    print(f'   Success rate: {global_stats["success_rate"]:.1f}%')
    
    processor_stats = chunk_processor.get_statistics()
    print(f'\n🧩 Processing Statistics:')
    print(f'   Documents processed: {processor_stats["documents_processed"]}')
    print(f'   Chunks created: {processor_stats["chunks_created"]}')
    print(f'   Embeddings generated: {processor_stats["embeddings_generated"]}')
    
    print('\n🎉 Full system integration test completed!')


async def main():
    """Main function."""
    print('🚀 MCP RAG Server - Full System Test')
    print('=' * 60)
    print(f'🕐 Started at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')
    
    try:
        await test_full_integration()
        
        print('\n✅ ALL TESTS PASSED!')
        print('🎯 System is fully operational and ready for production!')
        
    except Exception as e:
        print(f'\n❌ Test failed: {e}')
        import traceback
        traceback.print_exc()
    
    print(f'\n🕐 Completed at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}')


if __name__ == "__main__":
    asyncio.run(main())
