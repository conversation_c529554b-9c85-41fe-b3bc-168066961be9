import{d as x,V as y,u as w,J as h,K as T,i as V,c as b,o as k,j as s,k as r,n,z as B,H as P,I as i,B as $,L as m,$ as H}from"./index-ei-kaitd.js";import{_ as N}from"./AutomationWizard.vue_vue_type_script_setup_true_lang-CQQMIE-5.js";import{u as j}from"./usePageTitle-LeBMnqrg.js";import{u as q}from"./usePrefectApi-qsKG6mzx.js";import"./mapper-BuxGYc8V.js";import"./api-DGOAIix_.js";const L=x({__name:"AutomationCreate",async setup(v){let e,u;j("Create Automation");const _=q(),c=y(),l=w(),p=[{text:"Automations",to:c.automations()},{text:"Create"}],{getActions:f,getTrigger:g}=h(),d=([e,u]=T(()=>C()),e=await e,u(),e);async function C(){const o={},[t,a]=await Promise.all([g(),f()]);return t&&(o.trigger=t),a&&(o.actions=a),o}async function A(o){try{await _.automations.createAutomation(o),m(i.success.automationCreate),l.push(c.automations())}catch(t){console.error(t);const a=H(t,i.error.automationCreate);m(a,"error",{timeout:!1})}}return(o,t)=>{const a=V("p-layout-default");return k(),b(a,{class:"automation-create"},{header:s(()=>[r(n(B),{crumbs:p},{actions:s(()=>[r(n(P),{to:n(i).docs.automations},{default:s(()=>t[0]||(t[0]=[$(" Documentation ")])),_:1,__:[0]},8,["to"])]),_:1})]),default:s(()=>[r(N,{automation:n(d),onSubmit:A},null,8,["automation"])]),_:1})}}});export{L as default};
//# sourceMappingURL=AutomationCreate-DsV_cUzp.js.map
