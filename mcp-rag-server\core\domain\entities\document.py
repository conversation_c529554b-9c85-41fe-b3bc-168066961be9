"""Document entity for the MCP RAG Server."""

from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from enum import Enum
import uuid


class DocumentType(str, Enum):
    """Document type enumeration."""
    HTML = "html"
    PDF = "pdf"
    TEXT = "text"
    MARKDOWN = "markdown"


class DocumentStatus(str, Enum):
    """Document processing status."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    ARCHIVED = "archived"


@dataclass
class DocumentMetadata:
    """Document metadata container."""
    title: Optional[str] = None
    author: Optional[str] = None
    language: Optional[str] = None
    keywords: List[str] = field(default_factory=list)
    description: Optional[str] = None
    source_url: Optional[str] = None
    content_type: Optional[str] = None
    file_size: Optional[int] = None
    page_count: Optional[int] = None
    custom_fields: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Document:
    """Core document entity."""
    
    # Primary identifiers
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    url: str = ""
    
    # Content
    content: str = ""
    raw_content: Optional[str] = None
    
    # Classification
    document_type: DocumentType = DocumentType.HTML
    status: DocumentStatus = DocumentStatus.PENDING
    
    # Metadata
    metadata: DocumentMetadata = field(default_factory=DocumentMetadata)
    
    # Processing information
    content_hash: Optional[str] = None
    embedding_model: Optional[str] = None
    chunk_count: int = 0
    
    # Timestamps
    created_at: datetime = field(default_factory=datetime.utcnow)
    updated_at: datetime = field(default_factory=datetime.utcnow)
    processed_at: Optional[datetime] = None
    
    # Crawling metadata
    crawl_depth: int = 0
    parent_url: Optional[str] = None
    crawl_session_id: Optional[str] = None
    
    # Error handling
    error_message: Optional[str] = None
    retry_count: int = 0
    
    def __post_init__(self):
        """Post-initialization processing."""
        if not self.metadata.source_url and self.url:
            self.metadata.source_url = self.url
    
    def update_status(self, status: DocumentStatus, error_message: Optional[str] = None):
        """Update document status with timestamp."""
        self.status = status
        self.updated_at = datetime.utcnow()
        
        if status == DocumentStatus.COMPLETED:
            self.processed_at = datetime.utcnow()
        
        if error_message:
            self.error_message = error_message
    
    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.updated_at = datetime.utcnow()
    
    def is_processable(self) -> bool:
        """Check if document can be processed."""
        return (
            self.status in [DocumentStatus.PENDING, DocumentStatus.FAILED] and
            self.retry_count < 3 and
            bool(self.content or self.url)
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "id": self.id,
            "url": self.url,
            "content": self.content,
            "raw_content": self.raw_content,
            "document_type": self.document_type.value,
            "status": self.status.value,
            "metadata": {
                "title": self.metadata.title,
                "author": self.metadata.author,
                "language": self.metadata.language,
                "keywords": self.metadata.keywords,
                "description": self.metadata.description,
                "source_url": self.metadata.source_url,
                "content_type": self.metadata.content_type,
                "file_size": self.metadata.file_size,
                "page_count": self.metadata.page_count,
                "custom_fields": self.metadata.custom_fields,
            },
            "content_hash": self.content_hash,
            "embedding_model": self.embedding_model,
            "chunk_count": self.chunk_count,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat(),
            "processed_at": self.processed_at.isoformat() if self.processed_at else None,
            "crawl_depth": self.crawl_depth,
            "parent_url": self.parent_url,
            "crawl_session_id": self.crawl_session_id,
            "error_message": self.error_message,
            "retry_count": self.retry_count,
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> "Document":
        """Create document from dictionary."""
        metadata = DocumentMetadata(
            title=data.get("metadata", {}).get("title"),
            author=data.get("metadata", {}).get("author"),
            language=data.get("metadata", {}).get("language"),
            keywords=data.get("metadata", {}).get("keywords", []),
            description=data.get("metadata", {}).get("description"),
            source_url=data.get("metadata", {}).get("source_url"),
            content_type=data.get("metadata", {}).get("content_type"),
            file_size=data.get("metadata", {}).get("file_size"),
            page_count=data.get("metadata", {}).get("page_count"),
            custom_fields=data.get("metadata", {}).get("custom_fields", {}),
        )
        
        return cls(
            id=data.get("id", str(uuid.uuid4())),
            url=data.get("url", ""),
            content=data.get("content", ""),
            raw_content=data.get("raw_content"),
            document_type=DocumentType(data.get("document_type", DocumentType.HTML)),
            status=DocumentStatus(data.get("status", DocumentStatus.PENDING)),
            metadata=metadata,
            content_hash=data.get("content_hash"),
            embedding_model=data.get("embedding_model"),
            chunk_count=data.get("chunk_count", 0),
            created_at=datetime.fromisoformat(data["created_at"]) if data.get("created_at") else datetime.utcnow(),
            updated_at=datetime.fromisoformat(data["updated_at"]) if data.get("updated_at") else datetime.utcnow(),
            processed_at=datetime.fromisoformat(data["processed_at"]) if data.get("processed_at") else None,
            crawl_depth=data.get("crawl_depth", 0),
            parent_url=data.get("parent_url"),
            crawl_session_id=data.get("crawl_session_id"),
            error_message=data.get("error_message"),
            retry_count=data.get("retry_count", 0),
        )
