import{d as B,e as D,W as R,f as U,g as s,aI as V,al as j,bt as F,bu as I,bR as S,i,c as m,a as k,o as d,j as a,k as t,n as e,a6 as $,bS as Q,cV as T,cW as Y,bz as Z,cX as w,cY as q,cZ as z}from"./index-ei-kaitd.js";import{u as A}from"./usePageTitle-LeBMnqrg.js";const G=B({__name:"WorkPool",setup(O){const b=D(),n=R("workPoolName"),f={interval:3e5},u=U(b.workPools.getWorkPoolByName,[n.value],f),o=s(()=>u.response),c=s(()=>{var l;return((l=o.value)==null?void 0:l.type)==="prefect-agent"}),v=s(()=>[{label:"Details",hidden:V.xl},{label:"Runs"},{label:"Work Queues"},{label:"Workers",hidden:c.value},{label:"Deployments"}]),r=j("tab","Details"),{tabs:_}=F(v,r),P=s(()=>{var l;return((l=o.value)==null?void 0:l.status)!=="ready"}),y=s(()=>{var l;return`prefect ${c.value?"agent":"worker"} start --pool "${(l=o.value)==null?void 0:l.name}"`}),{filter:W}=I({workPools:{name:[n.value]}}),{filter:g}=S({workPools:{name:[n.value]}}),C=s(()=>o.value?`Work Pool: ${o.value.name}`:"Work Pool");return A(C),(l,p)=>{const h=i("p-tabs"),x=i("p-layout-well");return o.value?(d(),m(x,{key:0,class:"work-pool"},{header:a(()=>[t(e(q),{"work-pool":o.value,onUpdate:e(u).refresh},null,8,["work-pool","onUpdate"]),P.value?(d(),m(e(z),{key:0,class:"work-pool__code-banner",command:y.value,title:"Your work pool is almost ready!",subtitle:"Run this command to start."},null,8,["command"])):k("",!0)]),well:a(()=>[t(e(w),{alternate:"","work-pool":o.value},null,8,["work-pool"])]),default:a(()=>[t(h,{selected:e(r),"onUpdate:selected":p[0]||(p[0]=N=>$(r)?r.value=N:null),tabs:e(_)},{details:a(()=>[t(e(w),{"work-pool":o.value},null,8,["work-pool"])]),runs:a(()=>[t(e(Z),{filter:e(W),prefix:"runs"},null,8,["filter"])]),"work-queues":a(()=>[t(e(Y),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),workers:a(()=>[t(e(T),{"work-pool-name":e(n)},null,8,["work-pool-name"])]),deployments:a(()=>[t(e(Q),{filter:e(g)},null,8,["filter"])]),_:1},8,["selected","tabs"])]),_:1})):k("",!0)}}});export{G as default};
//# sourceMappingURL=WorkPool-Bn_qnZCe.js.map
