{"version": 3, "file": "Artifacts-Dv3blXKR.js", "sources": ["../../src/pages/Artifacts.vue"], "sourcesContent": ["<template>\n  <p-layout-default class=\"artifacts\">\n    <template #header>\n      <PageHeadingArtifacts />\n    </template>\n\n    <ArtifactCollections />\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import {\n    PageHeadingArtifacts,\n    ArtifactCollections\n  } from '@prefecthq/prefect-ui-library'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n\n  usePageTitle('Artifacts')\n</script>\n"], "names": ["usePageTitle", "_createBlock", "_component_p_layout_default", "_createVNode", "_unref", "PageHeadingArtifacts", "ArtifactCollections"], "mappings": "mLAiBE,OAAAA,EAAa,WAAW,iDAhBxB,EAAAC,EAMmBC,EAAA,CAND,MAAM,aAAW,CACtB,SACT,IAAwB,CAAxBC,EAAwBC,EAAAC,CAAA,CAAA,CAAA,aAG1B,IAAuB,CAAvBF,EAAuBC,EAAAE,CAAA,CAAA,CAAA"}