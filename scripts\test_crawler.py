#!/usr/bin/env python3
"""Test script for HTML crawler."""

import asyncio
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from crawlers.html import AiohttpCrawler
from core.application.services import DocumentService
from core.infrastructure.database.supabase_document_repository import SupabaseDocumentRepository
from config.settings import get_settings


async def test_crawler():
    """Test the HTML crawler."""
    print("🕷️  Testing HTML Crawler")
    print("=" * 50)
    
    # Setup
    settings = get_settings()
    
    # Create repository and service
    doc_repository = SupabaseDocumentRepository(
        settings.supabase_url,
        settings.supabase_service_key
    )
    doc_service = DocumentService(doc_repository)
    
    # Create crawler
    crawler = AiohttpCrawler(
        document_service=doc_service,
        max_concurrent=3,
        delay_range=(1.0, 2.0),
    )
    
    # Test URLs
    test_urls = [
        "https://httpbin.org/html",
        "https://example.com",
        "https://httpbin.org/json",  # This should be skipped (not HTML)
    ]
    
    print(f"🎯 Testing with URLs: {test_urls}")
    
    # Test single URL crawling
    print("\n📄 Testing single URL crawling...")
    
    try:
        import aiohttp
        async with aiohttp.ClientSession() as session:
            result = await crawler.crawl_url(test_urls[0], session)
            
            print(f"✅ Crawled: {result.url}")
            print(f"   Status: {result.status_code}")
            print(f"   Content length: {len(result.content)}")
            print(f"   Links found: {len(result.links)}")
            print(f"   Response time: {result.response_time:.2f}s")
            
            if result.error:
                print(f"   Error: {result.error}")
    
    except Exception as e:
        print(f"❌ Error testing single URL: {e}")
    
    # Test multiple URLs
    print("\n📄 Testing multiple URLs...")
    
    try:
        results = await crawler.crawl_urls(test_urls)
        
        for result in results:
            print(f"✅ {result.url}")
            print(f"   Status: {result.status_code}")
            print(f"   Content: {len(result.content)} chars")
            print(f"   Links: {len(result.links)}")
            print(f"   Time: {result.response_time:.2f}s")
            
            if result.error:
                print(f"   ❌ Error: {result.error}")
            print()
    
    except Exception as e:
        print(f"❌ Error testing multiple URLs: {e}")
    
    # Test website crawling (limited)
    print("\n🌐 Testing website crawling...")
    
    try:
        results = await crawler.crawl_website(
            start_urls=["https://example.com"],
            max_pages=3,
            max_depth=2,
        )
        
        print(f"✅ Crawled {len(results)} pages")
        
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result.url} ({result.status_code})")
    
    except Exception as e:
        print(f"❌ Error testing website crawling: {e}")
    
    # Show statistics
    print("\n📊 Crawler Statistics:")
    stats = crawler.get_statistics()
    for key, value in stats.items():
        print(f"   {key}: {value}")
    
    print("\n✅ Crawler testing completed!")


async def test_eufunds_crawler():
    """Test crawler specifically with eufunds.bg."""
    print("\n🇪🇺 Testing with eufunds.bg")
    print("=" * 50)
    
    # Setup
    settings = get_settings()
    
    # Create repository and service
    doc_repository = SupabaseDocumentRepository(
        settings.supabase_url,
        settings.supabase_service_key
    )
    doc_service = DocumentService(doc_repository)
    
    # Create crawler with more conservative settings
    crawler = AiohttpCrawler(
        document_service=doc_service,
        max_concurrent=2,
        delay_range=(2.0, 4.0),  # Be respectful
        user_agent="MCP-RAG-Server/1.0 (Educational Purpose)",
    )
    
    # Test with eufunds.bg
    test_urls = ["https://eufunds.bg"]
    
    try:
        print(f"🎯 Crawling: {test_urls[0]}")
        
        results = await crawler.crawl_website(
            start_urls=test_urls,
            max_pages=5,  # Limited for testing
            max_depth=2,
            allowed_domains=["eufunds.bg"],
        )
        
        print(f"✅ Successfully crawled {len(results)} pages from eufunds.bg")
        
        for i, result in enumerate(results, 1):
            print(f"   {i}. {result.url}")
            print(f"      Status: {result.status_code}")
            print(f"      Content: {len(result.content)} chars")
            print(f"      Links: {len(result.links)}")
            
            if result.error:
                print(f"      ❌ Error: {result.error}")
            print()
        
        # Show final statistics
        print("📊 Final Statistics:")
        stats = crawler.get_statistics()
        for key, value in stats.items():
            print(f"   {key}: {value}")
    
    except Exception as e:
        print(f"❌ Error crawling eufunds.bg: {e}")


async def main():
    """Main function."""
    print("🚀 MCP RAG Server - Crawler Testing")
    print("=" * 60)
    
    # Basic crawler test
    await test_crawler()
    
    # Ask user if they want to test with eufunds.bg
    if len(sys.argv) > 1 and sys.argv[1] == "--eufunds":
        await test_eufunds_crawler()
    else:
        print("\n💡 Tip: Run with --eufunds to test with eufunds.bg")
    
    print("\n🎉 All tests completed!")


if __name__ == "__main__":
    asyncio.run(main())
