import{d as B,e as D,u as C,W as x,K as g,ae as p,i as w,c as E,a as F,n as e,o as I,j as c,k as s,cn as N,cu as O,U as P,cp as S,cv as T,L as b,be as U}from"./index-ei-kaitd.js";import{u as V}from"./usePageTitle-LeBMnqrg.js";const H=B({__name:"BlockEdit",async setup(W){let t,n;const l=D(),u=C(),r=x("blockDocumentId"),o=([t,n]=g(()=>l.blockDocuments.getBlockDocument(r.value)),t=await t,n(),t),{blockType:i,blockSchema:_}=o,d=p(o.data),m=p(o.name);function f(k){l.blockDocuments.updateBlockDocument(o.id,k).then(()=>{b("Block updated successfully","success"),u.push(U.block(r.value))}).catch(a=>{b("Failed to update block","error"),console.error(a)})}function y(){u.back()}return V(`Edit Block: ${m.value}`),(k,a)=>{const v=w("p-layout-default");return e(o)?(I(),E(v,{key:0,class:"block-edit"},{header:c(()=>[s(e(T),{"block-document":e(o)},null,8,["block-document"])]),default:c(()=>[s(e(N),{"block-type":e(i)},{default:c(()=>[s(e(O),P({data:d.value,"onUpdate:data":a[0]||(a[0]=h=>d.value=h)},{name:m.value,blockSchema:e(_)},S({submit:f,cancel:y})),null,16,["data"])]),_:1},8,["block-type"])]),_:1})):F("",!0)}}});export{H as default};
//# sourceMappingURL=BlockEdit-DShEAl33.js.map
