{"prefect": {"process": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "stream_output": "{{ stream_output }}", "working_dir": "{{ working_dir }}"}, "variables": {"type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "stream_output": {"title": "Stream Output", "description": "If enabled, workers will stream output from flow run processes to local standard output.", "default": true, "type": "boolean"}, "working_dir": {"title": "Working Directory", "description": "If provided, workers will open flow run processes within the specified path as the working directory. Otherwise, a temporary directory will be created.", "type": "string", "format": "path"}}}}, "description": "Execute flow runs as subprocesses on a worker. Works well for local execution when first getting started.", "display_name": "Process", "documentation_url": "https://docs.prefect.io/latest/api-ref/prefect/workers/process/", "install_command": "pip install prefect", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/356e6766a91baf20e1d08bbe16e8b5aaef4d8643-48x48.png", "type": "process"}}, "prefect-aws": {"ecs": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "aws_credentials": "{{ aws_credentials }}", "task_definition": {"containerDefinitions": [{"image": "{{ image }}", "name": "{{ container_name }}"}], "cpu": "{{ cpu }}", "family": "{{ family }}", "memory": "{{ memory }}", "executionRoleArn": "{{ execution_role_arn }}"}, "task_run_request": {"launchType": "{{ launch_type }}", "cluster": "{{ cluster }}", "overrides": {"containerOverrides": [{"name": "{{ container_name }}", "command": "{{ command }}", "environment": "{{ env }}", "cpu": "{{ cpu }}", "memory": "{{ memory }}"}], "cpu": "{{ cpu }}", "memory": "{{ memory }}", "taskRoleArn": "{{ task_role_arn }}"}, "tags": "{{ labels }}", "taskDefinition": "{{ task_definition_arn }}", "capacityProviderStrategy": "{{ capacity_provider_strategy }}"}, "configure_cloudwatch_logs": "{{ configure_cloudwatch_logs }}", "cloudwatch_logs_options": "{{ cloudwatch_logs_options }}", "cloudwatch_logs_prefix": "{{ cloudwatch_logs_prefix }}", "network_configuration": "{{ network_configuration }}", "stream_output": "{{ stream_output }}", "task_start_timeout_seconds": "{{ task_start_timeout_seconds }}", "task_watch_poll_interval": "{{ task_watch_poll_interval }}", "auto_deregister_task_definition": "{{ auto_deregister_task_definition }}", "vpc_id": "{{ vpc_id }}", "container_name": "{{ container_name }}", "cluster": "{{ cluster }}", "match_latest_revision_in_family": "{{ match_latest_revision_in_family }}"}, "variables": {"description": "Variables for templating an ECS job.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to provide to the task run. These variables are set on the Prefect container at task runtime. These will not be set on the task definition.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "task_definition_arn": {"title": "Task Definition Arn", "description": "An identifier for an existing task definition to use. If set, options that require changes to the task definition will be ignored. All contents of the task definition in the job configuration will be ignored.", "type": "string"}, "aws_credentials": {"title": "AWS Credentials", "description": "The AWS credentials to use to connect to ECS. If not provided, credentials will be inferred from the local environment following AWS's boto client's rules.", "allOf": [{"$ref": "#/definitions/AwsCredentials"}]}, "cluster": {"title": "Cluster", "description": "The ECS cluster to run the task in. An ARN or name may be provided. If not provided, the default cluster will be used.", "type": "string"}, "family": {"title": "Family", "description": "A family for the task definition. If not provided, it will be inferred from the task definition. If the task definition does not have a family, the name will be generated. When flow and deployment metadata is available, the generated name will include their names. Values for this field will be slugified to match AWS character requirements.", "type": "string"}, "launch_type": {"title": "Launch Type", "description": "The type of ECS task run infrastructure that should be used. Note that 'FARGATE_SPOT' is not a formal ECS launch type, but we will configure the proper capacity provider strategy if set here.", "default": "FARGATE", "enum": ["FARGATE", "EC2", "EXTERNAL", "FARGATE_SPOT"], "type": "string"}, "capacity_provider_strategy": {"title": "Capacity Provider Strategy", "description": "The capacity provider strategy to use when running the task. If a capacity provider strategy is specified, the selected launch type will be ignored.", "type": "array", "items": {"$ref": "#/definitions/CapacityProvider"}}, "image": {"title": "Image", "description": "The image to use for the Prefect container in the task. If this value is not null, it will override the value in the task definition. This value defaults to a Prefect base image matching your local versions.", "type": "string"}, "cpu": {"title": "CPU", "description": "The amount of CPU to provide to the ECS task. Valid amounts are specified in the AWS documentation. If not provided, a default value of 1024 will be used unless present on the task definition.", "type": "integer"}, "memory": {"title": "Memory", "description": "The amount of memory to provide to the ECS task. Valid amounts are specified in the AWS documentation. If not provided, a default value of 2048 will be used unless present on the task definition.", "type": "integer"}, "container_name": {"title": "Container Name", "description": "The name of the container flow run orchestration will occur in. If not specified, a default value of prefect will be used and if that is not found in the task definition the first container will be used.", "type": "string"}, "task_role_arn": {"title": "Task Role ARN", "description": "A role to attach to the task run. This controls the permissions of the task while it is running.", "type": "string"}, "execution_role_arn": {"title": "Execution Role ARN", "description": "An execution role to use for the task. This controls the permissions of the task when it is launching. If this value is not null, it will override the value in the task definition. An execution role must be provided to capture logs from the container.", "type": "string"}, "vpc_id": {"title": "VPC ID", "description": "The AWS VPC to link the task run to. This is only applicable when using the 'awsvpc' network mode for your task. FARGATE tasks require this network  mode, but for EC2 tasks the default network mode is 'bridge'. If using the 'awsvpc' network mode and this field is null, your default VPC will be used. If no default VPC can be found, the task run will fail.", "type": "string"}, "configure_cloudwatch_logs": {"title": "Configure Cloudwatch Logs", "description": "If enabled, the Prefect container will be configured to send its output to the AWS CloudWatch logs service. This functionality requires an execution role with logs:CreateLogStream, logs:CreateLogGroup, and logs:PutLogEvents permissions. The default for this field is `False` unless `stream_output` is set.", "type": "boolean"}, "cloudwatch_logs_options": {"title": "Cloudwatch Logs Options", "description": "When `configure_cloudwatch_logs` is enabled, this setting may be used to pass additional options to the CloudWatch logs configuration or override the default options. See the [AWS documentation](https://docs.aws.amazon.com/AmazonECS/latest/developerguide/using_awslogs.html#create_awslogs_logdriver_options) for available options. ", "type": "object", "additionalProperties": {"type": "string"}}, "cloudwatch_logs_prefix": {"title": "Cloudwatch Logs Prefix", "description": "When `configure_cloudwatch_logs` is enabled, this setting may be used to set a prefix for the log group. If not provided, the default prefix will be `prefect-logs_<work_pool_name>_<deployment_id>`. If `awslogs-stream-prefix` is present in `Cloudwatch logs options` this setting will be ignored.", "type": "string"}, "network_configuration": {"title": "Network Configuration", "description": "When `network_configuration` is supplied it will override ECS Worker'sawsvpcConfiguration that defined in the ECS task executing your workload. See the [AWS documentation](https://docs.aws.amazon.com/AWSCloudFormation/latest/UserGuide/aws-properties-ecs-service-awsvpcconfiguration.html) for available options.", "type": "object"}, "stream_output": {"title": "Stream Output", "description": "If enabled, logs will be streamed from the Prefect container to the local console. Unless you have configured AWS CloudWatch logs manually on your task definition, this requires the same prerequisites outlined in `configure_cloudwatch_logs`.", "type": "boolean"}, "task_start_timeout_seconds": {"title": "Task Start Timeout Seconds", "description": "The amount of time to watch for the start of the ECS task before marking it as failed. The task must enter a RUNNING state to be considered started.", "default": 300, "type": "integer"}, "task_watch_poll_interval": {"title": "Task Watch Poll Interval", "description": "The amount of time to wait between AWS API calls while monitoring the state of an ECS task.", "default": 5.0, "type": "number"}, "auto_deregister_task_definition": {"title": "Auto Deregister Task Definition", "description": "If enabled, any task definitions that are created by this block will be deregistered. Existing task definitions linked by ARN will never be deregistered. Deregistering a task definition does not remove it from your AWS account, instead it will be marked as INACTIVE.", "default": false, "type": "boolean"}, "match_latest_revision_in_family": {"title": "Match Latest Revision In Family", "description": "If enabled, the most recent active revision in the task definition family will be compared against the desired ECS task configuration. If they are equal, the existing task definition will be used instead of registering a new one. If no family is specified the default family \"prefect\" will be used.", "default": false, "type": "boolean"}}, "definitions": {"AwsClientParameters": {"title": "AwsClientParameters", "description": "Model used to manage extra parameters that you can pass when you initialize\nthe Client. If you want to find more information, see\n[boto3 docs](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/core/session.html)\nfor more info about the possible client configurations.\n\nAttributes:\n    api_version: The API version to use. By default, botocore will\n        use the latest API version when creating a client. You only need\n        to specify this parameter if you want to use a previous API version\n        of the client.\n    use_ssl: Whether or not to use SSL. By default, SSL is used.\n        Note that not all services support non-ssl connections.\n    verify: Whether or not to verify SSL certificates. By default\n        SSL certificates are verified. If False, SSL will still be used\n        (unless use_ssl is False), but SSL certificates\n        will not be verified. Passing a file path to this is deprecated.\n    verify_cert_path: A filename of the CA cert bundle to\n        use. You can specify this argument if you want to use a\n        different CA cert bundle than the one used by botocore.\n    endpoint_url: The complete URL to use for the constructed\n        client. Normally, botocore will automatically construct the\n        appropriate URL to use when communicating with a service. You\n        can specify a complete URL (including the \"http/https\" scheme)\n        to override this behavior. If this value is provided,\n        then ``use_ssl`` is ignored.\n    config: Advanced configuration for Botocore clients. See\n        [botocore docs](https://botocore.amazonaws.com/v1/documentation/api/latest/reference/config.html)\n        for more details.", "type": "object", "properties": {"api_version": {"title": "API Version", "description": "The API version to use.", "type": "string"}, "use_ssl": {"title": "Use SSL", "description": "Whether or not to use SSL.", "default": true, "type": "boolean"}, "verify": {"title": "Verify", "description": "Whether or not to verify SSL certificates.", "default": true, "anyOf": [{"type": "boolean"}, {"type": "string", "format": "file-path"}]}, "verify_cert_path": {"title": "Certificate Authority Bundle File Path", "description": "Path to the CA cert bundle to use.", "format": "file-path", "type": "string"}, "endpoint_url": {"title": "Endpoint URL", "description": "The complete URL to use for the constructed client.", "type": "string"}, "config": {"title": "Botocore Config", "description": "Advanced configuration for Botocore clients.", "type": "object"}}}, "AwsCredentials": {"title": "AwsCredentials", "description": "Block used to manage authentication with AWS. AWS authentication is\nhandled via the `boto3` module. Refer to the\n[boto3 docs](https://boto3.amazonaws.com/v1/documentation/api/latest/guide/credentials.html)\nfor more info about the possible credential configurations.", "type": "object", "properties": {"aws_access_key_id": {"title": "AWS Access Key ID", "description": "A specific AWS access key ID.", "type": "string"}, "aws_secret_access_key": {"title": "AWS Access Key Secret", "description": "A specific AWS secret access key.", "type": "string", "writeOnly": true, "format": "password"}, "aws_session_token": {"title": "AWS Session Token", "description": "The session key for your AWS account. This is only needed when you are using temporary credentials.", "type": "string"}, "profile_name": {"title": "Profile Name", "description": "The profile to use when creating your session.", "type": "string"}, "region_name": {"title": "Region Name", "description": "The AWS Region where you want to create new connections.", "type": "string"}, "aws_client_parameters": {"title": "AWS Client Parameters", "description": "Extra parameters to initialize the Client.", "allOf": [{"$ref": "#/definitions/AwsClientParameters"}]}}, "block_type_slug": "aws-credentials", "secret_fields": ["aws_secret_access_key"], "block_schema_references": {}}, "CapacityProvider": {"title": "CapacityProvider", "description": "The capacity provider strategy to use when running the task.", "type": "object", "properties": {"capacityProvider": {"title": "Capacityprovider", "type": "string"}, "weight": {"title": "Weight", "type": "integer"}, "base": {"title": "Base", "type": "integer"}}, "required": ["capacityProvider", "weight", "base"]}}}}, "description": "Execute flow runs within containers on AWS ECS. Works with EC2 and Fargate clusters. Requires an AWS account.", "display_name": "AWS Elastic Container Service", "documentation_url": "https://prefecthq.github.io/prefect-aws/ecs_worker/", "install_command": "pip install prefect-aws", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/d74b16fe84ce626345adf235a47008fea2869a60-225x225.png", "type": "ecs"}}, "prefect-azure": {"azure-container-instance": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "image": "{{ image }}", "resource_group_name": "{{ resource_group_name }}", "subscription_id": "{{ subscription_id }}", "identities": "{{ identities }}", "entrypoint": "{{ entrypoint }}", "image_registry": "{{ image_registry }}", "cpu": "{{ cpu }}", "gpu_count": "{{ gpu_count }}", "gpu_sku": "{{ gpu_sku }}", "memory": "{{ memory }}", "subnet_ids": "{{ subnet_ids }}", "dns_servers": "{{ dns_servers }}", "stream_output": "{{ stream_output }}", "aci_credentials": "{{ aci_credentials }}", "task_start_timeout_seconds": "{{ task_start_timeout_seconds }}", "task_watch_poll_interval": "{{ task_watch_poll_interval }}", "arm_template": {"$schema": "https://schema.management.azure.com/schemas/2019-08-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources."}}, "container_group_name": {"type": "string", "defaultValue": "[uniqueString(resourceGroup().id)]", "metadata": {"description": "The name of the container group to create."}}, "container_name": {"type": "string", "defaultValue": "[uniqueString(resourceGroup().id)]", "metadata": {"description": "The name of the container to create."}}}, "resources": [{"type": "Microsoft.ContainerInstance/containerGroups", "apiVersion": "2022-09-01", "name": "[parameters('container_group_name')]", "location": "[parameters('location')]", "properties": {"containers": [{"name": "[parameters('container_name')]", "properties": {"image": "{{ image }}", "command": "{{ command }}", "resources": {"requests": {"cpu": "{{ cpu }}", "memoryInGB": "{{ memory }}"}}, "environmentVariables": []}}], "osType": "Linux", "restartPolicy": "Never"}}]}}, "variables": {"description": "Variables for an Azure Container Instance flow run.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "image": {"title": "Image", "description": "The image to use for the Prefect container in the task. This value defaults to a Prefect base image matching your local versions.", "type": "string"}, "resource_group_name": {"title": "Azure Resource Group Name", "description": "The name of the Azure Resource Group in which to run Prefect ACI tasks.", "type": "string"}, "subscription_id": {"title": "Azure Subscription ID", "description": "The ID of the Azure subscription to create containers under.", "type": "string", "writeOnly": true, "format": "password"}, "identities": {"title": "Identities", "description": "A list of user-assigned identities to associate with the container group. The identities should be an ARM resource IDs in the form: '/subscriptions/{subscriptionId}/resourceGroups/{resourceGroupName}/providers/Microsoft.ManagedIdentity/userAssignedIdentities/{identityName}'.", "type": "array", "items": {"type": "string"}}, "entrypoint": {"title": "Entrypoint", "description": "The entrypoint of the container you wish you run. This value defaults to the entrypoint used by Prefect images and should only be changed when using a custom image that is not based on an official Prefect image. Any commands set on deployments will be passed to the entrypoint as parameters.", "default": "/opt/prefect/entrypoint.sh", "type": "string"}, "image_registry": {"title": "Image Registry (Optional)", "description": "To use any private container registry with a username and password, choose DockerRegistry. To use a private Azure Container Registry with a managed identity, choose ACRManagedIdentity.", "anyOf": [{"$ref": "#/definitions/DockerRegistryCredentials"}, {"$ref": "#/definitions/ACRManagedIdentity"}]}, "cpu": {"title": "CPU", "description": "The number of virtual CPUs to assign to the task container. If not provided, a default value of 1.0 will be used.", "default": 1.0, "type": "number"}, "gpu_count": {"title": "GPU Count", "description": "The number of GPUs to assign to the task container. If not provided, no GPU will be used.", "type": "integer"}, "gpu_sku": {"title": "GPU SKU", "description": "The Azure GPU SKU to use. See the ACI documentation for a list of GPU SKUs available in each Azure region.", "type": "string"}, "memory": {"title": "Memory", "description": "The amount of memory in gigabytes to provide to the ACI task. Valid amounts are specified in the Azure documentation. If not provided, a default value of  1.0 will be used unless present on the task definition.", "default": 1.0, "type": "number"}, "subnet_ids": {"title": "Subnet IDs", "description": "A list of subnet IDs to associate with the container group. ", "type": "array", "items": {"type": "string"}}, "dns_servers": {"title": "DNS Servers", "description": "A list of DNS servers to associate with the container group.", "type": "array", "items": {"type": "string"}}, "aci_credentials": {"title": "Aci Credentials", "description": "The credentials to use to authenticate with Azure.", "allOf": [{"$ref": "#/definitions/AzureContainerInstanceCredentials"}]}, "stream_output": {"title": "Stream Output", "description": "If `True`, logs will be streamed from the Prefect container to the local console.", "default": false, "type": "boolean"}, "task_start_timeout_seconds": {"title": "Task Start Timeout Seconds", "description": "The amount of time to watch for the start of the ACI container. before marking it as failed.", "default": 240, "type": "integer"}, "task_watch_poll_interval": {"title": "Task Watch Poll Interval", "description": "The number of seconds to wait between Azure API calls while monitoring the state of an Azure Container Instances task.", "default": 5.0, "type": "number"}}, "required": ["resource_group_name", "subscription_id"], "definitions": {"DockerRegistryCredentials": {"title": "DockerRegistryCredentials", "description": "Connects to a Docker registry.\n\nRequires a Docker Engine to be connectable.", "type": "object", "properties": {"username": {"title": "Username", "description": "The username to log into the registry with.", "type": "string"}, "password": {"title": "Password", "description": "The password to log into the registry with.", "type": "string", "writeOnly": true, "format": "password"}, "registry_url": {"title": "Registry Url", "description": "The URL to the registry. Generally, \"http\" or \"https\" can be omitted.", "type": "string"}, "reauth": {"title": "<PERSON><PERSON><PERSON>", "description": "Whether or not to reauthenticate on each interaction.", "default": true, "type": "boolean"}}, "required": ["username", "password", "registry_url"], "block_type_slug": "docker-registry-credentials", "secret_fields": ["password"], "block_schema_references": {}}, "ACRManagedIdentity": {"title": "ACRManagedIdentity", "description": "Use a Managed Identity to access Azure Container registry. Requires the\nuser-assigned managed identity be available to the ACI container group.", "type": "object", "properties": {"registry_url": {"title": "Registry URL", "description": "The URL to the registry, such as myregistry.azurecr.io. Generally, 'http' or 'https' can be omitted.", "type": "string"}, "identity": {"title": "Identity", "description": "The user-assigned Azure managed identity for the private registry.", "type": "string"}}, "required": ["registry_url", "identity"]}, "AzureContainerInstanceCredentials": {"title": "AzureContainerInstanceCredentials", "description": "Block used to manage Azure Container Instances authentication. Stores Azure Service\nPrincipal authentication data.", "type": "object", "properties": {"client_id": {"title": "Client ID", "description": "The service principal client ID. If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.", "type": "string"}, "tenant_id": {"title": "Tenant ID", "description": "The service principal tenant ID.If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.", "type": "string"}, "client_secret": {"title": "Client Secret", "description": "The service principal client secret.If none of client_id, tenant_id, and client_secret are provided, will use DefaultAzureCredential; else will need to provide all three to use ClientSecretCredential.", "type": "string", "writeOnly": true, "format": "password"}, "credential_kwargs": {"title": "Additional Credential Keyword Arguments", "description": "Additional keyword arguments to pass to `ClientSecretCredential` or `DefaultAzureCredential`.", "type": "object"}}, "block_type_slug": "azure-container-instance-credentials", "secret_fields": ["client_secret"], "block_schema_references": {}}}}}, "description": "Execute flow runs within containers on Azure's Container Instances service. Requires an Azure account.", "display_name": "Azure Container Instances", "documentation_url": "https://prefecthq.github.io/prefect-azure/container_instance_worker/", "install_command": "pip install prefect-azure", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/54e3fa7e00197a4fbd1d82ed62494cb58d08c96a-250x250.png", "type": "azure-container-instance"}}, "prefect-docker": {"docker": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "image": "{{ image }}", "registry_credentials": "{{ registry_credentials }}", "image_pull_policy": "{{ image_pull_policy }}", "networks": "{{ networks }}", "network_mode": "{{ network_mode }}", "auto_remove": "{{ auto_remove }}", "volumes": "{{ volumes }}", "stream_output": "{{ stream_output }}", "mem_limit": "{{ mem_limit }}", "memswap_limit": "{{ memswap_limit }}", "privileged": "{{ privileged }}"}, "variables": {"description": "Configuration class used by the Docker worker.\n\nAn instance of this class is passed to the Docker worker's `run` method\nfor each flow run. It contains all the information necessary to execute the\nflow run as a Docker container.\n\nAttributes:\n    name: The name to give to created Docker containers.\n    command: The command executed in created Docker containers to kick off\n        flow run execution.\n    env: The environment variables to set in created Docker containers.\n    labels: The labels to set on created Docker containers.\n    image: The image reference of a container image to use for created jobs.\n        If not set, the latest Prefect image will be used.\n    image_pull_policy: The image pull policy to use when pulling images.\n    networks: Docker networks that created containers should be connected to.\n    network_mode: The network mode for the created containers (e.g. host, bridge).\n        If 'networks' is set, this cannot be set.\n    auto_remove: If set, containers will be deleted on completion.\n    volumes: Docker volumes that should be mounted in created containers.\n    stream_output: If set, the output from created containers will be streamed\n        to local standard output.\n    mem_limit: Memory limit of created containers. Accepts a value\n        with a unit identifier (e.g. 100000b, 1000k, 128m, 1g.) If a value is\n        given without a unit, bytes are assumed.\n    memswap_limit: Total memory (memory + swap), -1 to disable swap. Should only be\n        set if `mem_limit` is also set. If `mem_limit` is set, this defaults to\n        allowing the container to use as much swap as memory. For example, if\n        `mem_limit` is 300m and `memswap_limit` is not set, containers can use\n        600m in total of memory and swap.\n    privileged: Give extended privileges to created containers.", "type": "object", "properties": {"command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by the worker using this job configuration.", "type": "object", "additionalProperties": {"type": "string"}}, "name": {"title": "Name", "description": "Name given to infrastructure created by the worker using this job configuration.", "type": "string"}, "image": {"title": "Image", "description": "The image reference of a container image to use for created jobs. If not set, the latest Prefect image will be used.", "example": "docker.io/prefecthq/prefect:3-latest", "type": "string"}, "registry_credentials": {"title": "Registry Credentials", "description": "Credentials for logging into a Docker registry to pull images from.", "allOf": [{"$ref": "#/definitions/DockerRegistryCredentials"}]}, "image_pull_policy": {"title": "Image Pull Policy", "description": "The image pull policy to use when pulling images.", "enum": ["IfNotPresent", "Always", "Never"], "type": "string"}, "networks": {"title": "Networks", "description": "Docker networks that created containers should be connected to.", "type": "array", "items": {"type": "string"}}, "network_mode": {"title": "Network Mode", "description": "The network mode for the created containers (e.g. host, bridge). If 'networks' is set, this cannot be set.", "type": "string"}, "auto_remove": {"title": "Auto Remove", "description": "If set, containers will be deleted on completion.", "default": false, "type": "boolean"}, "volumes": {"title": "Volumes", "description": "A list of volume to mount into created containers.", "example": ["/my/local/path:/path/in/container"], "type": "array", "items": {"type": "string"}}, "stream_output": {"title": "Stream Output", "description": "If set, the output from created containers will be streamed to local standard output.", "default": true, "type": "boolean"}, "mem_limit": {"title": "Memory Limit", "description": "Memory limit of created containers. Accepts a value with a unit identifier (e.g. 100000b, 1000k, 128m, 1g.) If a value is given without a unit, bytes are assumed.", "type": "string"}, "memswap_limit": {"title": "Memory Swap <PERSON>", "description": "Total memory (memory + swap), -1 to disable swap. Should only be set if `mem_limit` is also set. If `mem_limit` is set, this defaults toallowing the container to use as much swap as memory. For example, if `mem_limit` is 300m and `memswap_limit` is not set, containers can use 600m in total of memory and swap.", "type": "string"}, "privileged": {"title": "Privileged", "description": "Give extended privileges to created container.", "default": false, "type": "boolean"}}, "definitions": {"DockerRegistryCredentials": {"title": "DockerRegistryCredentials", "description": "Store credentials for interacting with a Docker Registry.", "type": "object", "properties": {"username": {"title": "Username", "description": "The username to log into the registry with.", "type": "string"}, "password": {"title": "Password", "description": "The password to log into the registry with.", "type": "string", "writeOnly": true, "format": "password"}, "registry_url": {"title": "Registry Url", "description": "The URL to the registry. Generally, \"http\" or \"https\" can be omitted.", "example": "index.docker.io", "type": "string"}, "reauth": {"title": "<PERSON><PERSON><PERSON>", "description": "Whether or not to reauthenticate on each interaction.", "default": true, "type": "boolean"}}, "required": ["username", "password", "registry_url"], "block_type_slug": "docker-registry-credentials", "secret_fields": ["password"], "block_schema_references": {}}}}}, "description": "Execute flow runs within Docker containers. Works well for managing flow execution environments via Docker images. Requires access to a running Docker daemon.", "display_name": "<PERSON>er", "documentation_url": "https://prefecthq.github.io/prefect-docker/worker/", "install_command": "pip install prefect-docker", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/14a315b79990200db7341e42553e23650b34bb96-250x250.png", "type": "docker"}}, "prefect-gcp": {"cloud-run": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "region": "{{ region }}", "credentials": "{{ credentials }}", "job_body": {"apiVersion": "run.googleapis.com/v1", "kind": "Job", "metadata": {"name": "{{ name }}", "annotations": {"run.googleapis.com/launch-stage": "BETA"}}, "spec": {"template": {"spec": {"template": {"spec": {"containers": [{"image": "{{ image }}", "command": "{{ command }}", "resources": {"limits": {"cpu": "{{ cpu }}", "memory": "{{ memory }}"}, "requests": {"cpu": "{{ cpu }}", "memory": "{{ memory }}"}}}], "timeoutSeconds": "{{ timeout }}", "serviceAccountName": "{{ service_account_name }}"}}}, "metadata": {"annotations": {"run.googleapis.com/vpc-access-connector": "{{ vpc_connector_name }}"}}}}}, "timeout": "{{ timeout }}", "keep_job": "{{ keep_job }}"}, "variables": {"description": "Default variables for the Cloud Run worker.\n\nThe schema for this class is used to populate the `variables` section of the default\nbase job template.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "region": {"title": "Region", "description": "The region where the Cloud Run Job resides.", "default": "us-central1", "example": "us-central1", "type": "string"}, "credentials": {"title": "GCP Credentials", "description": "The GCP Credentials used to initiate the Cloud Run Job. If not provided credentials will be inferred from the local environment.", "allOf": [{"$ref": "#/definitions/GcpCredentials"}]}, "image": {"title": "Image Name", "description": "The image to use for a new Cloud Run Job. If not set, the latest Prefect image will be used. See https://cloud.google.com/run/docs/deploying#images.", "example": "docker.io/prefecthq/prefect:3-latest", "type": "string"}, "cpu": {"title": "CPU", "description": "The amount of compute allocated to the Cloud Run Job. (1000m = 1 CPU). See https://cloud.google.com/run/docs/configuring/cpu#setting-jobs.", "pattern": "^(\\d*000)m$", "example": "1000m", "type": "string"}, "memory": {"title": "Memory", "description": "The amount of memory allocated to the Cloud Run Job. Must be specified in units of 'G', 'Gi', 'M', or 'Mi'. See https://cloud.google.com/run/docs/configuring/memory-limits#setting.", "pattern": "^\\d+(?:G|Gi|M|Mi)$", "example": "512Mi", "type": "string"}, "vpc_connector_name": {"title": "VPC Connector Name", "description": "The name of the VPC connector to use for the Cloud Run Job.", "type": "string"}, "service_account_name": {"title": "Service Account Name", "description": "The name of the service account to use for the task execution of Cloud Run Job. By default Cloud Run jobs run as the default Compute Engine Service Account. ", "example": "<EMAIL>", "type": "string"}, "keep_job": {"title": "Keep Job After Completion", "description": "Keep the completed Cloud Run Job after it has run.", "default": false, "type": "boolean"}, "timeout": {"title": "Job Timeout", "description": "The length of time that Prefect will wait for Cloud Run Job state changes.", "default": 600, "exclusiveMinimum": 0, "maximum": 3600, "type": "integer"}}, "definitions": {"GcpCredentials": {"title": "GcpCredentials", "description": "Block used to manage authentication with GCP. Google authentication is\nhandled via the `google.oauth2` module or through the CLI.\nSpecify either one of service `account_file` or `service_account_info`; if both\nare not specified, the client will try to detect the credentials following Google's\n[Application Default Credentials](https://cloud.google.com/docs/authentication/application-default-credentials).\nSee Google's [Authentication documentation](https://cloud.google.com/docs/authentication#service-accounts)\nfor details on inference and recommended authentication patterns.", "type": "object", "properties": {"service_account_file": {"title": "Service Account File", "description": "Path to the service account JSON keyfile.", "type": "string", "format": "path"}, "service_account_info": {"title": "Service Account Info", "description": "The contents of the keyfile as a dict.", "type": "object"}, "project": {"title": "Project", "description": "The GCP project to use for the client.", "type": "string"}}, "block_type_slug": "gcp-credentials", "secret_fields": ["service_account_info.*"], "block_schema_references": {}}}}}, "description": "Execute flow runs within containers on Google Cloud Run. Requires a Google Cloud Platform account.", "display_name": "Google Cloud Run", "documentation_url": "https://prefecthq.github.io/prefect-gcp/cloud_run_worker/", "install_command": "pip install prefect-gcp", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/10424e311932e31c477ac2b9ef3d53cefbaad708-250x250.png", "type": "cloud-run"}, "cloud-run-v2": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "credentials": "{{ credentials }}", "job_body": {"client": "prefect", "launchStage": "{{ launch_stage }}", "template": {"template": {"serviceAccount": "{{ service_account_name }}", "maxRetries": "{{ max_retries }}", "timeout": "{{ timeout }}", "vpcAccess": {"connector": "{{ vpc_connector_name }}"}, "containers": [{"env": [], "image": "{{ image }}", "command": "{{ command }}", "args": "{{ args }}", "resources": {"limits": {"cpu": "{{ cpu }}", "memory": "{{ memory }}"}}}]}}}, "keep_job": "{{ keep_job }}", "region": "{{ region }}", "timeout": "{{ timeout }}"}, "variables": {"description": "Default variables for the Cloud Run worker V2.\n\nThe schema for this class is used to populate the `variables` section of the\ndefault base job template.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "credentials": {"title": "GCP Credentials", "description": "The GCP Credentials used to connect to Cloud Run. If not provided credentials will be inferred from the local environment.", "allOf": [{"$ref": "#/definitions/GcpCredentials"}]}, "region": {"title": "Region", "description": "The region in which to run the Cloud Run job", "default": "us-central1", "type": "string"}, "image": {"title": "Image Name", "description": "The image to use for the Cloud Run job. If not provided the default Prefect image will be used.", "default": "prefecthq/prefect:3-latest", "type": "string"}, "args": {"title": "<PERSON><PERSON><PERSON>", "description": "The arguments to pass to the Cloud Run Job V2's entrypoint command.", "type": "array", "items": {"type": "string"}}, "keep_job": {"title": "Keep Job After Completion", "description": "Keep the completed Cloud run job on Google Cloud Platform.", "default": false, "type": "boolean"}, "launch_stage": {"title": "Launch Stage", "description": "The launch stage of the Cloud Run Job V2. See https://cloud.google.com/run/docs/about-features-categories for additional details.", "default": "BETA", "enum": ["ALPHA", "BETA", "GA", "DEPRECATED", "EARLY_ACCESS", "PRELAUNCH", "UNIMPLEMENTED", "LAUNCH_TAG_UNSPECIFIED"], "type": "string"}, "max_retries": {"title": "Max Retries", "description": "The number of times to retry the Cloud Run job.", "default": 0, "type": "integer"}, "cpu": {"title": "CPU", "description": "The CPU to allocate to the Cloud Run job.", "default": "1000m", "type": "string"}, "memory": {"title": "Memory", "description": "The memory to allocate to the Cloud Run job along with the units, whichcould be: G, Gi, <PERSON>, Mi.", "default": "512Mi", "example": "512Mi", "pattern": "^\\d+(?:G|Gi|M|Mi)$", "type": "string"}, "timeout": {"title": "Job Timeout", "description": "The length of time that Prefect will wait for a Cloud Run Job to complete before raising an exception (maximum of 86400 seconds, 1 day).", "default": 600, "exclusiveMinimum": 0, "maximum": 86400, "type": "integer"}, "vpc_connector_name": {"title": "VPC Connector Name", "description": "The name of the VPC connector to use for the Cloud Run job.", "type": "string"}, "service_account_name": {"title": "Service Account Name", "description": "The name of the service account to use for the task execution of Cloud Run Job. By default Cloud Run jobs run as the default Compute Engine Service Account.", "example": "<EMAIL>", "type": "string"}}, "definitions": {"GcpCredentials": {"title": "GcpCredentials", "description": "Block used to manage authentication with GCP. Google authentication is\nhandled via the `google.oauth2` module or through the CLI.\nSpecify either one of service `account_file` or `service_account_info`; if both\nare not specified, the client will try to detect the credentials following Google's\n[Application Default Credentials](https://cloud.google.com/docs/authentication/application-default-credentials).\nSee Google's [Authentication documentation](https://cloud.google.com/docs/authentication#service-accounts)\nfor details on inference and recommended authentication patterns.", "type": "object", "properties": {"service_account_file": {"title": "Service Account File", "description": "Path to the service account JSON keyfile.", "type": "string", "format": "path"}, "service_account_info": {"title": "Service Account Info", "description": "The contents of the keyfile as a dict.", "type": "object"}, "project": {"title": "Project", "description": "The GCP project to use for the client.", "type": "string"}}, "block_type_slug": "gcp-credentials", "secret_fields": ["service_account_info.*"], "block_schema_references": {}}}}}, "description": "Execute flow runs within containers on Google Cloud Run (V2 API). Requires a Google Cloud Platform account.", "display_name": "Google Cloud Run V2", "documentation_url": "https://prefecthq.github.io/prefect-gcp/worker_v2/", "install_command": "pip install prefect-gcp", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/10424e311932e31c477ac2b9ef3d53cefbaad708-250x250.png", "type": "cloud-run-v2"}, "vertex-ai": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "region": "{{ region }}", "credentials": "{{ credentials }}", "job_spec": {"service_account_name": "{{ service_account_name }}", "network": "{{ network }}", "reserved_ip_ranges": "{{ reserved_ip_ranges }}", "maximum_run_time_hours": "{{ maximum_run_time_hours }}", "worker_pool_specs": [{"replica_count": 1, "container_spec": {"image_uri": "{{ image }}", "command": "{{ command }}", "args": []}, "machine_spec": {"machine_type": "{{ machine_type }}", "accelerator_type": "{{ accelerator_type }}", "accelerator_count": "{{ accelerator_count }}"}, "disk_spec": {"boot_disk_type": "{{ boot_disk_type }}", "boot_disk_size_gb": "{{ boot_disk_size_gb }}"}}]}, "job_watch_poll_interval": "{{ job_watch_poll_interval }}"}, "variables": {"description": "Default variables for the Vertex AI worker.\n\nThe schema for this class is used to populate the `variables` section of the default\nbase job template.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "region": {"title": "Region", "description": "The region where the Vertex AI Job resides.", "example": "us-central1", "type": "string"}, "image": {"title": "Image Name", "description": "The URI of a container image in the Container or Artifact Registry, used to run your Vertex AI Job. Note that Vertex AI will need accessto the project and region where the container image is stored. See https://cloud.google.com/vertex-ai/docs/training/create-custom-container", "example": "gcr.io/your-project/your-repo:latest", "type": "string"}, "credentials": {"title": "GCP Credentials", "description": "The GCP Credentials used to initiate the Vertex AI Job. If not provided credentials will be inferred from the local environment.", "allOf": [{"$ref": "#/definitions/GcpCredentials"}]}, "machine_type": {"title": "Machine Type", "description": "The machine type to use for the run, which controls the available CPU and memory. See https://cloud.google.com/vertex-ai/docs/reference/rest/v1/MachineSpec", "default": "n1-standard-4", "type": "string"}, "accelerator_type": {"title": "Accelerator Type", "description": "The type of accelerator to attach to the machine. See https://cloud.google.com/vertex-ai/docs/reference/rest/v1/MachineSpec", "example": "NVIDIA_TESLA_K80", "type": "string"}, "accelerator_count": {"title": "Accelerator Count", "description": "The number of accelerators to attach to the machine. See https://cloud.google.com/vertex-ai/docs/reference/rest/v1/MachineSpec", "example": 1, "type": "integer"}, "boot_disk_type": {"title": "Boot Disk Type", "description": "The type of boot disk to attach to the machine.", "default": "pd-ssd", "type": "string"}, "boot_disk_size_gb": {"title": "Boot Disk Size (GB)", "description": "The size of the boot disk to attach to the machine, in gigabytes.", "default": 100, "type": "integer"}, "maximum_run_time_hours": {"title": "Maximum Run Time (Hours)", "description": "The maximum job running time, in hours", "default": 1, "type": "integer"}, "network": {"title": "Network", "description": "The full name of the Compute Engine networkto which the Job should be peered. Private services access must already be configured for the network. If left unspecified, the job is not peered with any network. For example: projects/12345/global/networks/myVPC", "type": "string"}, "reserved_ip_ranges": {"title": "Reserved IP Ranges", "description": "A list of names for the reserved ip ranges under the VPC network that can be used for this job. If set, we will deploy the job within the provided ip ranges. Otherwise, the job will be deployed to any ip ranges under the provided VPC network.", "type": "array", "items": {"type": "string"}}, "service_account_name": {"title": "Service Account Name", "description": "Specifies the service account to use as the run-as account in Vertex AI. The worker submitting jobs must have act-as permission on this run-as account. If unspecified, the AI Platform Custom Code Service Agent for the CustomJob's project is used. Takes precedence over the service account found in GCP credentials, and required if a service account cannot be detected in GCP credentials.", "type": "string"}, "job_watch_poll_interval": {"title": "Poll Interval (Seconds)", "description": "The amount of time to wait between GCP API calls while monitoring the state of a Vertex AI Job.", "default": 5.0, "type": "number"}}, "required": ["region", "image"], "definitions": {"GcpCredentials": {"title": "GcpCredentials", "description": "Block used to manage authentication with GCP. Google authentication is\nhandled via the `google.oauth2` module or through the CLI.\nSpecify either one of service `account_file` or `service_account_info`; if both\nare not specified, the client will try to detect the credentials following Google's\n[Application Default Credentials](https://cloud.google.com/docs/authentication/application-default-credentials).\nSee Google's [Authentication documentation](https://cloud.google.com/docs/authentication#service-accounts)\nfor details on inference and recommended authentication patterns.", "type": "object", "properties": {"service_account_file": {"title": "Service Account File", "description": "Path to the service account JSON keyfile.", "type": "string", "format": "path"}, "service_account_info": {"title": "Service Account Info", "description": "The contents of the keyfile as a dict.", "type": "object"}, "project": {"title": "Project", "description": "The GCP project to use for the client.", "type": "string"}}, "block_type_slug": "gcp-credentials", "secret_fields": ["service_account_info.*"], "block_schema_references": {}}}}}, "description": "Execute flow runs within containers on Google Vertex AI. Requires a Google Cloud Platform account.", "display_name": "Google Vertex AI", "documentation_url": "https://prefecthq.github.io/prefect-gcp/vertex_worker/", "install_command": "pip install prefect-gcp", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/10424e311932e31c477ac2b9ef3d53cefbaad708-250x250.png", "type": "vertex-ai"}}, "prefect-kubernetes": {"kubernetes": {"default_base_job_configuration": {"job_configuration": {"command": "{{ command }}", "env": "{{ env }}", "labels": "{{ labels }}", "name": "{{ name }}", "namespace": "{{ namespace }}", "job_manifest": {"apiVersion": "batch/v1", "kind": "Job", "metadata": {"labels": "{{ labels }}", "namespace": "{{ namespace }}", "generateName": "{{ name }}-"}, "spec": {"backoffLimit": 0, "ttlSecondsAfterFinished": "{{ finished_job_ttl }}", "template": {"spec": {"parallelism": 1, "completions": 1, "restartPolicy": "Never", "serviceAccountName": "{{ service_account_name }}", "containers": [{"name": "prefect-job", "env": "{{ env }}", "image": "{{ image }}", "imagePullPolicy": "{{ image_pull_policy }}", "args": "{{ command }}"}]}}}}, "cluster_config": "{{ cluster_config }}", "job_watch_timeout_seconds": "{{ job_watch_timeout_seconds }}", "pod_watch_timeout_seconds": "{{ pod_watch_timeout_seconds }}", "stream_output": "{{ stream_output }}"}, "variables": {"description": "Default variables for the Kubernetes worker.\n\nThe schema for this class is used to populate the `variables` section of the default\nbase job template.", "type": "object", "properties": {"name": {"title": "Name", "description": "Name given to infrastructure created by a worker.", "type": "string"}, "env": {"title": "Environment Variables", "description": "Environment variables to set when starting a flow run.", "type": "object", "additionalProperties": {"type": "string"}}, "labels": {"title": "Labels", "description": "Labels applied to infrastructure created by a worker.", "type": "object", "additionalProperties": {"type": "string"}}, "command": {"title": "Command", "description": "The command to use when starting a flow run. In most cases, this should be left blank and the command will be automatically generated by the worker.", "type": "string"}, "namespace": {"title": "Namespace", "description": "The Kubernetes namespace to create jobs within.", "default": "default", "type": "string"}, "image": {"title": "Image", "description": "The image reference of a container image to use for created jobs. If not set, the latest Prefect image will be used.", "example": "docker.io/prefecthq/prefect:3-latest", "type": "string"}, "service_account_name": {"title": "Service Account Name", "description": "The Kubernetes service account to use for job creation.", "type": "string"}, "image_pull_policy": {"title": "Image Pull Policy", "description": "The Kubernetes image pull policy to use for job containers.", "default": "IfNotPresent", "enum": ["IfNotPresent", "Always", "Never"], "type": "string"}, "finished_job_ttl": {"title": "Finished Job TTL", "description": "The number of seconds to retain jobs after completion. If set, finished jobs will be cleaned up by Kubernetes after the given delay. If not set, jobs will be retained indefinitely.", "type": "integer"}, "job_watch_timeout_seconds": {"title": "Job Watch Timeout Seconds", "description": "Number of seconds to wait for each event emitted by a job before timing out. If not set, the worker will wait for each event indefinitely.", "type": "integer"}, "pod_watch_timeout_seconds": {"title": "Pod Watch Timeout Seconds", "description": "Number of seconds to watch for pod creation before timing out.", "default": 60, "type": "integer"}, "stream_output": {"title": "Stream Output", "description": "If set, output will be streamed from the job to local standard output.", "default": true, "type": "boolean"}, "cluster_config": {"title": "Cluster Config", "description": "The Kubernetes cluster config to use for job creation.", "allOf": [{"$ref": "#/definitions/KubernetesClusterConfig"}]}}, "definitions": {"KubernetesClusterConfig": {"title": "KubernetesClusterConfig", "description": "Stores configuration for interaction with Kubernetes clusters.\n\nSee `from_file` for creation.", "type": "object", "properties": {"config": {"title": "Config", "description": "The entire contents of a kubectl config file.", "type": "object"}, "context_name": {"title": "Context Name", "description": "The name of the kubectl context to use.", "type": "string"}}, "required": ["config", "context_name"], "block_type_slug": "kubernetes-cluster-config", "secret_fields": [], "block_schema_references": {}}}}}, "description": "Execute flow runs within jobs scheduled on a Kubernetes cluster. Requires a Kubernetes cluster.", "display_name": "Kubernetes", "documentation_url": "https://prefecthq.github.io/prefect-kubernetes/worker/", "install_command": "pip install prefect-kubernetes", "is_beta": false, "logo_url": "https://cdn.sanity.io/images/3ugk85nk/production/2d0b896006ad463b49c28aaac14f31e00e32cfab-250x250.png", "type": "kubernetes"}}}