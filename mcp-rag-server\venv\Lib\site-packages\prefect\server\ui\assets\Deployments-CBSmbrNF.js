import{d,e as i,f as y,g as o,i as _,c as a,o as t,j as n,q as f,a as g,F as k,n as s,bV as b,bS as h,k as v,bW as x}from"./index-ei-kaitd.js";import{u as C}from"./usePageTitle-LeBMnqrg.js";const S=d({__name:"Deployments",setup(D){const l=i(),c={interval:3e4},e=y(l.deployments.getDeployments,[{}],c),p=o(()=>e.response??[]),r=o(()=>e.executed&&p.value.length===0),m=o(()=>e.executed);return C("Deployments"),(B,N)=>{const u=_("p-layout-default");return t(),a(u,{class:"deployments"},{header:n(()=>[v(s(x))]),default:n(()=>[m.value?(t(),f(k,{key:0},[r.value?(t(),a(s(b),{key:0})):(t(),a(s(h),{key:1,onDelete:s(e).refresh},null,8,["onDelete"]))],64)):g("",!0)]),_:1})}}});export{S as default};
//# sourceMappingURL=Deployments-CBSmbrNF.js.map
