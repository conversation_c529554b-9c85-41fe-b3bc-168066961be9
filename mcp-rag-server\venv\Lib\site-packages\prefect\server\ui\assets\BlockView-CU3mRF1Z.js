import{d as b,e as d,u as i,W as _,g as o,bF as f,i as v,c as D,a as B,o as C,j as c,k as n,n as s,cs as g,ct as h,be as x}from"./index-ei-kaitd.js";import{u as w}from"./usePageTitle-LeBMnqrg.js";const T=b({__name:"BlockView",setup(y){const u=d(),a=i(),t=_("blockDocumentId"),l=o(()=>t.value?[t.value]:null),r=f(u.blockDocuments.getBlockDocument,l),e=o(()=>r.response),m=()=>{a.push(x.blocks())},k=o(()=>e.value?`Block: ${e.value.name}`:"Block");return w(k),(I,V)=>{const p=v("p-layout-default");return e.value?(C(),D(p,{key:0,class:"block-view"},{header:c(()=>[n(s(h),{"block-document":e.value,onDelete:m},null,8,["block-document"])]),default:c(()=>[n(s(g),{"block-document":e.value},null,8,["block-document"])]),_:1})):B("",!0)}}});export{T as default};
//# sourceMappingURL=BlockView-CU3mRF1Z.js.map
