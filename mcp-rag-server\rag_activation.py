#!/usr/bin/env python3
"""RAG System Activation - FINAL VERSION."""

import asyncio
import time
import json
import numpy as np
from config.settings import get_settings


async def create_chunks():
    """Create chunks from eufunds documents."""
    print('🧩 CREATING CHUNKS FROM EUFUNDS DOCUMENTS')
    print('=' * 50)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        
        # Get eufunds documents
        print('📄 Getting eufunds documents...')
        docs_result = client.table('documents').select('*').ilike('url', '%eufunds%').execute()
        documents = docs_result.data
        
        print(f'🇪🇺 Found {len(documents)} eufunds documents')
        
        if not documents:
            print('❌ No eufunds documents found!')
            return 0
        
        # Process each document
        total_chunks = 0
        
        for doc in documents:
            doc_id = doc['id']
            content = doc.get('content', '')
            url = doc.get('url', '')
            
            if not content or not content.strip():
                continue
            
            print(f'   📄 Processing: {url}')
            
            # Simple chunking
            clean_content = ' '.join(content.split())
            chunks = []
            chunk_size = 800
            overlap = 200
            
            start = 0
            chunk_index = 0
            
            while start < len(clean_content):
                end = start + chunk_size
                
                if end < len(clean_content):
                    # Find sentence end
                    for i in range(end, max(start + chunk_size//2, end - 100), -1):
                        if clean_content[i] in '.!?':
                            end = i + 1
                            break
                
                chunk_content = clean_content[start:end].strip()
                
                if chunk_content:
                    chunk_data = {
                        'document_id': doc_id,
                        'content': chunk_content,
                        'content_length': len(chunk_content),
                        'chunk_index': chunk_index,
                        'start_position': start,
                        'end_position': end,
                        'embedding_model': 'text-embedding-3-small',
                        'metadata': {'source_url': url}
                    }
                    chunks.append(chunk_data)
                    chunk_index += 1
                
                start = max(start + chunk_size - overlap, end)
                if start >= len(clean_content):
                    break
            
            # Insert chunks
            if chunks:
                try:
                    result = client.table('chunks').insert(chunks).execute()
                    doc_chunks = len(result.data) if result.data else len(chunks)
                    total_chunks += doc_chunks
                    print(f'     ✅ Created {doc_chunks} chunks')
                except Exception as e:
                    print(f'     ❌ Error: {e}')
        
        print(f'\n📊 Total chunks created: {total_chunks}')
        return total_chunks
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return 0


async def generate_embeddings():
    """Generate embeddings for chunks."""
    print(f'\n🤖 GENERATING EMBEDDINGS')
    print('=' * 30)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        import openai
        
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        openai.api_key = settings.openai_api_key
        
        # Get chunks without embeddings
        chunks_result = client.table('chunks').select('id, content').is_('embedding', 'null').limit(20).execute()
        chunks = chunks_result.data
        
        print(f'🧩 Found {len(chunks)} chunks to process')
        
        if not chunks:
            print('✅ All chunks already have embeddings!')
            return 0
        
        embeddings_created = 0
        
        for i, chunk in enumerate(chunks, 1):
            chunk_id = chunk['id']
            content = chunk['content']
            
            print(f'   🤖 Processing chunk {i}/{len(chunks)}...')
            
            try:
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=content
                )
                
                embedding = response.data[0].embedding
                embedding_json = json.dumps(embedding)
                
                update_result = client.table('chunks').update({
                    'embedding': embedding_json,
                    'embedding_model': 'text-embedding-3-small'
                }).eq('id', chunk_id).execute()
                
                if update_result.data:
                    embeddings_created += 1
                    print(f'     ✅ Embedding created ({len(embedding)} dims)')
                
                await asyncio.sleep(0.1)
                
            except Exception as e:
                print(f'     ❌ Error: {e}')
        
        print(f'\n📊 Embeddings created: {embeddings_created}')
        return embeddings_created
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return 0


async def test_rag():
    """Test RAG search."""
    print(f'\n🔍 TESTING RAG SEARCH')
    print('=' * 30)
    
    settings = get_settings()
    
    try:
        from supabase import create_client
        import openai
        
        client = create_client(settings.supabase_url, settings.supabase_service_key)
        openai.api_key = settings.openai_api_key
        
        test_questions = [
            "Какви са условията за кандидатстване за европейски фондове?",
            "Кои са основните програми за финансиране в България?",
            "Как мога да се свържа с отговорните лица?",
        ]
        
        successful_searches = 0
        
        for i, question in enumerate(test_questions, 1):
            print(f'\n❓ Question {i}: {question}')
            
            try:
                # Generate question embedding
                response = openai.embeddings.create(
                    model="text-embedding-3-small",
                    input=question
                )
                question_embedding = response.data[0].embedding
                
                # Get chunks with embeddings
                chunks_result = client.table('chunks').select('id, content, embedding').not_.is_('embedding', 'null').execute()
                chunks = chunks_result.data
                
                if not chunks:
                    print('   ❌ No chunks with embeddings found!')
                    continue
                
                # Calculate similarities
                similarities = []
                
                for chunk in chunks:
                    try:
                        chunk_embedding = json.loads(chunk['embedding'])
                        
                        # Cosine similarity
                        dot_product = np.dot(question_embedding, chunk_embedding)
                        norm_a = np.linalg.norm(question_embedding)
                        norm_b = np.linalg.norm(chunk_embedding)
                        similarity = dot_product / (norm_a * norm_b)
                        
                        similarities.append((chunk, similarity))
                        
                    except Exception as e:
                        continue
                
                # Sort by similarity
                similarities.sort(key=lambda x: x[1], reverse=True)
                top_results = similarities[:3]
                
                if top_results and top_results[0][1] > 0.7:
                    successful_searches += 1
                    print(f'   ✅ Found relevant chunks')
                    
                    for j, (chunk, similarity) in enumerate(top_results, 1):
                        content = chunk['content']
                        preview = content[:100] + "..." if len(content) > 100 else content
                        print(f'     {j}. Similarity: {similarity:.3f} - "{preview}"')
                else:
                    best_sim = top_results[0][1] if top_results else 0
                    print(f'   ❌ No relevant chunks found (best: {best_sim:.3f})')
                
            except Exception as e:
                print(f'   ❌ Search error: {e}')
        
        success_rate = (successful_searches / len(test_questions)) * 100
        print(f'\n📊 Success rate: {success_rate:.1f}%')
        
        return success_rate
        
    except Exception as e:
        print(f'❌ Error: {e}')
        return 0


async def main():
    """Main function."""
    print('🚀 RAG SYSTEM ACTIVATION')
    print('=' * 50)
    
    start_time = time.time()
    
    try:
        # Step 1: Create chunks
        total_chunks = await create_chunks()
        
        if total_chunks > 0:
            # Step 2: Generate embeddings
            embeddings_created = await generate_embeddings()
            
            if embeddings_created > 0:
                # Step 3: Test RAG
                success_rate = await test_rag()
                
                # Results
                total_time = time.time() - start_time
                
                print(f'\n🎯 FINAL RESULTS')
                print('=' * 20)
                print(f'⏱️  Time: {total_time:.2f}s')
                print(f'🧩 Chunks: {total_chunks}')
                print(f'🤖 Embeddings: {embeddings_created}')
                print(f'🔍 Success rate: {success_rate:.1f}%')
                
                if success_rate >= 60:
                    print('\n🟢 SUCCESS! RAG SYSTEM WORKING!')
                else:
                    print('\n🟡 PARTIAL SUCCESS')
            else:
                print('\n❌ FAILED - No embeddings')
        else:
            print('\n❌ FAILED - No chunks')
            
    except Exception as e:
        print(f'❌ Error: {e}')


if __name__ == "__main__":
    asyncio.run(main())
