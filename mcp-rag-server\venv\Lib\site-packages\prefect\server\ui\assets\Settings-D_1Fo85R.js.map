{"version": 3, "file": "Settings-D_1Fo85R.js", "sources": ["../../src/components/SettingsCodeBlock.vue", "../../src/pages/Settings.vue"], "sourcesContent": ["<template>\n  <div class=\"settings-block\">\n    <p-code multiline>\n      <div v-for=\"(section, index) in settingSections\" :key=\"index\" class=\"settings-block--code-line\">\n        {{ section[0] }}: {{ section[1] }}\n      </div>\n    </p-code>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PCode } from '@prefecthq/prefect-design'\n  import { computed } from 'vue'\n  import { ServerSettings } from '@/models/ServerSettings'\n\n  const props = defineProps<{\n    engineSettings: ServerSettings,\n  }>()\n  const settingSections = computed(() => Object.entries(props.engineSettings))\n</script>\n\n<style>\n.settings-block--code-line {\n  @apply whitespace-pre-wrap;\n}\n</style>\n", "<template>\n  <p-layout-default class=\"settings\">\n    <template #header>\n      <PageHeading :crumbs=\"crumbs\">\n        <template #actions>\n          <p-key-value class=\"settings__version\" label=\"Version\" :value=\"version\" alternate />\n        </template>\n      </PageHeading>\n    </template>\n\n    <p-label label=\"Theme\">\n      <p-theme-toggle />\n    </p-label>\n\n    <p-label label=\"Color Mode\" class=\"settings__color-mode\">\n      <ColorModeSelect v-model:selected=\"activeColorMode\" />\n    </p-label>\n\n    <p-label label=\"Server Settings\">\n      <SettingsCodeBlock class=\"settings__code-block\" :engine-settings=\"engineSettings\" />\n    </p-label>\n  </p-layout-default>\n</template>\n\n<script lang=\"ts\" setup>\n  import { PageHeading, ColorModeSelect } from '@prefecthq/prefect-ui-library'\n  import SettingsCodeBlock from '@/components/SettingsCodeBlock.vue'\n  import { usePageTitle } from '@/compositions/usePageTitle'\n  import { usePrefectApi } from '@/compositions/usePrefectApi'\n  import { activeColorMode } from '@/utilities/colorMode'\n\n  const crumbs = [{ text: 'Settings' }]\n\n  const api = usePrefectApi()\n  const [engineSettings, version] = await Promise.all([\n    api.admin.getSettings(),\n    api.admin.getVersion(),\n  ])\n\n  usePageTitle('Settings')\n</script>\n\n<style>\n.settings__version { @apply\n  w-auto\n}\n\n.settings__color-mode { @apply\n  w-96\n  max-w-full\n}\n\n.settings__code-block { @apply\n  max-w-full\n  overflow-x-auto\n}\n</style>"], "names": ["props", "__props", "settingSections", "computed", "_openBlock", "_createElementBlock", "_hoisted_1", "_createVNode", "_unref", "PCode", "_Fragment", "_renderList", "section", "index", "_toDisplayString", "crumbs", "api", "usePrefectApi", "engineSettings", "version", "__temp", "__restore", "_withAsyncContext", "usePageTitle", "_createBlock", "_component_p_layout_default", "PageHeading", "_component_p_key_value", "_component_p_label", "_component_p_theme_toggle", "ColorModeSelect", "activeColorMode", "$event", "SettingsCodeBlock"], "mappings": "wZAeE,MAAMA,EAAQC,EAGRC,EAAkBC,EAAS,IAAM,OAAO,QAAQH,EAAM,cAAc,CAAC,gBAjB3EI,EAAA,EAAAC,EAMM,MANNC,EAMM,CALJC,EAISC,EAAAC,CAAA,EAAA,CAJD,UAAA,IAAS,WACV,IAA2C,EAAhDL,EAAA,EAAA,EAAAC,EAEMK,EAF0B,KAAAC,EAAAT,EAAA,MAAnB,CAAAU,EAASC,SAAtBR,EAEM,MAAA,CAF4C,IAAKQ,EAAO,MAAM,+BAC/DD,EAAO,CAAA,CAAA,EAAM,KAAEE,EAAGF,EAAO,CAAA,CAAA,EAAA,CAAA,sEC2BlC,MAAMG,EAAS,CAAC,CAAE,KAAM,WAAY,EAE9BC,EAAMC,EAAc,EACpB,CAACC,EAAgBC,CAAO,GAAU,CAAAC,EAAAC,CAAA,EAAAC,EAAA,IAAA,QAAQ,IAAI,CAClDN,EAAI,MAAM,YAAY,EACtBA,EAAI,MAAM,WAAW,CAAA,CACtB,CAAA,mBAED,OAAAO,EAAa,UAAU,yGAtCvB,EAAAC,EAoBmBC,EAAA,CApBD,MAAM,YAAU,CACrB,SACT,IAIc,CAJdlB,EAIcC,EAAAkB,CAAA,EAAA,CAJA,OAAAX,GAAc,CACf,UACT,IAAoF,CAApFR,EAAoFoB,EAAA,CAAvE,MAAM,oBAAoB,MAAM,UAAW,MAAOnB,EAAOW,CAAA,EAAE,UAAA,EAAA,wCAK9E,IAEU,CAFVZ,EAEUqB,EAAA,CAFD,MAAM,SAAO,WACpB,IAAkB,CAAlBrB,EAAkBsB,CAAA,CAAA,SAGpBtB,EAEUqB,EAAA,CAFD,MAAM,aAAa,MAAM,sBAAA,aAChC,IAAsD,CAAtDrB,EAAsDC,EAAAsB,CAAA,EAAA,CAA7B,SAAUtB,EAAeuB,CAAA,0CAAfA,EAAe,MAAAC,EAAA,KAAA,+BAGpDzB,EAEUqB,EAAA,CAFD,MAAM,mBAAiB,WAC9B,IAAoF,CAApFrB,EAAoF0B,EAAA,CAAjE,MAAM,uBAAwB,kBAAiBzB,EAAcU,CAAA,CAAA"}