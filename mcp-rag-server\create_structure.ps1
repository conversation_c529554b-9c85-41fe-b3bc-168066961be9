# PowerShell script to create project structure
Write-Host "Creating MCP RAG Server project structure..."

# Core architecture
New-Item -ItemType Directory -Path "core\domain\entities" -Force | Out-Null
New-Item -ItemType Directory -Path "core\domain\repositories" -Force | Out-Null
New-Item -ItemType Directory -Path "core\application\services" -Force | Out-Null
New-Item -ItemType Directory -Path "core\application\use_cases" -Force | Out-Null
New-Item -ItemType Directory -Path "core\infrastructure\database" -Force | Out-Null
New-Item -ItemType Directory -Path "core\infrastructure\external" -Force | Out-Null

# Crawlers
New-Item -ItemType Directory -Path "crawlers\html" -Force | Out-Null
New-Item -ItemType Directory -Path "crawlers\pdf" -Force | Out-Null
New-Item -ItemType Directory -Path "crawlers\hybrid" -Force | Out-Null

# API
New-Item -ItemType Directory -Path "api\mcp" -Force | Out-Null
New-Item -ItemType Directory -Path "api\health" -Force | Out-Null
New-Item -ItemType Directory -Path "api\middleware" -Force | Out-Null

# Workflows
New-Item -ItemType Directory -Path "workflows\prefect" -Force | Out-Null

# Config
New-Item -ItemType Directory -Path "config\settings" -Force | Out-Null
New-Item -ItemType Directory -Path "config\database" -Force | Out-Null

# Tests
New-Item -ItemType Directory -Path "tests\unit\core" -Force | Out-Null
New-Item -ItemType Directory -Path "tests\integration" -Force | Out-Null
New-Item -ItemType Directory -Path "tests\e2e" -Force | Out-Null

# Monitoring
New-Item -ItemType Directory -Path "monitoring\prometheus" -Force | Out-Null
New-Item -ItemType Directory -Path "monitoring\logging" -Force | Out-Null

# Deployment
New-Item -ItemType Directory -Path "deployment\docker" -Force | Out-Null
New-Item -ItemType Directory -Path "deployment\scripts" -Force | Out-Null

# Documentation
New-Item -ItemType Directory -Path "docs\api" -Force | Out-Null
New-Item -ItemType Directory -Path "docs\architecture" -Force | Out-Null

# Scripts
New-Item -ItemType Directory -Path "scripts\setup" -Force | Out-Null
New-Item -ItemType Directory -Path "scripts\maintenance" -Force | Out-Null

Write-Host "✅ All directories created successfully!"
