# MCP RAG Server

A production-ready Model Context Protocol (MCP) server with Retrieval-Augmented Generation (RAG) capabilities.

## 🚀 Features

- **Advanced Web Crawling**: Hybrid approach with aiohttp, Scrapy, and Playwright
- **Vector Search**: Supabase with pgvector and HNSW indexing
- **Document Processing**: PDF, HTML, and text processing with embeddings
- **MCP Protocol**: Full Model Context Protocol implementation
- **Production Ready**: Monitoring, logging, error handling, and scalability
- **Clean Architecture**: Domain-driven design with proper separation of concerns

## 📋 Requirements

- Python 3.11+
- PostgreSQL with pgvector extension
- Redis
- Supabase account (or local PostgreSQL)
- OpenAI API key

## 🛠️ Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd mcp-rag-server
```

2. Create virtual environment:
```bash
python -m venv venv
source venv/bin/activate  # Linux/Mac
# venv\Scripts\activate   # Windows
```

3. Install dependencies:
```bash
pip install -e .
```

4. Configure environment:
```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Initialize database:
```bash
python scripts/setup/init_database.py
```

## 🚀 Quick Start

1. Start the MCP server:
```bash
uvicorn main:app --host 0.0.0.0 --port 8000
```

2. Test the health endpoint:
```bash
curl http://localhost:8000/health
```

3. Start crawling:
```bash
python scripts/crawl.py --url https://example.com
```

## 📖 Documentation

- [Architecture Overview](docs/architecture/)
- [API Documentation](docs/api/)
- [Deployment Guide](docs/deployment/)

## 🧪 Testing

Run all tests:
```bash
pytest
```

Run specific test types:
```bash
pytest tests/unit/          # Unit tests
pytest tests/integration/   # Integration tests
pytest tests/e2e/          # End-to-end tests
```

## 📈 Monitoring

- Prometheus metrics: http://localhost:9090/metrics
- Health checks: http://localhost:8000/health
- API documentation: http://localhost:8000/docs

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📜 License

MIT License - see LICENSE file for details.
