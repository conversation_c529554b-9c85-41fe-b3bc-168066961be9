ФАЗА 2: PRODUCTION READY - Детайлен План за Augment Code
🚨 КРИТИЧНИ ПРАВИЛА ЗА ИЗПЪЛНЕНИЕ
‼️ ЗАДЪЛЖИТЕЛНИ ИЗИСКВАНИЯ:
✅ ВСЯКА ЗАДАЧА трябва да има валидационни квадратчета
‼️ ЗАБРАНЕНО е преминаване без 100% потвърждение
🔍 ИЗИСКВАНЕ: Реално тестване на всяка функционалност
🚫 СТРОГО ЗАБРАНЕНО: Лъжене или измисляне на резултати
📝 ЗАДЪЛЖИТЕЛНО: Отметка [✅] след всяка завършена задача
ПРЕДПОСТАВКА: Фаза 1 трябва да е 100% завършена преди започване на Фаза 2
📅 СЕДМИЦА 4: ADVANCED CRAWLING & PROCESSING
ЗАДАЧА 2.1: Database Connection Manager (КРИТИЧНО)
Стъпка 2.1.1: Създаване на DatabaseManager
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > core/infrastructure/database/manager.py << 'DB_MANAGER_EOF'
"""
Database Connection Manager with Singleton pattern and connection pooling.
КРИТИЧНО: Решава проблема с connection limits в Supabase.
"""

import threading
import asyncio
import logging
from typing import Optional, Dict, Any, List
from contextlib import asynccontextmanager
from enum import Enum
import time

from supabase import create_client, Client
from config.settings.base import settings


class ConnectionType(Enum):
    """Database connection types."""
    READ_ONLY = "readonly"
    READ_WRITE = "readwrite"
    ADMIN = "admin"


class DatabaseManager:
    """Singleton Database Manager with connection pooling."""
    
    _instance: Optional['DatabaseManager'] = None
    _lock = threading.Lock()
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if not self._initialized:
            self.logger = logging.getLogger(self.__class__.__name__)
            self._supabase_client: Optional[Client] = None
            self._readonly_client: Optional[Client] = None
            self._connection_count = 0
            self._max_connections = settings.database_pool_size
            self._connection_lock = asyncio.Lock()
            self._health_status = {"status": "unknown", "last_check": 0}
            DatabaseManager._initialized = True
    
    async def initialize_pools(self) -> bool:
        """Initialize database connection pools."""
        try:
            self.logger.info("Initializing database connection pools...")
            
            # Main client for read-write operations
            self._supabase_client = create_client(
                settings.supabase_url,
                settings.supabase_service_key
            )
            
            # Read-only client for search operations
            if settings.supabase_anon_key:
                self._readonly_client = create_client(
                    settings.supabase_url,
                    settings.supabase_anon_key
                )
            
            # Test connections
            await self._test_connections()
            
            self.logger.info("Database connection pools initialized successfully")
            return True
            
        except Exception as e:
            self.logger.error(f"Failed to initialize database pools: {e}")
            return False
    
    async def _test_connections(self):
        """Test database connections."""
        try:
            # Test main connection
            result = self._supabase_client.table('raw_pages').select('id').limit(1).execute()
            self.logger.info("Main database connection test: OK")
            
            # Test readonly connection if available
            if self._readonly_client:
                result = self._readonly_client.table('raw_pages').select('id').limit(1).execute()
                self.logger.info("Readonly database connection test: OK")
                
        except Exception as e:
            self.logger.warning(f"Database connection test failed: {e}")
    
    def get_supabase_client(self, connection_type: ConnectionType = ConnectionType.READ_WRITE) -> Client:
        """Get Supabase client instance."""
        if connection_type == ConnectionType.READ_ONLY and self._readonly_client:
            return self._readonly_client
        
        if self._supabase_client is None:
            raise RuntimeError("DatabaseManager not initialized. Call initialize_pools() first.")
            
        return self._supabase_client
    
    @asynccontextmanager
    async def get_connection(self, connection_type: ConnectionType = ConnectionType.READ_WRITE):
        """Get database connection with automatic cleanup."""
        async with self._connection_lock:
            if self._connection_count >= self._max_connections:
                raise RuntimeError(f"Maximum connections ({self._max_connections}) exceeded")
            
            self._connection_count += 1
        
        try:
            client = self.get_supabase_client(connection_type)
            yield client
        finally:
            async with self._connection_lock:
                self._connection_count -= 1
    
    async def health_check(self) -> Dict[str, Any]:
        """Comprehensive database health check."""
        current_time = time.time()
        
        # Cache health check for 30 seconds
        if current_time - self._health_status["last_check"] < 30:
            return self._health_status
        
        try:
            start_time = time.time()
            
            # Test main connection
            client = self.get_supabase_client()
            result = client.table('raw_pages').select('id').limit(1).execute()
            
            response_time = (time.time() - start_time) * 1000
            
            self._health_status = {
                "status": "healthy",
                "response_time_ms": round(response_time, 2),
                "active_connections": self._connection_count,
                "max_connections": self._max_connections,
                "last_check": current_time,
                "supabase_url": settings.supabase_url.split('@')[-1] if '@' in settings.supabase_url else settings.supabase_url
            }
            
        except Exception as e:
            self._health_status = {
                "status": "unhealthy",
                "error": str(e),
                "active_connections": self._connection_count,
                "max_connections": self._max_connections,
                "last_check": current_time
            }
        
        return self._health_status
    
    async def execute_query(self, table: str, operation: str, **kwargs) -> Dict[str, Any]:
        """Execute database query with error handling."""
        try:
            async with self.get_connection() as client:
                table_ref = client.table(table)
                
                if operation == "select":
                    result = table_ref.select(kwargs.get("columns", "*"))
                    if "eq" in kwargs:
                        for field, value in kwargs["eq"].items():
                            result = result.eq(field, value)
                    if "limit" in kwargs:
                        result = result.limit(kwargs["limit"])
                    return result.execute()
                
                elif operation == "insert":
                    return table_ref.insert(kwargs["data"]).execute()
                
                elif operation == "update":
                    result = table_ref.update(kwargs["data"])
                    if "eq" in kwargs:
                        for field, value in kwargs["eq"].items():
                            result = result.eq(field, value)
                    return result.execute()
                
                elif operation == "delete":
                    result = table_ref.delete()
                    if "eq" in kwargs:
                        for field, value in kwargs["eq"].items():
                            result = result.eq(field, value)
                    return result.execute()
                
                else:
                    raise ValueError(f"Unsupported operation: {operation}")
                    
        except Exception as e:
            self.logger.error(f"Database query failed: {e}")
            raise


# Global database manager instance
db_manager = DatabaseManager()


# Dependency for FastAPI
async def get_db_manager() -> DatabaseManager:
    """FastAPI dependency for database manager."""
    if not db_manager._supabase_client:
        await db_manager.initialize_pools()
    return db_manager
DB_MANAGER_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 2.1.1:
Generated bash
# Проверка че файлът е създаден
ls -la core/infrastructure/database/manager.py

# Проверка на синтаксиса
python -c "from core.infrastructure.database.manager import DatabaseManager, db_manager; print('✅ DatabaseManager loaded successfully')"

# Проверка на Singleton pattern
python -c "
from core.infrastructure.database.manager import DatabaseManager
dm1 = DatabaseManager()
dm2 = DatabaseManager()
assert dm1 is dm2, 'Singleton pattern failed'
print('✅ Singleton pattern works correctly')
"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 2.1.1:
100% съм убеден че DatabaseManager е създаден правилно
Singleton pattern работи коректно
НЕ лъжа и НЕ си измислям - DatabaseManager е реален и функционален
Стъпка 2.1.2: Интеграция на DatabaseManager в health endpoints
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
# Backup на оригиналния файл
cp api/health/endpoints.py api/health/endpoints.py.backup

cat > api/health/endpoints.py << 'HEALTH_UPDATED_EOF'
"""
Health check endpoints for MCP RAG Server with database integration.
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel
from typing import Dict, Any
import time
import psutil
import sys
from datetime import datetime

from config.settings.base import settings
from core.infrastructure.database.manager import DatabaseManager, get_db_manager


router = APIRouter()


class HealthResponse(BaseModel):
    """Health check response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    system_info: Dict[str, Any]


class DetailedHealthResponse(BaseModel):
    """Detailed health check response model."""
    status: str
    timestamp: str
    version: str
    environment: str
    uptime_seconds: float
    system_info: Dict[str, Any]
    checks: Dict[str, Dict[str, Any]]


# Application start time
_start_time = time.time()


@router.get("/", response_model=HealthResponse)
async def basic_health_check():
    """Basic health check endpoint."""
    
    current_time = time.time()
    uptime = current_time - _start_time
    
    # System information
    system_info = {
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage_percent": psutil.disk_usage('/').percent,
    }
    
    return HealthResponse(
        status="healthy",
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version,
        environment=settings.environment,
        uptime_seconds=round(uptime, 2),
        system_info=system_info
    )


@router.get("/detailed", response_model=DetailedHealthResponse)
async def detailed_health_check(db: DatabaseManager = Depends(get_db_manager)):
    """Detailed health check with component status."""
    
    current_time = time.time()
    uptime = current_time - _start_time
    
    # System information
    system_info = {
        "python_version": sys.version,
        "cpu_count": psutil.cpu_count(),
        "memory_total_gb": round(psutil.virtual_memory().total / (1024**3), 2),
        "memory_available_gb": round(psutil.virtual_memory().available / (1024**3), 2),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_usage_percent": psutil.disk_usage('/').percent,
        "cpu_percent": psutil.cpu_percent(interval=1),
    }
    
    # Component checks
    checks = {
        "api": {
            "status": "healthy",
            "message": "API server is running"
        },
        "configuration": {
            "status": "healthy",
            "message": "Configuration loaded successfully"
        }
    }
    
    # Database health check
    try:
        db_health = await db.health_check()
        checks["database"] = db_health
    except Exception as e:
        checks["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Overall status
    overall_status = "healthy" if all(
        check.get("status") == "healthy" for check in checks.values()
    ) else "unhealthy"
    
    return DetailedHealthResponse(
        status=overall_status,
        timestamp=datetime.utcnow().isoformat(),
        version=settings.app_version,
        environment=settings.environment,
        uptime_seconds=round(uptime, 2),
        system_info=system_info,
        checks=checks
    )


@router.get("/database")
async def database_health_check(db: DatabaseManager = Depends(get_db_manager)):
    """Dedicated database health check."""
    return await db.health_check()


@router.get("/ready")
async def readiness_check(db: DatabaseManager = Depends(get_db_manager)):
    """Readiness check for Kubernetes."""
    try:
        db_health = await db.health_check()
        if db_health.get("status") == "healthy":
            return {"status": "ready", "timestamp": datetime.utcnow().isoformat()}
        else:
            raise HTTPException(status_code=503, detail="Database not ready")
    except Exception as e:
        raise HTTPException(status_code=503, detail=f"Not ready: {str(e)}")


@router.get("/live")
async def liveness_check():
    """Liveness check for Kubernetes."""
    return {"status": "alive", "timestamp": datetime.utcnow().isoformat()}
HEALTH_UPDATED_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 2.1.2:
Generated bash
# Проверка че файлът е обновен
ls -la api/health/endpoints.py

# Проверка на синтаксиса
python -c "from api.health.endpoints import router; print('✅ Updated health endpoints loaded successfully')"

# Тест на сървъра с нови endpoints
python main.py &
SERVER_PID=$!
sleep 5

# Тест на database health endpoint
curl -s http://localhost:8000/health/database | head -50
curl -s http://localhost:8000/health/detailed | head -50

kill $SERVER_PID 2>/dev/null || true
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 2.1.2:
100% съм убеден че health endpoints са обновени правилно
Database health check работи без грешки
НЕ лъжа и НЕ си измислям - интеграцията работи
ЗАДАЧА 2.2: Hybrid Crawler System
Стъпка 2.2.1: Създаване на базов crawler interface
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > crawlers/base_crawler.py << 'BASE_CRAWLER_EOF'
"""
Base crawler interface and common functionality.
"""

from abc import ABC, abstractmethod
import logging
import time
import random
from typing import Dict, Any, List, Optional
from dataclasses import dataclass
from enum import Enum

from prometheus_client import Counter, Histogram, Gauge


# Metrics
PAGES_CRAWLED = Counter('pages_crawled_total', 'Total pages crawled', ['crawler_type', 'status'])
CRAWL_DURATION = Histogram('crawl_duration_seconds', 'Time taken to crawl', ['crawler_type'])
ACTIVE_CRAWLERS = Gauge('active_crawlers', 'Number of active crawlers', ['crawler_type'])


class CrawlStatus(Enum):
    """Crawl status enumeration."""
    SUCCESS = "success"
    FAILED = "failed"
    TIMEOUT = "timeout"
    BLOCKED = "blocked"


@dataclass
class CrawlResult:
    """Crawl result data structure."""
    url: str
    success: bool
    status_code: Optional[int] = None
    content: Optional[str] = None
    html_content: Optional[str] = None
    title: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    crawl_time: Optional[float] = None
    crawler_type: Optional[str] = None


class BaseCrawler(ABC):
    """Abstract base crawler class."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.logger = logging.getLogger(self.__class__.__name__)
        self.crawler_type = self.__class__.__name__
        
        # User agents for rotation
        self.user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0'
        ]
    
    def get_random_user_agent(self) -> str:
        """Get random user agent for requests."""
        return random.choice(self.user_agents)
    
    def add_delay(self):
        """Add random delay between requests."""
        min_delay = self.config.get('min_delay', 1.0)
        max_delay = self.config.get('max_delay', 3.0)
        delay = random.uniform(min_delay, max_delay)
        time.sleep(delay)
    
    @abstractmethod
    async def crawl_url(self, url: str) -> CrawlResult:
        """Crawl a single URL."""
        pass
    
    @abstractmethod
    async def crawl_urls(self, urls: List[str]) -> List[CrawlResult]:
        """Crawl multiple URLs."""
        pass
    
    async def run_with_metrics(self, url: str) -> CrawlResult:
        """Run crawler with metrics collection."""
        ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).inc()
        
        start_time = time.time()
        try:
            result = await self.crawl_url(url)
            
            # Record metrics
            status = CrawlStatus.SUCCESS.value if result.success else CrawlStatus.FAILED.value
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status=status).inc()
            
            return result
            
        except Exception as e:
            self.logger.error(f"Crawler error for {url}: {e}")
            PAGES_CRAWLED.labels(crawler_type=self.crawler_type, status=CrawlStatus.FAILED.value).inc()
            
            return CrawlResult(
                url=url,
                success=False,
                error=str(e),
                crawler_type=self.crawler_type
            )
        finally:
            duration = time.time() - start_time
            CRAWL_DURATION.labels(crawler_type=self.crawler_type).observe(duration)
            ACTIVE_CRAWLERS.labels(crawler_type=self.crawler_type).dec()
    
    def should_crawl_url(self, url: str) -> bool:
        """Check if URL should be crawled based on filters."""
        # Basic URL validation
        if not url or not url.startswith(('http://', 'https://')):
            return False
        
        # Check against exclude patterns
        exclude_patterns = self.config.get('exclude_patterns', [])
        for pattern in exclude_patterns:
            if pattern in url:
                return False
        
        # Check against include patterns
        include_patterns = self.config.get('include_patterns', [])
        if include_patterns:
            return any(pattern in url for pattern in include_patterns)
        
        return True
BASE_CRAWLER_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 2.2.1:
Generated bash
# Проверка че файлът е създаден
ls -la crawlers/base_crawler.py

# Проверка на синтаксиса
python -c "from crawlers.base_crawler import BaseCrawler, CrawlResult, CrawlStatus; print('✅ BaseCrawler loaded successfully')"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 2.2.1:
100% съм убеден че BaseCrawler е създаден правилно
Всички класове и enums се импортират без грешки
НЕ лъжа и НЕ си измислям - базовият crawler работи
Стъпка 2.2.2: Създаване на AioHttp Crawler
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > crawlers/html/aiohttp_crawler.py << 'AIOHTTP_CRAWLER_EOF'
"""
AioHttp-based crawler for simple HTML pages.
Fast and lightweight for basic crawling needs.
"""

import asyncio
import aiohttp
from bs4 import BeautifulSoup
from typing import Dict, Any, List
from urllib.parse import urljoin, urlparse
import time

from crawlers.base_crawler import BaseCrawler, CrawlResult


class AioHttpCrawler(BaseCrawler):
    """AioHttp-based crawler for simple HTML pages."""
    
    def __init__(self, config: Dict[str, Any]):
        super().__init__(config)
        self.session: Optional[aiohttp.ClientSession] = None
        self.timeout = aiohttp.ClientTimeout(
            total=config.get('timeout', 30),
            connect=config.get('connect_timeout', 10)
        )
        self.max_concurrent = config.get('max_concurrent', 10)
        self.semaphore = asyncio.Semaphore(self.max_concurrent)
    
    async def __aenter__(self):
        """Async context manager entry."""
        connector = aiohttp.TCPConnector(
            limit=self.max_concurrent,
            limit_per_host=5,
            ttl_dns_cache=300,
            use_dns_cache=True,
        )
        
        self.session = aiohttp.ClientSession(
            timeout=self.timeout,
            connector=connector,
            headers={'User-Agent': self.get_random_user_agent()}
        )
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        if self.session:
            await self.session.close()
    
    async def crawl_url(self, url: str) -> CrawlResult:
        """Crawl a single URL with aiohttp."""
        if not self.should_crawl_url(url):
            return CrawlResult(
                url=url,
                success=False,
                error="URL filtered out",
                crawler_type=self.crawler_type
            )
        
        start_time = time.time()
        
        async with self.semaphore:
            try:
                if not self.session:
                    raise RuntimeError("AioHttpCrawler must be used as async context manager")
                
                self.logger.info(f"Crawling URL: {url}")
                
                async with self.session.get(url) as response:
                    if response.status == 200:
                        html_content = await response.text()
                        
                        # Parse HTML with BeautifulSoup
                        soup = BeautifulSoup(html_content, 'html.parser')
                        
                        # Extract title
                        title = soup.title.string.strip() if soup.title else ""
                        
                        # Remove script and style tags
                        for script in soup(["script", "style"]):
                            script.decompose()
                        
                        # Extract clean text
                        text_content = soup.get_text(separator=' ', strip=True)
                        
                        # Extract metadata
                        metadata = {
                            'content_type': response.headers.get('Content-Type', ''),
                            'content_length': len(html_content),
                            'server': response.headers.get('Server', ''),
                            'last_modified': response.headers.get('Last-Modified', ''),
                        }
                        
                        crawl_time = time.time() - start_time
                        
                        return CrawlResult(
                            url=url,
                            success=True,
                            status_code=response.status,
                            content=text_content,
                            html_content=html_content,
                            title=title,
                            metadata=metadata,
                            crawl_time=crawl_time,
                            crawler_type=self.crawler_type
                        )
                    else:
                        return CrawlResult(
                            url=url,
                            success=False,
                            status_code=response.status,
                            error=f'HTTP {response.status}',
                            crawl_time=time.time() - start_time,
                            crawler_type=self.crawler_type
                        )
            
            except asyncio.TimeoutError:
                return CrawlResult(
                    url=url,
                    success=False,
                    error="Request timeout",
                    crawl_time=time.time() - start_time,
                    crawler_type=self.crawler_type
                )
            except Exception as e:
                return CrawlResult(
                    url=url,
                    success=False,
                    error=str(e),
                    crawl_time=time.time() - start_time,
                    crawler_type=self.crawler_type
                )
    
    async def crawl_urls(self, urls: List[str]) -> List[CrawlResult]:
        """Crawl multiple URLs concurrently."""
        self.logger.info(f"Starting batch crawl of {len(urls)} URLs")
        
        # Create tasks for concurrent crawling
        tasks = [self.crawl_url(url) for url in urls]
        
        # Execute with progress tracking
        results = []
        for i, task in enumerate(asyncio.as_completed(tasks)):
            result = await task
            results.append(result)
            
            if (i + 1) % 10 == 0:
                self.logger.info(f"Completed {i + 1}/{len(urls)} URLs")
        
        # Log summary
        successful = sum(1 for r in results if r.success)
        failed = len(results) - successful
        self.logger.info(f"Batch crawl completed: {successful} successful, {failed} failed")
        
        return results
AIOHTTP_CRAWLER_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 2.2.2:```bash
Проверка че файлът е създаден
ls -la crawlers/html/aiohttp_crawler.py
Проверка на синтаксиса
python -c "from crawlers.html.aiohttp_crawler import AioHttpCrawler; print('✅ AioHttpCrawler loaded successfully')"
Generated code
**✅ ПОТВЪРЖДЕНИЕ 2.2.2:**
- [ ] **100% съм убеден че AioHttpCrawler е създаден правилно**
- [ ] **Класът се импортира без грешки**
- [ ] **НЕ лъжа и НЕ си измислям - AioHttpCrawler работи**

#### Стъпка 2.2.3: Тест на AioHttpCrawler
**ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:**

```bash
cat > tests/integration/test_aiohttp_crawler.py << 'TEST_CRAWLER_EOF'
"""
Integration tests for AioHttpCrawler.
"""

import pytest
import asyncio
from crawlers.html.aiohttp_crawler import AioHttpCrawler


@pytest.fixture
def crawler_config():
    """Crawler configuration for testing."""
    return {
        'timeout': 10,
        'connect_timeout': 5,
        'max_concurrent': 3,
        'min_delay': 0.1,
        'max_delay': 0.2
    }


@pytest.fixture
def aiohttp_crawler(crawler_config):
    """AioHttpCrawler instance for testing."""
    return AioHttpCrawler(crawler_config)


@pytest.mark.asyncio
async def test_crawl_single_url(aiohttp_crawler):
    """Test crawling a single URL."""
    test_url = "https://httpbin.org/html"
    
    async with aiohttp_crawler:
        result = await aiohttp_crawler.crawl_url(test_url)
    
    assert result.success == True
    assert result.url == test_url
    assert result.status_code == 200
    assert result.content is not None
    assert result.html_content is not None
    assert result.title is not None
    assert result.crawler_type == "AioHttpCrawler"
    assert result.crawl_time is not None
    assert result.crawl_time > 0


@pytest.mark.asyncio
async def test_crawl_invalid_url(aiohttp_crawler):
    """Test crawling an invalid URL."""
    test_url = "https://this-domain-does-not-exist-12345.com"
    
    async with aiohttp_crawler:
        result = await aiohttp_crawler.crawl_url(test_url)
    
    assert result.success == False
    assert result.url == test_url
    assert result.error is not None
    assert result.crawler_type == "AioHttpCrawler"


@pytest.mark.asyncio
async def test_crawl_multiple_urls(aiohttp_crawler):
    """Test crawling multiple URLs."""
    test_urls = [
        "https://httpbin.org/html",
        "https://httpbin.org/json",
        "https://httpbin.org/xml"
    ]
    
    async with aiohttp_crawler:
        results = await aiohttp_crawler.crawl_urls(test_urls)
    
    assert len(results) == len(test_urls)
    
    # Check that at least some requests succeeded
    successful_results = [r for r in results if r.success]
    assert len(successful_results) > 0
    
    # Check result structure
    for result in results:
        assert result.url in test_urls
        assert result.crawler_type == "AioHttpCrawler"
        assert result.crawl_time is not None


@pytest.mark.asyncio
async def test_url_filtering(aiohttp_crawler):
    """Test URL filtering functionality."""
    # Test invalid URL
    invalid_url = "not-a-url"
    result = await aiohttp_crawler.crawl_url(invalid_url)
    assert result.success == False
    assert "filtered out" in result.error


def test_user_agent_rotation(aiohttp_crawler):
    """Test user agent rotation."""
    ua1 = aiohttp_crawler.get_random_user_agent()
    ua2 = aiohttp_crawler.get_random_user_agent()
    
    assert ua1 in aiohttp_crawler.user_agents
    assert ua2 in aiohttp_crawler.user_agents
    # Note: ua1 and ua2 might be the same due to randomness


if __name__ == "__main__":
    # Simple test runner
    async def run_tests():
        config = {
            'timeout': 10,
            'connect_timeout': 5,
            'max_concurrent': 3,
            'min_delay': 0.1,
            'max_delay': 0.2
        }
        
        crawler = AioHttpCrawler(config)
        
        print("Testing single URL crawl...")
        async with crawler:
            result = await crawler.crawl_url("https://httpbin.org/html")
            print(f"Result: {result.success}, Status: {result.status_code}")
        
        print("Testing multiple URL crawl...")
        async with crawler:
            results = await crawler.crawl_urls([
                "https://httpbin.org/html",
                "https://httpbin.org/json"
            ])
            print(f"Results: {len(results)} URLs crawled")
            for r in results:
                print(f"  {r.url}: {r.success}")
    
    asyncio.run(run_tests())
TEST_CRAWLER_EOF
Use code with caution.
ВАЛИДАЦИЯ 2.2.3:
Generated bash
# Проверка че тестовият файл е създаден
ls -la tests/integration/test_aiohttp_crawler.py

# Стартиране на тестовете
cd tests/integration
python test_aiohttp_crawler.py

# Проверка с pytest (ако е инсталиран)
pytest test_aiohttp_crawler.py -v || echo "pytest not available, manual test completed"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 2.2.3:
100% съм убеден че тестовете на AioHttpCrawler работят
Тестовете минават успешно и показват реални резултати
НЕ лъжа и НЕ си измислям - crawler-ът работи реално
ЗАДАЧА 2.3: Monitoring и Metrics
Стъпка 2.3.1: Prometheus metrics setup
ТОЧНИ КОМАНДИ ЗА ИЗПЪЛНЕНИЕ:
Generated bash
cat > monitoring/prometheus/metrics.py << 'METRICS_EOF'
"""
Prometheus metrics for MCP RAG Server.
"""

from prometheus_client import Counter, Histogram, Gauge, Info, start_http_server
import logging
import psutil
import time
from typing import Dict, Any

from config.settings.base import settings


# Application metrics
APP_INFO = Info('mcp_rag_server_info', 'Application information')
APP_INFO.info({
    'version': settings.app_version,
    'environment': settings.environment
})

# API metrics
HTTP_REQUESTS_TOTAL = Counter(
    'http_requests_total',
    'Total HTTP requests',
    ['method', 'endpoint', 'status_code']
)

HTTP_REQUEST_DURATION = Histogram(
    'http_request_duration_seconds',
    'HTTP request duration',
    ['method', 'endpoint']
)

# Crawler metrics
PAGES_CRAWLED_TOTAL = Counter(
    'pages_crawled_total',
    'Total pages crawled',
    ['crawler_type', 'status']
)

CRAWL_DURATION = Histogram(
    'crawl_duration_seconds',
    'Time taken to crawl a page',
    ['crawler_type']
)

ACTIVE_CRAWLERS = Gauge(
    'active_crawlers',
    'Number of active crawlers',
    ['crawler_type']
)

# Database metrics
DATABASE_CONNECTIONS_ACTIVE = Gauge(
    'database_connections_active',
    'Number of active database connections'
)

DATABASE_QUERY_DURATION = Histogram(
    'database_query_duration_seconds',
    'Database query duration',
    ['operation', 'table']
)

DATABASE_QUERIES_TOTAL = Counter(
    'database_queries_total',
    'Total database queries',
    ['operation', 'table', 'status']
)

# Embedding metrics
EMBEDDINGS_GENERATED_TOTAL = Counter(
    'embeddings_generated_total',
    'Total embeddings generated',
    ['model', 'status']
)

EMBEDDING_GENERATION_DURATION = Histogram(
    'embedding_generation_duration_seconds',
    'Time taken to generate embeddings',
    ['model']
)

EMBEDDING_CACHE_HITS = Counter(
    'embedding_cache_hits_total',
    'Embedding cache hits'
)

EMBEDDING_CACHE_MISSES = Counter(
    'embedding_cache_misses_total',
    'Embedding cache misses'
)

# System metrics
SYSTEM_CPU_USAGE = Gauge('system_cpu_usage_percent', 'System CPU usage percentage')
SYSTEM_MEMORY_USAGE = Gauge('system_memory_usage_percent', 'System memory usage percentage')
SYSTEM_DISK_USAGE = Gauge('system_disk_usage_percent', 'System disk usage percentage')

# Search metrics
SEARCH_REQUESTS_TOTAL = Counter(
    'search_requests_total',
    'Total search requests',
    ['endpoint', 'status']
)

SEARCH_DURATION = Histogram(
    'search_duration_seconds',
    'Search request duration',
    ['search_type']
)

SEARCH_RESULTS_COUNT = Histogram(
    'search_results_count',
    'Number of search results returned'
)


class MetricsCollector:
    """Metrics collector for system monitoring."""
    
    def __init__(self):
        self.logger = logging.getLogger(self.__class__.__name__)
        self._collecting = False
    
    def start_collection(self):
        """Start metrics collection."""
        if not self._collecting:
            self._collecting = True
            self.logger.info("Starting metrics collection")
            # Start system metrics collection in background
            import asyncio
            asyncio.create_task(self._collect_system_metrics())
    
    def stop_collection(self):
        """Stop metrics collection."""
        self._collecting = False
        self.logger.info("Stopping metrics collection")
    
    async def _collect_system_metrics(self):
        """Collect system metrics periodically."""
        while self._collecting:
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=1)
                SYSTEM_CPU_USAGE.set(cpu_percent)
                
                # Memory usage
                memory = psutil.virtual_memory()
                SYSTEM_MEMORY_USAGE.set(memory.percent)
                
                # Disk usage
                disk = psutil.disk_usage('/')
                SYSTEM_DISK_USAGE.set(disk.percent)
                
                # Wait before next collection
                await asyncio.sleep(30)  # Collect every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error collecting system metrics: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    def record_http_request(self, method: str, endpoint: str, status_code: int, duration: float):
        """Record HTTP request metrics."""
        HTTP_REQUESTS_TOTAL.labels(
            method=method,
            endpoint=endpoint,
            status_code=str(status_code)
        ).inc()
        
        HTTP_REQUEST_DURATION.labels(
            method=method,
            endpoint=endpoint
        ).observe(duration)
    
    def record_crawl_result(self, crawler_type: str, success: bool, duration: float):
        """Record crawl result metrics."""
        status = "success" if success else "failed"
        PAGES_CRAWLED_TOTAL.labels(
            crawler_type=crawler_type,
            status=status
        ).inc()
        
        CRAWL_DURATION.labels(crawler_type=crawler_type).observe(duration)
    
    def record_database_query(self, operation: str, table: str, success: bool, duration: float):
        """Record database query metrics."""
        status = "success" if success else "failed"
        DATABASE_QUERIES_TOTAL.labels(
            operation=operation,
            table=table,
            status=status
        ).inc()
        
        DATABASE_QUERY_DURATION.labels(
            operation=operation,
            table=table
        ).observe(duration)
    
    def record_embedding_generation(self, model: str, success: bool, duration: float):
        """Record embedding generation metrics."""
        status = "success" if success else "failed"
        EMBEDDINGS_GENERATED_TOTAL.labels(
            model=model,
            status=status
        ).inc()
        
        EMBEDDING_GENERATION_DURATION.labels(model=model).observe(duration)
    
    def record_search_request(self, endpoint: str, success: bool, duration: float, results_count: int):
        """Record search request metrics."""
        status = "success" if success else "failed"
        SEARCH_REQUESTS_TOTAL.labels(
            endpoint=endpoint,
            status=status
        ).inc()
        
        SEARCH_DURATION.labels(search_type=endpoint).observe(duration)
        SEARCH_RESULTS_COUNT.observe(results_count)


# Global metrics collector
metrics_collector = MetricsCollector()


def start_metrics_server():
    """Start Prometheus metrics server."""
    try:
        start_http_server(settings.prometheus_port)
        logging.getLogger(__name__).info(f"Prometheus metrics server started on port {settings.prometheus_port}")
        metrics_collector.start_collection()
    except Exception as e:
        logging.getLogger(__name__).error(f"Failed to start metrics server: {e}")
METRICS_EOF
Use code with caution.
Bash
ВАЛИДАЦИЯ 2.3.1:
Generated bash
# Проверка че файлът е създаден
ls -la monitoring/prometheus/metrics.py

# Проверка на синтаксиса
python -c "from monitoring/prometheus/metrics import metrics_collector, start_metrics_server; print('✅ Metrics module loaded successfully')"

# Тест на metrics server
python -c "
from monitoring.prometheus.metrics import start_metrics_server
import time
import requests

# Start metrics server
start_metrics_server()
time.sleep(2)

# Test metrics endpoint
try:
    response = requests.get('http://localhost:9090/metrics', timeout=5)
    if response.status_code == 200:
        print('✅ Metrics server responding')
        print(f'Metrics content length: {len(response.text)}')
    else:
        print(f'❌ Metrics server error: {response.status_code}')
except Exception as e:
    print(f'❌ Metrics server test failed: {e}')
"
Use code with caution.
Bash
✅ ПОТВЪРЖДЕНИЕ 2.3.1:
100% съм убеден че Prometheus metrics са настроени правилно
Metrics server стартира и отговаря на заявки
НЕ лъжа и НЕ си измислям - metrics системата работи
✅ ФИНАЛНА ВАЛИДАЦИЯ НА ФАЗА 2
Generated bash
echo "=== ФИНАЛНА ПРОВЕРКА НА ФАЗА 2 ==="

echo "1. Проверка на DatabaseManager:"
python -c "
from core.infrastructure.database.manager import DatabaseManager
dm = DatabaseManager()
print('✅ DatabaseManager singleton works')
"

echo ""
echo "2. Проверка на AioHttpCrawler:"
python -c "
from crawlers.html.aiohttp_crawler import AioHttpCrawler
config = {'timeout': 10, 'max_concurrent': 5}
crawler = AioHttpCrawler(config)
print('✅ AioHttpCrawler initializes correctly')
"

echo ""
echo "3. Проверка на Metrics:"
python -c "
from monitoring.prometheus.metrics import metrics_collector
print('✅ Metrics collector available')
"

echo ""
echo "4. Тест на интеграцията:"
python main.py &
SERVER_PID=$!
sleep 5

# Test health endpoints
curl -s http://localhost:8000/health/database | head -20
curl -s http://localhost:8000/health/detailed | head -20

kill $SERVER_PID 2>/dev/null || true

echo ""
echo "5. Git commit на промените:"
git add .
git commit -m "Phase 2: Production Ready Components

- Implemented DatabaseManager with connection pooling
- Created AioHttpCrawler with async support
- Added comprehensive Prometheus metrics
- Updated health endpoints with database checks
- Added integration tests for crawlers"
Use code with caution.
Bash
✅ ФИНАЛНО ПОТВЪРЖДЕНИЕ НА ФАЗА 2:
100% съм убеден че цялата фаза 2 е завършена успешно
DatabaseManager работи с connection pooling
AioHttpCrawler работи асинхронно
Prometheus metrics са активни
Health endpoints включват database проверки
НЕ лъжа и НЕ си измислям - всичко работи реално
📝 CHECKPOINT ФАЗА 2 ЗАВЪРШЕНА ✅
Какво е направено:
✅ Имплементиран DatabaseManager с Singleton pattern и connection pooling
✅ Създаден AioHttpCrawler за асинхронен HTML crawling
✅ Добавени comprehensive Prometheus metrics
✅ Обновени health endpoints с database integration
✅ Създадени integration тестове за crawlers
✅ Git commit с всички промени
Следваща стъпка: Фаза 3 - Advanced Features (Hybrid search, Security, Optimization)