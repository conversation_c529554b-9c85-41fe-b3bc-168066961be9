"""Chunk repository interface for the MCP RAG Server."""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any, Tuple
from datetime import datetime

from ..entities import Chunk


class ChunkRepository(ABC):
    """Abstract chunk repository interface."""
    
    @abstractmethod
    async def create(self, chunk: Chunk) -> Chunk:
        """Create a new chunk."""
        pass
    
    @abstractmethod
    async def get_by_id(self, chunk_id: str) -> Optional[Chunk]:
        """Get chunk by ID."""
        pass
    
    @abstractmethod
    async def update(self, chunk: Chunk) -> Chunk:
        """Update an existing chunk."""
        pass
    
    @abstractmethod
    async def delete(self, chunk_id: str) -> bool:
        """Delete a chunk."""
        pass
    
    @abstractmethod
    async def get_chunks_by_document(
        self,
        document_id: str,
        limit: int = 100,
        offset: int = 0,
    ) -> List[Chunk]:
        """Get all chunks for a specific document."""
        pass
    
    @abstractmethod
    async def count_chunks_by_document(self, document_id: str) -> int:
        """Count chunks for a specific document."""
        pass
    
    @abstractmethod
    async def delete_chunks_by_document(self, document_id: str) -> int:
        """Delete all chunks for a specific document."""
        pass
    
    @abstractmethod
    async def bulk_create(self, chunks: List[Chunk]) -> List[Chunk]:
        """Create multiple chunks in bulk."""
        pass
    
    @abstractmethod
    async def bulk_update_embeddings(
        self,
        chunk_embeddings: List[Tuple[str, List[float], str]],  # (chunk_id, embedding, model)
    ) -> int:
        """Update embeddings for multiple chunks."""
        pass
    
    @abstractmethod
    async def search_by_embedding(
        self,
        embedding: List[float],
        limit: int = 10,
        similarity_threshold: float = 0.7,
        document_ids: Optional[List[str]] = None,
    ) -> List[Tuple[Chunk, float]]:
        """Search chunks by embedding similarity."""
        pass
    
    @abstractmethod
    async def search_by_text(
        self,
        query: str,
        limit: int = 10,
        document_ids: Optional[List[str]] = None,
    ) -> List[Chunk]:
        """Search chunks by text content."""
        pass
    
    @abstractmethod
    async def get_chunks_without_embeddings(
        self,
        limit: int = 100,
        embedding_model: Optional[str] = None,
    ) -> List[Chunk]:
        """Get chunks that don't have embeddings."""
        pass
    
    @abstractmethod
    async def get_chunks_by_embedding_model(self, model: str) -> List[Chunk]:
        """Get chunks processed with a specific embedding model."""
        pass
    
    @abstractmethod
    async def get_similar_chunks(
        self,
        chunk_id: str,
        limit: int = 10,
        similarity_threshold: float = 0.8,
    ) -> List[Tuple[Chunk, float]]:
        """Get chunks similar to a specific chunk."""
        pass
    
    @abstractmethod
    async def get_chunks_by_content_length(
        self,
        min_length: int = 0,
        max_length: int = 10000,
        limit: int = 100,
    ) -> List[Chunk]:
        """Get chunks by content length range."""
        pass
    
    @abstractmethod
    async def get_chunks_created_after(self, date: datetime) -> List[Chunk]:
        """Get chunks created after a specific date."""
        pass
    
    @abstractmethod
    async def get_random_chunks(self, limit: int = 10) -> List[Chunk]:
        """Get random chunks for sampling."""
        pass
    
    @abstractmethod
    async def update_chunk_scores(
        self,
        chunk_scores: List[Tuple[str, float, float]],  # (chunk_id, relevance_score, readability_score)
    ) -> int:
        """Update quality scores for multiple chunks."""
        pass
    
    @abstractmethod
    async def get_statistics(self) -> Dict[str, Any]:
        """Get chunk repository statistics."""
        pass
    
    @abstractmethod
    async def cleanup_orphaned_chunks(self) -> int:
        """Clean up chunks that don't have corresponding documents."""
        pass
    
    @abstractmethod
    async def get_chunk_distribution_by_document(self) -> Dict[str, int]:
        """Get chunk count distribution by document."""
        pass
    
    @abstractmethod
    async def reindex_embeddings(self, embedding_model: str) -> int:
        """Reindex all embeddings for a specific model."""
        pass
