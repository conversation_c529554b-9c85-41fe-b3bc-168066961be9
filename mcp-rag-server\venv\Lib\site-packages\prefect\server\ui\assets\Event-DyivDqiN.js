import{d as f,e as b,V as x,W as c,g as r,aC as C,aD as y,K as E,i as I,c as h,a as k,n as e,o as D,j as n,k as s,aE as F,z as V,aF as g}from"./index-ei-kaitd.js";import{u as w}from"./usePageTitle-LeBMnqrg.js";const q=f({__name:"Event",async setup(L){let a,o;const u=b(),v=x(),l=c("eventDate"),_=c("eventId"),m=r(()=>C(l.value)),d=y({startDate:m,eventId:[_.value]}),t=([a,o]=E(()=>u.events.getFirstEvent(d.value)),a=await a,o(),a),p=r(()=>[{text:"Event Feed",to:v.events()},{text:t.eventLabel}]);return w(`Event: ${t.eventLabel}`),(B,N)=>{const i=I("p-layout-default");return e(t)?(D(),h(i,{key:0,class:"event"},{header:n(()=>[s(e(V),{crumbs:p.value},{actions:n(()=>[s(e(g),{event:e(t)},null,8,["event"])]),_:1},8,["crumbs"])]),default:n(()=>[s(e(F),{event:e(t)},null,8,["event"])]),_:1})):k("",!0)}}});export{q as default};
//# sourceMappingURL=Event-DyivDqiN.js.map
